# Flutter 环境配置详细文档

**项目**: MasteryOS (万时通) - 智能技能发展和追踪系统  
**文档版本**: 1.0  
**创建日期**: 2025年1月21日  
**最后更新**: 2025年1月21日  
**Flutter版本**: 3.32.1  
**Dart版本**: 3.8.1  

---

## 📋 目录

1. [项目概述](#项目概述)
2. [技术架构](#技术架构)
3. [环境要求](#环境要求)
4. [依赖包详解](#依赖包详解)
5. [开发工具配置](#开发工具配置)
6. [环境变量配置](#环境变量配置)
7. [国际化配置](#国际化配置)
8. [启动指南](#启动指南)
9. [故障排除](#故障排除)

---

## 🎯 项目概述

MasteryOS是基于"一万小时定律"的智能技能发展和追踪系统，采用现代化Flutter架构，包含：

- **Admin Web**: Flutter Web管理后台 (端口: 8081)
- **Mobile App**: Flutter移动应用 (端口: 8080)
- **Unified BFF**: NestJS后端服务 (端口: 3102)

## 🏗️ 技术架构

### 核心架构模式
- **状态管理**: BLoC Pattern (flutter_bloc)
- **路由管理**: Declarative Routing (go_router)
- **网络请求**: HTTP Client (dio + retrofit)
- **依赖注入**: Service Locator (get_it + injectable)
- **代码生成**: Build Runner (json_serializable + freezed)

### 平台支持
- ✅ Web (Chrome, Safari, Firefox, Edge)
- ✅ iOS (iOS 12.0+)
- ✅ Android (API 21+)
- ✅ macOS (10.14+)
- ✅ Windows (Windows 10+)
- ✅ Linux (64-bit)

---

## 💻 环境要求

### 开发工具
- **Flutter SDK**: 3.32.1+ (Stable Channel)
- **Dart SDK**: 3.8.1+ (包含在Flutter中)
- **Node.js**: 22.18.0 LTS (通过NVM管理)
- **pnpm**: 10.14.0+ (包管理器)
- **Docker**: 20.0+ (数据库容器化)
- **Git**: 2.30+ (版本控制)

### IDE推荐
- **VS Code**: + Flutter/Dart插件
- **Android Studio**: + Flutter插件
- **IntelliJ IDEA**: + Flutter插件

### 系统要求
- **macOS**: 10.14 Mojave或更高
- **Windows**: Windows 10 64位或更高
- **Linux**: 64位发行版

---

## 📦 依赖包详解

### Admin Web 应用 (apps/admin-web/pubspec.yaml)

#### 核心框架依赖
```yaml
dependencies:
  flutter:
    sdk: flutter
    # Flutter框架核心，提供UI组件和渲染引擎
```

#### 状态管理相关
```yaml
  # BLoC状态管理 - 企业级应用首选
  flutter_bloc: ^9.1.1           # Flutter BLoC集成包，提供BlocProvider、BlocBuilder等Widget
  bloc: ^9.0.0                   # BLoC核心库，实现业务逻辑分离和状态管理
  equatable: ^2.0.7              # 值对象比较工具，简化BLoC状态比较逻辑
```

#### 路由管理
```yaml
  # 声明式路由 - Flutter官方推荐
  go_router: ^16.1.0             # 声明式路由框架，支持深度链接、Web URL、嵌套路由
```

#### 网络请求
```yaml
  # HTTP客户端套件
  dio: ^5.8.0                    # 强大的HTTP客户端，支持拦截器、缓存、重试等
  dio_web_adapter: ^2.1.1        # Dio Web平台适配器，解决CORS和Web兼容性问题
```

#### 依赖注入
```yaml
  # 服务定位器模式 - 现代化DI方案
  get_it: ^8.0.2                 # 轻量级服务定位器，支持单例、工厂模式
  injectable: ^2.5.0             # 基于注解的依赖注入代码生成器
```

#### UI组件库
```yaml
  # 数据展示组件
  data_table_2: ^2.6.0           # 高级数据表格组件，支持排序、分页、固定列
  fl_chart: ^1.0.0               # 美观的图表库，支持线图、柱图、饼图等
```

#### 工具类库
```yaml
  # 国际化和格式化
  intl: ^0.20.2                  # 国际化支持，日期时间格式化、数字格式化
  
  # 本地存储
  shared_preferences: ^2.5.3     # 键值对本地存储，用于用户偏好设置
  
  # JWT处理
  jwt_decoder: ^2.0.1            # JWT令牌解码，用于身份验证
  
  # JSON序列化
  json_annotation: ^4.9.0        # JSON序列化注解，配合code generator使用
```

#### 错误监控和性能
```yaml
  # 生产环境监控
  sentry_flutter: ^8.13.0        # Sentry错误追踪和性能监控，自动捕获异常
```

#### 环境配置
```yaml
  # 环境变量管理
  flutter_dotenv: ^5.2.1         # .env文件支持，管理不同环境的配置
```

#### Web平台优化
```yaml
  # Web特性支持
  web: ^1.1.1                    # Web平台API支持
  logging: ^1.3.0                # 日志记录框架，支持不同级别和输出
```

#### 开发工具依赖
```yaml
dev_dependencies:
  flutter_test:
    sdk: flutter
    # Flutter测试框架
    
  # 代码质量
  very_good_analysis: ^7.0.0     # 严格的Dart代码规范，比flutter_lints更严格
  
  # 代码生成工具链
  build_runner: ^2.6.0           # 代码生成器运行器，协调所有代码生成任务
  json_serializable: ^6.10.0     # JSON序列化代码生成器
  source_gen: ^3.0.0             # 代码生成基础库
  injectable_generator: ^2.8.1   # 依赖注入代码生成器
  freezed: ^3.2.0                # 不可变数据类和联合类型生成器
  freezed_annotation: ^3.1.0     # Freezed注解库
```

### Mobile 应用 (apps/mobile/pubspec.yaml)

#### UI和导航增强
```yaml
dependencies:
  # 基础UI
  cupertino_icons: ^1.0.8        # iOS风格图标库
  
  # 路由 (与Admin Web一致)
  go_router: ^16.1.0             # 统一的路由方案
```

#### 状态管理 (与Admin Web一致)
```yaml
  # BLoC生态系统
  flutter_bloc: ^9.1.1           # BLoC Flutter集成
  bloc: ^9.0.0                   # BLoC核心
  equatable: ^2.0.7              # 对象比较工具
```

#### 网络和API
```yaml
  # HTTP客户端套件
  dio: ^5.8.0                    # HTTP客户端核心
  retrofit: ^4.5.0               # 类型安全的REST API客户端代码生成器
  json_annotation: ^4.9.0        # JSON序列化注解
  connectivity_plus: ^6.1.1      # 网络连接状态检测，支持WiFi/移动网络/离线状态
```

#### 本地存储方案
```yaml
  # 多层级存储策略
  shared_preferences: ^2.5.3     # 简单键值对存储 (用户偏好)
  flutter_secure_storage: ^9.2.2 # 安全存储 (Token、密码)
  hive_flutter: ^1.1.0          # 高性能NoSQL数据库 (离线数据)
```

#### 依赖注入系统
```yaml
  # 服务定位器 + 代码生成
  get_it: ^8.0.2                 # 服务定位器核心
  injectable: ^2.5.0             # 依赖注入注解
  # provider已被get_it替代，但保留以支持flutter_bloc内部使用
```

#### 工具类库
```yaml
  # 通用工具
  intl: ^0.20.2                  # 国际化 (与Admin Web保持一致)
  uuid: ^4.5.1                   # UUID生成器，用于唯一标识符
  path_provider: ^2.1.5          # 系统路径获取 (文档、缓存目录)
  logging: ^1.3.0                # 日志记录
  
  # 文件操作
  file_picker: ^10.2.1           # 文件选择器，支持多种文件类型
  path: ^1.9.1                   # 路径操作工具
  image_picker: ^1.1.2           # 图片选择器，支持相机和相册
```

#### PDF和文档处理
```yaml
  # 文档查看器
  syncfusion_flutter_pdfviewer: ^30.1.42  # 专业PDF查看器，支持缩放、搜索、注释
```

#### Flutter 3.32新特性支持
```yaml
  # 新一代UI组件
  animations: ^2.0.11            # 高级动画套件，预定义转场动画
  cached_network_image: ^3.4.1   # 网络图片缓存，支持占位符和错误处理
```

#### 性能监控
```yaml
  # 错误追踪 (与Admin Web一致)
  sentry_flutter: ^8.13.0        # 生产环境监控
```

#### 环境配置
```yaml
  # 配置管理
  flutter_dotenv: ^5.2.1         # 环境变量支持
```

#### 开发和测试工具
```yaml
dev_dependencies:
  flutter_test:
    sdk: flutter
    
  # 代码质量
  flutter_lints: ^6.0.0          # Flutter官方代码规范
  very_good_analysis: ^7.0.0    # 更严格的代码分析规则
  
  # 代码生成完整工具链
  build_runner: ^2.6.0           # 代码生成运行器
  retrofit_generator: ^10.0.1    # REST API客户端生成器
  json_serializable: ^6.10.0     # JSON序列化生成器
  injectable_generator: ^2.8.1   # 依赖注入生成器
  freezed: ^3.2.0                # 不可变对象生成器
  freezed_annotation: ^3.1.0     # Freezed注解支持
  
  # 测试框架
  bloc_test: ^10.0.0             # BLoC单元测试工具
  mocktail: ^1.0.4               # Mock对象创建，用于单元测试
  flutter_test_robots: ^0.0.24   # UI测试自动化辅助工具
```

---

## 🛠️ 开发工具配置

### 代码分析配置 (analysis_options.yaml)

项目使用统一的代码分析配置，位于项目根目录：

```yaml
# 继承Flutter推荐规范
include: package:flutter_lints/flutter.yaml

analyzer:
  language:
    # 启用严格模式
    strict-casts: true        # 严格类型转换
    strict-inference: true    # 严格类型推断
    strict-raw-types: true    # 严格原始类型检查
  
  exclude:
    # 排除生成的代码
    - "**/*.g.dart"          # JSON序列化生成文件
    - "**/*.freezed.dart"    # Freezed生成文件
    - "**/*.config.dart"     # 配置生成文件
    - "**/*.env.dart"        # 环境变量生成文件
```

### VS Code 推荐配置

创建 `.vscode/settings.json`:

```json
{
  "dart.flutterSdkPath": "/path/to/flutter",
  "dart.analyzeAngularTemplates": false,
  "dart.previewFlutterUiGuides": true,
  "dart.hotReloadOnSave": "always",
  "editor.formatOnSave": true,
  "editor.rulers": [80, 120],
  "files.associations": {
    "*.arb": "json"
  }
}
```

### 推荐扩展

- **Flutter**: Google官方Flutter支持
- **Dart**: Dart语言支持
- **Pubspec Assist**: pubspec.yaml依赖管理
- **Flutter Intl**: 国际化支持
- **GitLens**: Git增强
- **Bracket Pair Colorizer**: 括号高亮

---

## 🔧 环境变量配置

### 环境变量文件结构

```
apps/admin-web/
├── .env.example          # 环境变量模板
├── .env                  # 开发环境配置 (需手动创建)
└── .env.production       # 生产环境配置 (可选)

apps/mobile/
├── .env.example          # 环境变量模板  
├── .env                  # 开发环境配置 (需手动创建)
└── .env.production       # 生产环境配置 (可选)
```

### Admin Web 环境变量 (.env)

```bash
# API配置
API_BASE_URL=http://localhost:3102
API_TIMEOUT=30000

# Sentry错误追踪 - 需要从 https://sentry.io 获取
SENTRY_DSN_DEV=https://<EMAIL>/project-id
SENTRY_DSN_PROD=https://<EMAIL>/project-id

# 功能开关
FEATURE_AI_ASSISTANT=true
FEATURE_ANALYTICS=true
FEATURE_DARK_MODE=true

# 应用信息
APP_NAME=MasteryOS Admin
APP_VERSION=1.0.0
```

### Mobile 环境变量 (.env)

```bash
# API配置
API_BASE_URL=http://localhost:3102
API_TIMEOUT=30000

# Sentry错误追踪
SENTRY_DSN_DEV=https://<EMAIL>/project-id  
SENTRY_DSN_PROD=https://<EMAIL>/project-id

# 功能开关
FEATURE_AI_ASSISTANT=true
FEATURE_SOCIAL_LEARNING=true
FEATURE_GAMIFICATION=true
FEATURE_OFFLINE_MODE=true

# 应用信息
APP_NAME=MasteryOS
APP_VERSION=1.0.0

# 推送通知 (可选)
FCM_SERVER_KEY=your-fcm-server-key
APNS_KEY_ID=your-apns-key-id
```

### 环境变量使用示例

```dart
import 'package:flutter_dotenv/flutter_dotenv.dart';

void main() async {
  // 加载环境变量
  await dotenv.load(fileName: '.env');
  
  // 使用环境变量
  final apiUrl = dotenv.env['API_BASE_URL'] ?? 'http://localhost:3102';
  final sentryDsn = dotenv.env['SENTRY_DSN_DEV'] ?? '';
  
  runApp(MyApp(apiUrl: apiUrl));
}
```

---

## 🌍 国际化配置

### 配置文件结构

```
apps/admin-web/
├── l10n.yaml             # 国际化配置
└── lib/l10n/
    ├── app_en.arb        # 英文翻译
    └── app_zh.arb        # 中文翻译

apps/mobile/  
├── l10n.yaml             # 国际化配置
└── lib/l10n/
    ├── app_en.arb        # 英文翻译
    └── app_zh.arb        # 中文翻译
```

### 国际化配置 (l10n.yaml)

```yaml
arb-dir: lib/l10n                      # 翻译文件目录
template-arb-file: app_en.arb          # 模板文件 (英文)
output-localization-file: app_localizations.dart  # 生成的文件名
```

### 翻译文件示例 (app_en.arb)

```json
{
  "@@locale": "en",
  "appTitle": "MasteryOS",
  "@appTitle": {
    "description": "The title of the application"
  },
  "welcome": "Welcome to MasteryOS",
  "@welcome": {
    "description": "Welcome message"
  },
  "hoursTracked": "{count} hours tracked",
  "@hoursTracked": {
    "description": "Hours tracked label",
    "placeholders": {
      "count": {
        "type": "int"
      }
    }
  }
}
```

### 在代码中使用翻译

```dart
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return Column(
      children: [
        Text(l10n.appTitle),              // 简单翻译
        Text(l10n.hoursTracked(120)),     // 带参数翻译
      ],
    );
  }
}
```

### 支持的语言

- **English (en)**: 英语 (默认)
- **简体中文 (zh)**: 中文

---

## 🚀 启动指南

### 1. 环境准备

```bash
# 1. 检查Flutter环境
flutter doctor

# 2. 检查Flutter版本 (应该是3.32.1)
flutter --version

# 3. 启动数据库服务
cd /path/to/project
./scripts/1w-db.sh start

# 4. 检查数据库状态
./scripts/1w-db.sh status
```

### 2. 依赖安装

```bash
# 安装项目依赖 (在项目根目录)
pnpm install

# 安装Admin Web依赖
cd apps/admin-web
flutter pub get

# 安装Mobile依赖  
cd ../mobile
flutter pub get
```

### 3. 代码生成

```bash
# 生成Admin Web代码
cd apps/admin-web
flutter pub run build_runner build --delete-conflicting-outputs

# 生成Mobile代码
cd ../mobile  
flutter pub run build_runner build --delete-conflicting-outputs

# 生成国际化代码
cd ../admin-web && flutter gen-l10n
cd ../mobile && flutter gen-l10n
```

### 4. 环境配置

```bash
# 创建环境文件
cp apps/admin-web/.env.example apps/admin-web/.env
cp apps/mobile/.env.example apps/mobile/.env

# 编辑环境文件，填入实际配置
# vim apps/admin-web/.env
# vim apps/mobile/.env
```

### 5. 启动应用

```bash
# 启动Admin Web (端口8081)
cd apps/admin-web
flutter run -d web-server --web-port=8081

# 启动Mobile (端口8080) - 新终端
cd apps/mobile
flutter run -d web-server --web-port=8080

# 启动Unified BFF API (端口3102) - 新终端
cd apps/unified-bff
pnpm run dev
```

### 6. 验证启动

- **Admin Web**: http://localhost:8081
- **Mobile**: http://localhost:8080  
- **API**: http://localhost:3102/health
- **PostgreSQL**: localhost:8182
- **Redis**: localhost:8183

---

## 🔧 开发命令

### 代码生成相关

```bash
# 完整代码生成 (删除冲突)
flutter pub run build_runner build --delete-conflicting-outputs

# 监听模式代码生成 (开发时)
flutter pub run build_runner watch

# 清理生成的代码
flutter pub run build_runner clean
```

### 代码质量

```bash
# 代码分析
flutter analyze

# 代码格式化
dart format .

# 修复可自动修复的问题
dart fix --apply
```

### 测试相关

```bash
# 运行所有测试
flutter test

# 运行特定测试文件
flutter test test/widget_test.dart

# 测试覆盖率
flutter test --coverage
```

### 构建相关

```bash
# Web构建
flutter build web

# Android构建
flutter build apk
flutter build appbundle

# iOS构建  
flutter build ios

# 桌面构建
flutter build macos
flutter build windows
flutter build linux
```

---

## 🔍 故障排除

### 常见问题

#### 1. 依赖冲突

```bash
# 症状：版本冲突错误
# 解决：清理并重新安装
flutter clean
flutter pub get
```

#### 2. 代码生成失败

```bash
# 症状：*.g.dart文件错误
# 解决：清理重新生成
flutter pub run build_runner clean  
flutter pub run build_runner build --delete-conflicting-outputs
```

#### 3. Web端CORS问题

```bash
# 症状：API请求被阻止
# 解决：使用 --disable-web-security (仅开发)
flutter run -d chrome --web-browser-flag "--disable-web-security"
```

#### 4. 数据库连接失败

```bash
# 检查数据库状态
./scripts/1w-db.sh status

# 重启数据库
./scripts/1w-db.sh stop
./scripts/1w-db.sh start
```

#### 5. 热重载不工作

```bash
# 重启开发服务器
r (在Flutter命令行中)
R (完全重启)
```

### 性能优化

#### Web端优化

```bash
# 使用CanvasKit渲染器 (更好的性能)
flutter run -d web-server --web-renderer canvaskit

# 构建时优化
flutter build web --web-renderer canvaskit --dart-define=FLUTTER_WEB_USE_SKIA=true
```

#### 分析器使用

```bash
# 性能分析模式运行
flutter run --profile

# 内存分析
flutter run --profile --enable-software-rendering
```

### 日志调试

```bash
# 详细日志
flutter run --verbose

# 特定设备日志
flutter logs -d <device-id>
```

---

## 📚 参考资源

### 官方文档
- [Flutter Official Docs](https://docs.flutter.dev/)
- [Dart Language Tour](https://dart.dev/guides/language/language-tour)
- [Flutter Cookbook](https://docs.flutter.dev/cookbook)

### 核心包文档
- [flutter_bloc](https://bloclibrary.dev/)
- [go_router](https://pub.dev/packages/go_router)
- [dio](https://pub.dev/packages/dio)
- [get_it](https://pub.dev/packages/get_it)
- [freezed](https://pub.dev/packages/freezed)

### 最佳实践
- [Flutter最佳实践](https://docs.flutter.dev/development/tools/devtools/overview)
- [Dart编码规范](https://dart.dev/guides/language/effective-dart)
- [Flutter性能优化](https://docs.flutter.dev/perf)

---

**文档维护**: 请在每次重大更新后同步更新此文档  
**联系方式**: 如有问题请提交Issue或联系开发团队  
**版权声明**: © 2025 MasteryOS Team. All rights reserved.