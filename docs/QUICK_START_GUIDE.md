# MasteryOS Flutter 快速启动指南

**文档日期**: 2025年1月21日  
**预计完成时间**: 15-20分钟  

---

## 🚀 一键启动命令

```bash
# 1. 启动数据库服务
./scripts/1w-db.sh start

# 2. 安装所有依赖
pnpm install && cd apps/admin-web && flutter pub get && cd ../mobile && flutter pub get && cd ../..

# 3. 生成代码
cd apps/admin-web && flutter pub run build_runner build --delete-conflicting-outputs && cd ../mobile && flutter pub run build_runner build --delete-conflicting-outputs && cd ../..

# 4. 创建环境配置
cp apps/admin-web/.env.example apps/admin-web/.env && cp apps/mobile/.env.example apps/mobile/.env

# 5. 生成国际化代码
cd apps/admin-web && flutter gen-l10n && cd ../mobile && flutter gen-l10n && cd ../..
```

## 🖥️ 启动开发服务器

```bash
# Terminal 1: Admin Web (管理后台)
cd apps/admin-web
flutter run -d web-server --web-port=8081

# Terminal 2: Mobile App (移动端)  
cd apps/mobile
flutter run -d web-server --web-port=8080

# Terminal 3: BFF API (后端服务)
cd apps/unified-bff  
pnpm run dev
```

## 📱 访问地址

- **管理后台**: http://localhost:8081
- **移动应用**: http://localhost:8080
- **API服务**: http://localhost:3102
- **数据库**: localhost:8182 (PostgreSQL)
- **缓存**: localhost:8183 (Redis)

---

## ⚡ 核心包说明

| 包名 | 用途 | 版本 |
|------|------|------|
| `flutter_bloc` | 状态管理 | ^9.1.1 |
| `go_router` | 路由管理 | ^16.1.0 |
| `dio` | HTTP客户端 | ^5.8.0 |
| `get_it` | 依赖注入 | ^8.0.2 |
| `freezed` | 不可变对象 | ^3.2.0 |
| `sentry_flutter` | 错误监控 | ^8.13.0 |
| `flutter_dotenv` | 环境配置 | ^5.2.1 |

## 🔧 常用开发命令

```bash
# 代码生成 (当修改了*.dart文件需要生成代码时)
flutter pub run build_runner build --delete-conflicting-outputs

# 代码检查
flutter analyze

# 格式化代码
dart format .

# 运行测试
flutter test

# 清理项目
flutter clean && flutter pub get
```

## 🌍 国际化支持

项目支持中英文双语：
- **English**: 默认语言
- **中文**: 完整翻译

翻译文件位置：
- `apps/admin-web/lib/l10n/`
- `apps/mobile/lib/l10n/`

## 🔐 环境变量配置

### 必需配置 (production)

```bash
# apps/admin-web/.env
SENTRY_DSN_PROD=https://<EMAIL>/project-id
API_BASE_URL=https://your-api-domain.com

# apps/mobile/.env  
SENTRY_DSN_PROD=https://<EMAIL>/project-id
API_BASE_URL=https://your-api-domain.com
```

### 可选配置

```bash
# 功能开关
FEATURE_AI_ASSISTANT=true
FEATURE_ANALYTICS=true
FEATURE_DARK_MODE=true

# 推送通知 (移动端)
FCM_SERVER_KEY=your-fcm-key
```

## ❗ 故障排除

### 如果启动失败

```bash
# 1. 检查Flutter环境
flutter doctor

# 2. 清理重新安装
flutter clean
rm -rf .dart_tool
flutter pub get

# 3. 重启数据库
./scripts/1w-db.sh stop
./scripts/1w-db.sh start
```

### 如果代码生成失败

```bash
# 清理生成的代码
flutter pub run build_runner clean

# 重新生成
flutter pub run build_runner build --delete-conflicting-outputs
```

## 📋 开发检查清单

- [ ] Flutter 3.32.1+ 已安装
- [ ] 数据库服务已启动 (`./scripts/1w-db.sh status`)
- [ ] 依赖包已安装 (`flutter pub get`)
- [ ] 代码已生成 (无 `*.g.dart` 错误)
- [ ] 环境变量已配置 (`.env` 文件存在)
- [ ] 应用可访问 (localhost:8080, localhost:8081)

---

**快速支持**: 如遇问题请查看 `docs/FLUTTER_ENVIRONMENT_SETUP.md` 详细文档