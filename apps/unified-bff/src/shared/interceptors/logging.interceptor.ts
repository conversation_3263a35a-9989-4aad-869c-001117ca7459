import { Injectable, NestInterceptor, ExecutionContext, CallHandler, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Request, Response } from 'express';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const now = Date.now();
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();

    const { method, url, ip, headers } = request;
    const userAgent = headers['user-agent'] || '';

    return next.handle().pipe(
      tap(() => {
        const duration = Date.now() - now;
        const { statusCode } = response;

        const logData = {
          method,
          url,
          statusCode,
          duration: `${duration}ms`,
          ip,
          userAgent,
        };

        // 根据状态码决定日志级别
        if (statusCode >= 500) {
          this.logger.error('Request failed', logData);
        } else if (statusCode >= 400) {
          this.logger.warn('Request with client error', logData);
        } else {
          this.logger.log('Request completed', logData);
        }
      }),
    );
  }
}
