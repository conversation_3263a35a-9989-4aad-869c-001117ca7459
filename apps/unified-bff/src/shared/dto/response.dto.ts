import { ApiProperty } from '@nestjs/swagger';

export class BaseResponseDto<T = unknown> {
  @ApiProperty({ description: '请求是否成功' })
  success: boolean;

  @ApiProperty({ description: '响应数据' })
  data?: T;

  @ApiProperty({ description: '错误信息' })
  error?: {
    code: string;
    message: string;
    details?: Record<string, unknown>;
  };

  @ApiProperty({ description: '时间戳' })
  timestamp: string;

  @ApiProperty({ description: '请求路径' })
  path?: string;

  constructor(
    success: boolean,
    data?: T,
    error?: { code: string; message: string; details?: Record<string, unknown> },
    path?: string,
  ) {
    this.success = success;
    this.data = data;
    this.error = error;
    this.timestamp = new Date().toISOString();
    this.path = path;
  }

  static success<T>(data: T, path?: string): BaseResponseDto<T> {
    return new BaseResponseDto(true, data, undefined, path);
  }

  static error(
    code: string,
    message: string,
    details?: Record<string, unknown>,
    path?: string,
  ): BaseResponseDto {
    return new BaseResponseDto(false, undefined, { code, message, details }, path);
  }
}

export class MessageResponseDto {
  @ApiProperty({ description: '消息内容' })
  message: string;

  constructor(message: string) {
    this.message = message;
  }
}
