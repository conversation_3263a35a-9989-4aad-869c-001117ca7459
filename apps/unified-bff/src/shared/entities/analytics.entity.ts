import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum AnalyticsEventType {
  USER_LOGIN = 'USER_LOGIN',
  USER_LOGOUT = 'USER_LOGOUT',
  DOCUMENT_VIEW = 'DOCUMENT_VIEW',
  DOCUMENT_DOWNLOAD = 'DOCUMENT_DOWNLOAD',
  DOCUMENT_UPLOAD = 'DOCUMENT_UPLOAD',
  PAGE_VIEW = 'PAGE_VIEW',
  SEARCH = 'SEARCH',
  ERROR = 'ERROR',
  CUSTOM = 'CUSTOM',
}

@Entity('analytics_events')
@Index(['eventType'])
@Index(['userId'])
@Index(['createdAt'])
@Index(['sessionId'])
@Index(['eventType', 'createdAt']) // 复合索引用于按类型和时间查询
@Index(['userId', 'eventType']) // 复合索引用于用户行为分析
@Index(['createdAt', 'eventType', 'userId']) // 覆盖索引用于复杂查询
@Index(['sessionId', 'createdAt']) // 会话分析查询
export class AnalyticsEvent {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({
    name: 'event_type',
    type: 'enum',
    enum: AnalyticsEventType,
  })
  eventType!: AnalyticsEventType;

  @Column({ name: 'user_id', nullable: true })
  userId?: string;

  @Column({ name: 'session_id', nullable: true, length: 255 })
  sessionId?: string;

  @Column({ name: 'ip_address', type: 'inet', nullable: true })
  ipAddress?: string;

  @Column({ name: 'user_agent', type: 'text', nullable: true })
  userAgent?: string;

  @Column('jsonb', { default: '{}' })
  metadata!: Record<string, any>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt!: Date;

  // 业务方法
  static createEvent(
    eventType: AnalyticsEventType,
    userId?: string,
    sessionId?: string,
    metadata?: Record<string, any>,
    ipAddress?: string,
    userAgent?: string,
  ): Partial<AnalyticsEvent> {
    return {
      eventType,
      userId,
      sessionId,
      metadata: metadata || {},
      ipAddress,
      userAgent,
    };
  }
}

@Entity('analytics_daily_stats')
@Index(['date'])
@Index(['date', 'createdAt'])
export class AnalyticsDailyStats {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ type: 'date', unique: true })
  date!: Date;

  @Column({ name: 'total_users', default: 0 })
  totalUsers!: number;

  @Column({ name: 'active_users', default: 0 })
  activeUsers!: number;

  @Column({ name: 'new_users', default: 0 })
  newUsers!: number;

  @Column({ name: 'total_documents', default: 0 })
  totalDocuments!: number;

  @Column({ name: 'new_documents', default: 0 })
  newDocuments!: number;

  @Column({ name: 'total_page_views', default: 0 })
  totalPageViews!: number;

  @Column({ name: 'unique_visitors', default: 0 })
  uniqueVisitors!: number;

  @Column({ name: 'document_views', default: 0 })
  documentViews!: number;

  @Column({ name: 'document_downloads', default: 0 })
  documentDownloads!: number;

  @Column({ name: 'search_queries', default: 0 })
  searchQueries!: number;

  @Column({ name: 'error_count', default: 0 })
  errorCount!: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt!: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt!: Date;

  // 业务方法
  incrementMetric(
    metric: keyof Omit<AnalyticsDailyStats, 'id' | 'date' | 'createdAt' | 'updatedAt'>,
    count: number = 1,
  ): void {
    (this[metric] as number) += count;
  }

  getMetricValue(
    metric: keyof Omit<AnalyticsDailyStats, 'id' | 'date' | 'createdAt' | 'updatedAt'>,
  ): number {
    return this[metric] as number;
  }
}
