import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TypeOrmModuleOptions, TypeOrmOptionsFactory } from '@nestjs/typeorm';
import { DataSource, DataSourceOptions } from 'typeorm';

@Injectable()
export class DatabaseConfig implements TypeOrmOptionsFactory {
  constructor(private configService: ConfigService) {}

  createTypeOrmOptions(): TypeOrmModuleOptions {
    const isProduction = this.configService.get('NODE_ENV') === 'production';

    return {
      type: 'postgres',
      host: this.configService.get('DATABASE_HOST') || 'localhost',
      port: parseInt(this.configService.get('DATABASE_PORT')) || 8182,
      username: this.configService.get('DATABASE_USER') || 'masteryos',
      password: this.configService.get('DATABASE_PASSWORD') || 'masteryos123',
      database: this.configService.get('DATABASE_NAME') || 'masteryos',

      // 实体配置
      entities: [`${__dirname}/../**/*.entity{.ts,.js}`],
      migrations: [`${__dirname}/../database/migrations/*{.ts,.js}`],

      // 开发环境配置
      synchronize: !isProduction, // 生产环境禁用同步
      logging: !isProduction ? ['query', 'error'] : ['error'],

      // 连接池配置
      extra: {
        connectionLimit: 10,
        acquireTimeout: 60000,
        timeout: 60000,
      },

      // SSL配置（生产环境）
      ssl: isProduction ? { rejectUnauthorized: false } : false,

      // 自动加载实体
      autoLoadEntities: true,

      // 数据库连接重试
      retryAttempts: 5,
      retryDelay: 3000,
    };
  }
}

// 用于CLI工具的数据源配置
export const dataSource = new DataSource({
  type: 'postgres',
  host: process.env['DATABASE_HOST'] || 'localhost',
  port: parseInt(process.env['DATABASE_PORT'] || '0') || 8182,
  username: process.env['DATABASE_USER'] || 'masteryos',
  password: process.env['DATABASE_PASSWORD'] || 'masteryos123',
  database: process.env['DATABASE_NAME'] || 'masteryos',
  entities: [`${__dirname}/../**/*.entity{.ts,.js}`],
  migrations: [`${__dirname}/../database/migrations/*{.ts,.js}`],
  synchronize: false,
  logging: process.env['NODE_ENV'] !== 'production',
} as DataSourceOptions);
