import { registerAs } from '@nestjs/config';

export const appConfig = registerAs('app', () => ({
  name: 'MasteryOS Unified BFF',
  version: '1.0.0',
  environment: process.env['NODE_ENV'] || 'development',
  port: parseInt(process.env['PORT'] || '3100'),

  // CORS配置
  corsOrigins: process.env['CORS_ORIGINS']?.split(',') || [
    'http://localhost:3200',
    'http://localhost:3100',
    'http://localhost:8080',
  ],

  // 文件上传配置
  fileUpload: {
    maxSize: parseInt(process.env['MAX_FILE_SIZE'] || '104857600'), // 100MB
    allowedTypes: process.env['ALLOWED_FILE_TYPES']?.split(',') || [
      'pdf',
      'doc',
      'docx',
      'xls',
      'xlsx',
      'ppt',
      'pptx',
      'txt',
      'jpg',
      'jpeg',
      'png',
      'gif',
      'mp4',
      'mp3',
    ],
  },

  // 分页配置
  pagination: {
    defaultPage: 1,
    defaultPageSize: 20,
    maxPageSize: 100,
  },

  // 日志配置
  logging: {
    level: process.env['LOG_LEVEL'] || 'info',
    format: process.env['LOG_FORMAT'] || 'json',
  },

  // 邮件配置
  mail: {
    host: process.env['SMTP_HOST'] || 'smtp.gmail.com',
    port: parseInt(process.env['SMTP_PORT'] || '587'),
    secure: process.env['SMTP_SECURE'] === 'true',
    user: process.env['SMTP_USER'],
    password: process.env['SMTP_PASSWORD'],
    from: process.env['FROM_EMAIL'] || '<EMAIL>',
  },

  // 短信配置
  sms: {
    provider: process.env['SMS_PROVIDER'] || 'aliyun',
    apiKey: process.env['SMS_API_KEY'],
    apiSecret: process.env['SMS_API_SECRET'],
    signName: process.env['SMS_SIGN_NAME'] || 'MasteryOS',
  },
}));
