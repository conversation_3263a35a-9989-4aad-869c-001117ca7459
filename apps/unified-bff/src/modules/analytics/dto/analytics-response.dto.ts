import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { AnalyticsEventType } from '@/shared/entities/analytics.entity';

export class AnalyticsEventDto {
  @ApiProperty({ description: '事件ID' })
  id!: string;

  @ApiProperty({
    description: '事件类型',
    enum: AnalyticsEventType,
  })
  eventType!: AnalyticsEventType;

  @ApiPropertyOptional({ description: '用户ID' })
  userId?: string;

  @ApiPropertyOptional({ description: '会话ID' })
  sessionId?: string;

  @ApiPropertyOptional({ description: 'IP地址' })
  ipAddress?: string;

  @ApiProperty({
    description: '元数据',
    example: { page: '/dashboard', duration: 120 },
  })
  metadata!: Record<string, unknown>;

  @ApiProperty({ description: '创建时间' })
  createdAt!: Date;
}

export class UserActivityTrend {
  @ApiProperty({ description: '日期' })
  date!: string;

  @ApiProperty({ description: '活跃用户数' })
  activeUsers!: number;

  @ApiProperty({ description: '新用户数' })
  newUsers!: number;

  @ApiProperty({ description: '总会话数' })
  totalSessions!: number;
}

export class ContentEngagement {
  @ApiProperty({ description: '日期' })
  date!: string;

  @ApiProperty({ description: '页面浏览数' })
  pageViews!: number;

  @ApiProperty({ description: '文档查看数' })
  documentViews!: number;

  @ApiProperty({ description: '文档下载数' })
  documentDownloads!: number;

  @ApiProperty({ description: '搜索查询数' })
  searchQueries!: number;
}

export class SystemUsage {
  @ApiProperty({ description: '日期' })
  date!: string;

  @ApiProperty({ description: '独立访客数' })
  uniqueVisitors!: number;

  @ApiProperty({ description: '总文档数' })
  totalDocuments!: number;

  @ApiProperty({ description: '新文档数' })
  newDocuments!: number;

  @ApiProperty({ description: '错误数' })
  errorCount!: number;
}

export class TopContent {
  @ApiProperty({ description: '内容ID' })
  contentId!: string;

  @ApiProperty({ description: '内容标题' })
  title!: string;

  @ApiProperty({ description: '查看次数' })
  views!: number;

  @ApiProperty({ description: '下载次数' })
  downloads!: number;

  @ApiProperty({ description: '最后访问时间' })
  lastAccessed!: Date;
}

export class DashboardStatsDto {
  @ApiProperty({ description: '总用户数' })
  totalUsers!: number;

  @ApiProperty({ description: '活跃用户数' })
  activeUsers!: number;

  @ApiProperty({ description: '新用户数' })
  newUsers!: number;

  @ApiProperty({ description: '总文档数' })
  totalDocuments!: number;

  @ApiProperty({ description: '新文档数' })
  newDocuments!: number;

  @ApiProperty({ description: '总页面浏览数' })
  totalPageViews!: number;

  @ApiProperty({ description: '独立访客数' })
  uniqueVisitors!: number;

  @ApiProperty({ description: '用户活动趋势', type: [UserActivityTrend] })
  userActivityTrend!: UserActivityTrend[];

  @ApiProperty({ description: '内容参与度', type: [ContentEngagement] })
  contentEngagement!: ContentEngagement[];

  @ApiProperty({ description: '系统使用情况', type: [SystemUsage] })
  systemUsage!: SystemUsage[];

  @ApiProperty({ description: '热门内容', type: [TopContent] })
  topContent!: TopContent[];

  @ApiProperty({ description: '统计时间范围' })
  timeRange!: {
    startDate: string;
    endDate: string;
    days: number;
  };
}

export class EventStatsDto {
  @ApiProperty({ description: '事件类型', enum: AnalyticsEventType })
  eventType!: AnalyticsEventType;

  @ApiProperty({ description: '事件总数' })
  totalEvents!: number;

  @ApiProperty({ description: '独立用户数' })
  uniqueUsers!: number;

  @ApiProperty({ description: '平均每用户事件数' })
  avgEventsPerUser!: number;

  @ApiProperty({ description: '时间趋势' })
  timeTrend!: Array<{
    date: string;
    count: number;
  }>;
}

export class CreateEventDto {
  @ApiProperty({
    description: '事件类型',
    enum: AnalyticsEventType,
  })
  eventType!: AnalyticsEventType;

  @ApiPropertyOptional({ description: '用户ID' })
  userId?: string;

  @ApiPropertyOptional({ description: '会话ID' })
  sessionId?: string;

  @ApiPropertyOptional({
    description: '元数据',
    example: { page: '/dashboard', action: 'click' },
  })
  metadata?: Record<string, unknown>;
}
