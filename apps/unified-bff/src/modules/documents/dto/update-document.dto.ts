import { AccessLevel, DocumentStatus, DocumentType } from '@/shared/entities/document.entity';
import { OmitType, PartialType } from '@nestjs/mapped-types';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { CreateDocumentDto } from './create-document.dto';

export class UpdateDocumentDto extends PartialType(
  OmitType(CreateDocumentDto, ['fileName', 'filePath', 'fileSize'] as const),
) {
  @ApiPropertyOptional({
    description: '文档标题',
    example: '更新后的系统使用手册',
  })
  override title?: string;

  @ApiPropertyOptional({
    description: '文档描述',
    example: '更新的系统操作指南',
  })
  description?: string;

  @ApiPropertyOptional({
    description: 'MIME类型',
    example: 'application/pdf',
  })
  mimeType?: string;

  @ApiPropertyOptional({
    description: '文档类型',
    enum: DocumentType,
  })
  @IsOptional()
  @IsEnum(DocumentType)
  type?: DocumentType;

  @ApiPropertyOptional({
    description: '文档状态',
    enum: DocumentStatus,
  })
  @IsOptional()
  @IsEnum(DocumentStatus)
  status?: DocumentStatus;

  @ApiPropertyOptional({
    description: '访问级别',
    enum: AccessLevel,
  })
  @IsOptional()
  @IsEnum(AccessLevel)
  accessLevel?: AccessLevel;

  @ApiPropertyOptional({
    description: '标签',
    example: ['manual', 'guide', 'updated'],
  })
  tags?: string[];

  @ApiPropertyOptional({
    description: '元数据',
    example: { author: 'Admin', category: 'System', updated: true },
  })
  metadata?: Record<string, unknown>;

  @ApiPropertyOptional({
    description: '缩略图路径',
    example: '/thumbnails/manual_updated_thumb.jpg',
  })
  thumbnailPath?: string;

  @ApiPropertyOptional({
    description: '是否可搜索',
  })
  isSearchable?: boolean;

  @ApiPropertyOptional({
    description: '文档内容（用于搜索）',
  })
  content?: string;
}
