import { AccessLevel, DocumentStatus, DocumentType } from '@/shared/entities/document.entity';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class DocumentResponseDto {
  @ApiProperty({ description: '文档ID' })
  id!: string;

  @ApiProperty({ description: '文档标题' })
  title!: string;

  @ApiPropertyOptional({ description: '文档描述' })
  description?: string;

  @ApiProperty({ description: '文件名' })
  fileName!: string;

  @ApiProperty({ description: '文件大小（字节）' })
  fileSize!: number;

  @ApiProperty({ description: '格式化文件大小' })
  formattedSize!: string;

  @ApiPropertyOptional({ description: 'MIME类型' })
  mimeType?: string;

  @ApiProperty({ description: '文件扩展名' })
  fileExtension!: string;

  @ApiProperty({
    description: '文档类型',
    enum: DocumentType,
  })
  type!: DocumentType;

  @ApiProperty({
    description: '文档状态',
    enum: DocumentStatus,
  })
  status!: DocumentStatus;

  @ApiProperty({
    description: '访问级别',
    enum: AccessLevel,
  })
  accessLevel!: AccessLevel;

  @ApiProperty({ description: '是否公开' })
  isPublic!: boolean;

  @ApiProperty({ description: '是否已发布' })
  isPublished!: boolean;

  @ApiProperty({ description: '是否为草稿' })
  isDraft!: boolean;

  @ApiProperty({ description: '标签' })
  tags!: string[];

  @ApiProperty({
    description: '元数据',
    example: { author: 'Admin', category: 'System' },
  })
  metadata!: Record<string, unknown>;

  @ApiProperty({ description: '浏览次数' })
  viewCount!: number;

  @ApiProperty({ description: '下载次数' })
  downloadCount!: number;

  @ApiPropertyOptional({ description: '缩略图路径' })
  thumbnailPath?: string;

  @ApiProperty({ description: '版本号' })
  version!: number;

  @ApiProperty({ description: '创建时间' })
  createdAt!: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt!: Date;

  // 创建者信息（可选包含）
  @ApiPropertyOptional({
    description: '创建者信息',
    type: Object,
  })
  creator?: {
    id: string;
    username: string;
    fullName: string;
  };
}

export class DocumentStatsDto {
  @ApiProperty({ description: '总文档数' })
  totalDocuments!: number;

  @ApiProperty({ description: '已发布文档数' })
  publishedDocuments!: number;

  @ApiProperty({ description: '草稿文档数' })
  draftDocuments!: number;

  @ApiProperty({ description: '指定天数内新文档数' })
  newDocuments!: number;

  @ApiProperty({ description: '总文件大小（字节）' })
  totalFileSize!: number;

  @ApiProperty({ description: '格式化总文件大小' })
  totalFileSizeFormatted!: string;

  @ApiProperty({ description: '平均文件大小（字节）' })
  avgFileSize!: number;

  @ApiProperty({ description: '总浏览次数' })
  totalViews!: number;

  @ApiProperty({ description: '总下载次数' })
  totalDownloads!: number;

  @ApiPropertyOptional({
    description: '分组统计（当指定groupBy时）',
    type: Object,
  })
  groupStats?: Record<string, number>;
}

export class DocumentUploadDto {
  @ApiProperty({ description: '上传的文件' })
  file!: any; // 实际使用时会被multer处理

  @ApiPropertyOptional({ description: '文档标题' })
  title?: string;

  @ApiPropertyOptional({ description: '文档描述' })
  description?: string;

  @ApiPropertyOptional({
    description: '文档类型',
    enum: DocumentType,
  })
  type?: DocumentType;

  @ApiPropertyOptional({
    description: '访问级别',
    enum: AccessLevel,
  })
  accessLevel?: AccessLevel;

  @ApiPropertyOptional({ description: '标签（JSON字符串）' })
  tags?: string;

  @ApiPropertyOptional({ description: '元数据（JSON字符串）' })
  metadata?: string;
}
