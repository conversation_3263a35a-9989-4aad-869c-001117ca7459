import { AccessLevel, DocumentStatus, DocumentType } from '@/shared/entities/document.entity';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsObject,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';

export class CreateDocumentDto {
  @ApiProperty({
    description: '文档标题',
    maxLength: 255,
    example: '系统使用手册',
  })
  @IsString()
  @MaxLength(255)
  title!: string;

  @ApiPropertyOptional({
    description: '文档描述',
    example: '详细的系统操作指南',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: '文件名',
    example: 'manual.pdf',
  })
  @IsString()
  fileName!: string;

  @ApiProperty({
    description: '文件路径',
    example: '/documents/2024/01/manual.pdf',
  })
  @IsString()
  filePath!: string;

  @ApiProperty({
    description: '文件大小（字节）',
    example: 1024000,
  })
  fileSize!: number;

  @ApiPropertyOptional({
    description: 'MIME类型',
    example: 'application/pdf',
  })
  @IsOptional()
  @IsString()
  mimeType?: string;

  @ApiProperty({
    description: '文档类型',
    enum: DocumentType,
    example: DocumentType.MANUAL,
  })
  @IsEnum(DocumentType)
  type!: DocumentType;

  @ApiPropertyOptional({
    description: '文档状态',
    enum: DocumentStatus,
    default: DocumentStatus.DRAFT,
  })
  @IsOptional()
  @IsEnum(DocumentStatus)
  status?: DocumentStatus;

  @ApiPropertyOptional({
    description: '访问级别',
    enum: AccessLevel,
    default: AccessLevel.INTERNAL,
  })
  @IsOptional()
  @IsEnum(AccessLevel)
  accessLevel?: AccessLevel;

  @ApiPropertyOptional({
    description: '标签',
    example: ['manual', 'guide', 'help'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({
    description: '元数据',
    example: { author: 'Admin', category: 'System' },
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, unknown>;

  @ApiPropertyOptional({
    description: '缩略图路径',
    example: '/thumbnails/manual_thumb.jpg',
  })
  @IsOptional()
  @IsString()
  thumbnailPath?: string;

  @ApiPropertyOptional({
    description: '是否可搜索',
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isSearchable?: boolean;

  @ApiPropertyOptional({
    description: '文档内容（用于搜索）',
  })
  @IsOptional()
  @IsString()
  content?: string;
}
