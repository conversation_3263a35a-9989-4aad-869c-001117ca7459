import { UserRole } from '@/shared/enums/user-role.enum';
import { UserStatus } from '@/shared/enums/user-status.enum';
import { OmitType, PartialType } from '@nestjs/mapped-types';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { CreateUserDto } from './create-user.dto';

export class UpdateUserDto extends PartialType(
  OmitType(CreateUserDto, ['password', 'username'] as const),
) {
  @ApiPropertyOptional({
    description: '邮箱地址',
    example: '<EMAIL>',
  })
  email?: string;

  @ApiPropertyOptional({
    description: '名字',
    example: 'NewFirstName',
  })
  firstName?: string;

  @ApiPropertyOptional({
    description: '姓氏',
    example: 'NewLastName',
  })
  lastName?: string;

  @ApiPropertyOptional({
    description: '头像URL',
    example: 'https://example.com/new-avatar.jpg',
  })
  avatarUrl?: string;

  @ApiPropertyOptional({
    description: '手机号码',
    example: '+8613800138001',
  })
  override phone?: string;

  @ApiPropertyOptional({
    description: '用户角色',
    enum: UserRole,
  })
  @IsOptional()
  @IsEnum(UserRole)
  override role?: UserRole;

  @ApiPropertyOptional({
    description: '用户状态',
    enum: UserStatus,
  })
  @IsOptional()
  @IsEnum(UserStatus)
  override status?: UserStatus;

  @ApiPropertyOptional({
    description: '用户偏好设置',
    example: { theme: 'light', language: 'en-US' },
  })
  override preferences?: Record<string, unknown>;
}
