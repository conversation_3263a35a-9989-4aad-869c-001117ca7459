import { PaginationDto } from '@/shared/dto/pagination.dto';
import { UserRole } from '@/shared/enums/user-role.enum';
import { UserStatus } from '@/shared/enums/user-status.enum';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsDateString, IsEnum, IsOptional, IsString } from 'class-validator';

export class UserQueryDto extends PaginationDto {
  @ApiPropertyOptional({
    description: '用户角色筛选',
    enum: UserRole,
  })
  @IsOptional()
  @IsEnum(UserRole)
  role?: UserRole;

  @ApiPropertyOptional({
    description: '用户状态筛选',
    enum: UserStatus,
  })
  @IsOptional()
  @IsEnum(UserStatus)
  status?: UserStatus;

  @ApiPropertyOptional({
    description: '邮箱验证状态',
    type: Boolean,
  })
  @IsOptional()
  emailVerified?: boolean;

  @ApiPropertyOptional({
    description: '手机验证状态',
    type: Boolean,
  })
  @IsOptional()
  mobileVerified?: boolean;

  @ApiPropertyOptional({
    description: '创建开始时间',
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  createdFrom?: string;

  @ApiPropertyOptional({
    description: '创建结束时间',
    example: '2024-12-31T23:59:59.999Z',
  })
  @IsOptional()
  @IsDateString()
  createdTo?: string;

  @ApiPropertyOptional({
    description: '最后登录开始时间',
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  lastLoginFrom?: string;

  @ApiPropertyOptional({
    description: '最后登录结束时间',
    example: '2024-12-31T23:59:59.999Z',
  })
  @IsOptional()
  @IsDateString()
  lastLoginTo?: string;

  @ApiPropertyOptional({
    description: '排序字段',
    enum: ['createdAt', 'updatedAt', 'lastLoginAt', 'username', 'email'],
    default: 'createdAt',
  })
  @IsOptional()
  @IsString()
  declare sortBy?: 'createdAt' | 'updatedAt' | 'lastLoginAt' | 'username' | 'email';
}
