import { CacheService } from '@/core/cache/cache.service';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';

@Injectable()
export class HealthService {
  constructor(
    private configService: ConfigService,
    @InjectDataSource() private dataSource: DataSource,
    private cacheService: CacheService,
  ) {}

  // 基础健康检查
  getBasicHealth() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env['npm_package_version'] || '1.0.0',
      environment: this.configService.get('NODE_ENV') || 'development',
      memory: {
        used: `${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB`,
        total: `${Math.round(process.memoryUsage().heapTotal / 1024 / 1024)}MB`,
      },
    };
  }

  // 详细健康检查
  async getDetailedHealth() {
    const [databaseHealth, redisHealth] = await Promise.allSettled([
      this.getDatabaseHealth(),
      this.getRedisHealth(),
    ]);

    return {
      ...this.getBasicHealth(),
      services: {
        database:
          databaseHealth.status === 'fulfilled'
            ? databaseHealth.value
            : { status: 'error', error: databaseHealth.reason?.message },
        redis:
          redisHealth.status === 'fulfilled'
            ? redisHealth.value
            : { status: 'error', error: redisHealth.reason?.message },
      },
      system: {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version,
        pid: process.pid,
        cpuUsage: process.cpuUsage(),
      },
    };
  }

  // 数据库健康检查
  async getDatabaseHealth() {
    try {
      const startTime = Date.now();

      // 执行简单查询测试连接
      await this.dataSource.query('SELECT 1');

      const responseTime = Date.now() - startTime;

      return {
        status: 'ok',
        responseTime: `${responseTime}ms`,
        connection: 'active',
        database: this.dataSource.options.database,
        type: this.dataSource.options.type,
      };
    } catch (error) {
      return {
        status: 'error',
        error: error.message,
        connection: 'failed',
      };
    }
  }

  // Redis健康检查
  async getRedisHealth() {
    try {
      const startTime = Date.now();

      // 测试Redis连接
      const testKey = 'health_check_test';
      const testValue = 'ok';

      await this.cacheService.set(testKey, testValue, 1000); // 1秒过期
      const retrievedValue = await this.cacheService.get(testKey);

      if (retrievedValue !== testValue) {
        throw new Error('Redis读写测试失败');
      }

      const responseTime = Date.now() - startTime;

      return {
        status: 'ok',
        responseTime: `${responseTime}ms`,
        connection: 'active',
      };
    } catch (error) {
      return {
        status: 'error',
        error: error.message,
        connection: 'failed',
      };
    }
  }
}
