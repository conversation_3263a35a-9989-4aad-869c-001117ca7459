import { AuthService } from '@/core/auth/auth.service';
import { CurrentUser, CurrentUserData } from '@/core/auth/decorators/current-user.decorator';
import { Public } from '@/core/auth/decorators/public.decorator';
import { JwtAuthGuard } from '@/core/auth/guards/jwt-auth.guard';
import { LocalAuthGuard } from '@/core/auth/guards/local-auth.guard';
import { BaseResponseDto } from '@/shared/dto/base-response.dto';
import {
  Body,
  Controller,
  Get,
  Headers,
  HttpCode,
  HttpStatus,
  Post,
  Req,
  UnauthorizedException,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthResponseDto } from './dto/auth-response.dto';
import { LoginDto } from './dto/login.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  @Post('login')
  @Public()
  @UseGuards(LocalAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '用户登录' })
  @ApiResponse({
    status: 200,
    description: '登录成功',
    type: AuthResponseDto,
  })
  async login(
    @Req() req: unknown,
    @Body() _loginDto: LoginDto,
  ): Promise<BaseResponseDto<AuthResponseDto>> {
    const user = req.user;
    const tokens = await this.authService.generateTokens(user);

    return {
      success: true,
      message: '登录成功',
      data: {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role,
          firstName: user.firstName,
          lastName: user.lastName,
          avatarUrl: user.avatarUrl,
        },
        ...tokens,
      },
    };
  }

  @Post('refresh')
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '刷新访问令牌' })
  @ApiResponse({
    status: 200,
    description: '刷新成功',
  })
  async refreshToken(
    @Body() refreshTokenDto: RefreshTokenDto,
  ): Promise<BaseResponseDto<{ accessToken: string }>> {
    const result = await this.authService.refreshToken(refreshTokenDto.refreshToken);

    if (!result) {
      throw new UnauthorizedException('无效的刷新令牌');
    }

    return {
      success: true,
      message: '刷新成功',
      data: result,
    };
  }

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth()
  @ApiOperation({ summary: '用户登出' })
  @ApiResponse({
    status: 200,
    description: '登出成功',
  })
  async logout(@Headers('authorization') authHeader: string): Promise<BaseResponseDto<void>> {
    const token = authHeader?.replace('Bearer ', '');
    if (token) {
      await this.authService.logout(token);
    }

    return {
      success: true,
      message: '登出成功',
    };
  }

  @Get('profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取当前用户信息' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
  })
  async getProfile(
    @CurrentUser() currentUser: CurrentUserData,
  ): Promise<BaseResponseDto<CurrentUserData>> {
    return {
      success: true,
      message: '获取用户信息成功',
      data: currentUser,
    };
  }
}
