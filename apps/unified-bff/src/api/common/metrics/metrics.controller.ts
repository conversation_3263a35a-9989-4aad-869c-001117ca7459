import { MonitoringService } from '@/core/monitoring/monitoring.service';
import { PrometheusService } from '@/core/monitoring/prometheus/prometheus.service';
import { Controller, Get, Header, UseGuards } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ThrottlerGuard } from '@nestjs/throttler';

@ApiTags('Common - Metrics')
@Controller({ path: 'metrics', version: '1' })
@UseGuards(ThrottlerGuard)
export class MetricsController {
  constructor(
    private readonly prometheusService: PrometheusService,
    private readonly monitoringService: MonitoringService,
    private readonly configService: ConfigService,
  ) {}

  @Get()
  @Header('Content-Type', 'text/plain; version=0.0.4; charset=utf-8')
  @ApiOperation({
    summary: '获取Prometheus格式指标',
    description: '返回Prometheus格式的应用指标，用于监控系统抓取',
  })
  @ApiResponse({
    status: 200,
    description: 'Prometheus格式的指标数据',
    content: {
      'text/plain': {
        example: `# HELP masteryos_http_requests_total Total number of HTTP requests
# TYPE masteryos_http_requests_total counter
masteryos_http_requests_total{method="GET",route="/api/v1/health",status_code="200",version="v1"} 150

# HELP masteryos_http_request_duration_seconds Duration of HTTP requests in seconds
# TYPE masteryos_http_request_duration_seconds histogram
masteryos_http_request_duration_seconds_bucket{method="GET",route="/api/v1/health",status_code="200",le="0.1"} 120
masteryos_http_request_duration_seconds_bucket{method="GET",route="/api/v1/health",status_code="200",le="0.3"} 145
masteryos_http_request_duration_seconds_bucket{method="GET",route="/api/v1/health",status_code="200",le="+Inf"} 150`,
      },
    },
  })
  async getMetrics(): Promise<string> {
    // 更新系统和应用指标
    await this.updateMetrics();

    return this.prometheusService.getMetrics();
  }

  @Get('json')
  @ApiOperation({
    summary: '获取JSON格式指标',
    description: '返回JSON格式的指标数据，便于调试和查看',
  })
  @ApiResponse({
    status: 200,
    description: 'JSON格式的指标数据',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          name: { type: 'string', description: '指标名称' },
          help: { type: 'string', description: '指标描述' },
          type: { type: 'string', description: '指标类型' },
          values: { type: 'array', description: '指标值' },
          aggregator: { type: 'string', description: '聚合器' },
        },
      },
    },
  })
  async getMetricsAsJson() {
    // 更新系统和应用指标
    await this.updateMetrics();

    return this.prometheusService.getMetricsAsJson();
  }

  @Get('health')
  @ApiOperation({
    summary: '指标系统健康检查',
    description: '检查Prometheus指标收集系统是否正常工作',
  })
  @ApiResponse({
    status: 200,
    description: '指标系统健康状态',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', enum: ['healthy', 'unhealthy'] },
        timestamp: { type: 'string', format: 'date-time' },
        stats: {
          type: 'object',
          properties: {
            totalMetrics: { type: 'number' },
            metricTypes: { type: 'object' },
            lastUpdated: { type: 'string', format: 'date-time' },
          },
        },
      },
    },
  })
  async getMetricsHealth() {
    const isHealthy = this.prometheusService.isHealthy();
    const stats = await this.prometheusService.getMetricsStats();

    return {
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      stats,
    };
  }

  @Get('stats')
  @ApiOperation({
    summary: '获取指标统计信息',
    description: '获取指标收集的统计信息',
  })
  @ApiResponse({
    status: 200,
    description: '指标统计信息',
  })
  async getMetricsStats() {
    const prometheusStats = await this.prometheusService.getMetricsStats();
    const systemMetrics = await this.monitoringService.getSystemMetrics();
    const appMetrics = this.monitoringService.getApplicationMetrics();

    return {
      prometheus: prometheusStats,
      system: {
        uptime: systemMetrics.uptime,
        memory: systemMetrics.memory.usagePercent,
        cpu: systemMetrics.cpu.usage,
      },
      application: {
        requests: appMetrics.requests.total,
        errors: appMetrics.errors.total,
        errorRate: appMetrics.errors.rate,
        avgResponseTime: appMetrics.requests.averageResponseTime,
      },
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 更新所有指标
   */
  private async updateMetrics(): Promise<void> {
    try {
      // 获取系统指标
      const systemMetrics = await this.monitoringService.getSystemMetrics();
      this.prometheusService.updateSystemMetrics({
        memoryUsage: {
          total: systemMetrics.memory.total,
          used: systemMetrics.memory.used,
          free: systemMetrics.memory.free,
          heap: systemMetrics.memory.heap.used,
        },
        cpuUsage: systemMetrics.cpu.usage,
        uptime: systemMetrics.uptime,
      });

      // 获取应用指标
      const appMetrics = this.monitoringService.getApplicationMetrics();
      this.prometheusService.updateApplicationMetrics({
        activeUsers: appMetrics.auth.activeUsers,
        databaseConnections: appMetrics.database.connections,
        cacheHitRate: appMetrics.cache.hitRate,
        queueSizes: {
          // 这里可以添加队列大小信息
          default: 0,
        },
      });

      // 更新错误率
      this.prometheusService.updateErrorRate('api', appMetrics.errors.rate);
      this.prometheusService.updateErrorRate('database', 0); // 需要从数据库监控获取
      this.prometheusService.updateErrorRate('cache', 0); // 需要从缓存监控获取
    } catch (error) {
      // 静默处理错误，避免影响指标收集
      console.error('Failed to update metrics:', error);
    }
  }
}
