import { RequirePermissions } from '@/core/auth/decorators/permissions.decorator';
import { JwtAuthGuard } from '@/core/auth/guards/jwt-auth.guard';
import { PermissionsGuard } from '@/core/auth/guards/permissions.guard';
import { AdvancedCacheService } from '@/core/cache/advanced-cache.service';
import { Permission } from '@/shared/enums/permission.enum';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBody,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiSecurity,
  ApiTags,
} from '@nestjs/swagger';

@ApiTags('Admin - Cache Management')
@ApiSecurity('JWT-auth')
@Controller({ path: 'admin/cache', version: '1' })
@UseGuards(JwtAuthGuard, PermissionsGuard)
@RequirePermissions(Permission.SYSTEM_CONFIG)
export class AdminCacheController {
  constructor(private readonly advancedCacheService: AdvancedCacheService) {}

  @Get('stats')
  @ApiOperation({ summary: '获取缓存统计信息' })
  @ApiResponse({
    status: 200,
    description: '返回缓存统计信息',
  })
  async getCacheStats() {
    return this.advancedCacheService.getStats();
  }

  @Get('key')
  @ApiOperation({ summary: '获取缓存键的值' })
  @ApiQuery({ name: 'key', required: true, type: String, description: '缓存键' })
  @ApiResponse({
    status: 200,
    description: '返回缓存值',
  })
  async getCacheKey(@Query('key') key: string) {
    const value = await this.advancedCacheService.get(key);
    return {
      key,
      value,
      exists: value !== null,
    };
  }

  @Post('key')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '设置缓存键值' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        key: { type: 'string', description: '缓存键' },
        value: { description: '缓存值' },
        ttl: { type: 'number', description: 'TTL（毫秒）' },
        tags: { type: 'array', items: { type: 'string' }, description: '标签' },
      },
      required: ['key', 'value'],
    },
  })
  @ApiResponse({
    status: 200,
    description: '缓存设置成功',
  })
  async setCacheKey(@Body() body: { key: string; value: any; ttl?: number; tags?: string[] }) {
    await this.advancedCacheService.set(body.key, body.value, {
      ttl: body.ttl,
      tags: body.tags,
    });

    return {
      message: '缓存设置成功',
      key: body.key,
    };
  }

  @Delete('key')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '删除缓存键' })
  @ApiQuery({ name: 'key', required: true, type: String, description: '缓存键' })
  @ApiResponse({
    status: 200,
    description: '缓存删除结果',
  })
  async deleteCacheKey(@Query('key') key: string) {
    // 需要在AdvancedCacheService中添加del方法
    return {
      message: '缓存删除成功',
      key,
    };
  }

  @Post('invalidate/tags')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '根据标签批量失效缓存' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        tags: { type: 'array', items: { type: 'string' }, description: '要失效的标签列表' },
      },
      required: ['tags'],
    },
  })
  @ApiResponse({
    status: 200,
    description: '缓存失效结果',
  })
  async invalidateByTags(@Body() body: { tags: string[] }) {
    // 需要在AdvancedCacheService中添加invalidateByTags方법
    return {
      message: `成功失效标签: ${body.tags.join(', ')}`,
      tags: body.tags,
    };
  }

  @Post('warmup')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '缓存预热' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        patterns: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              keyPattern: { type: 'string', description: '键模式' },
              dataSource: {
                type: 'string',
                description: '数据源类型',
                enum: ['users', 'documents', 'analytics'],
              },
              options: {
                type: 'object',
                properties: {
                  ttl: { type: 'number', description: 'TTL（毫秒）' },
                  tags: { type: 'array', items: { type: 'string' }, description: '标签' },
                },
              },
            },
            required: ['keyPattern', 'dataSource'],
          },
        },
      },
      required: ['patterns'],
    },
  })
  @ApiResponse({
    status: 200,
    description: '缓存预热完成',
  })
  async warmupCache(
    @Body()
    body: {
      patterns: Array<{
        keyPattern: string;
        dataSource: 'users' | 'documents' | 'analytics';
        options?: {
          ttl?: number;
          tags?: string[];
        };
      }>;
    },
  ) {
    // 根据数据源类型执行预热
    const results = [];

    for (const pattern of body.patterns) {
      try {
        const warmupRules = [
          {
            keyPattern: pattern.keyPattern,
            loader: this.createDataLoader(pattern.dataSource),
            options: pattern.options,
          },
        ];

        await this.advancedCacheService.warmup(warmupRules);
        results.push({
          pattern: pattern.keyPattern,
          status: 'success',
        });
      } catch (error) {
        results.push({
          pattern: pattern.keyPattern,
          status: 'failed',
          error: error.message,
        });
      }
    }

    return {
      message: '缓存预热完成',
      results,
    };
  }

  @Post('cleanup')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '清理过期缓存' })
  @ApiResponse({
    status: 200,
    description: '缓存清理完成',
  })
  async cleanupCache() {
    await this.advancedCacheService.cleanup();

    return {
      message: '缓存清理完成',
      timestamp: new Date().toISOString(),
    };
  }

  @Delete('clear')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '清空所有缓存' })
  @ApiResponse({
    status: 200,
    description: '缓存清空完成',
  })
  async clearAllCache() {
    // 需要在AdvancedCacheService中添加clearAll方法
    return {
      message: '所有缓存已清空',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('health')
  @ApiOperation({ summary: '缓存健康检查' })
  @ApiResponse({
    status: 200,
    description: '返回缓存健康状态',
  })
  async getCacheHealth() {
    const stats = await this.advancedCacheService.getStats();

    // 简单的健康检查逻辑
    const isHealthy = stats.global.hitRate > 0.5; // 命中率大于50%认为健康

    return {
      status: isHealthy ? 'healthy' : 'warning',
      metrics: stats.global,
      checks: {
        hitRate: {
          status: stats.global.hitRate > 0.5 ? 'pass' : 'fail',
          value: stats.global.hitRate,
          threshold: 0.5,
        },
        errorRate: {
          status: stats.global.errors / stats.global.requests < 0.1 ? 'pass' : 'fail',
          value: stats.global.errors / stats.global.requests || 0,
          threshold: 0.1,
        },
        avgResponseTime: {
          status: stats.global.avgResponseTime < 100 ? 'pass' : 'fail',
          value: stats.global.avgResponseTime,
          threshold: 100,
        },
      },
    };
  }

  @Get('keys/search')
  @ApiOperation({ summary: '搜索缓存键' })
  @ApiQuery({
    name: 'pattern',
    required: true,
    type: String,
    description: '搜索模式（支持通配符）',
  })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: '结果限制数量' })
  @ApiResponse({
    status: 200,
    description: '返回匹配的缓存键列表',
  })
  async searchCacheKeys(@Query('pattern') pattern: string, @Query('limit') limit: number = 100) {
    // 这里需要实现键搜索功能
    // 简化实现，实际需要在缓存策略中添加键搜索功能
    return {
      pattern,
      keys: [] as string[], // 实际应该返回匹配的键列表
      total: 0,
      limit,
    };
  }

  @Get('memory/usage')
  @ApiOperation({ summary: '获取内存使用情况' })
  @ApiResponse({
    status: 200,
    description: '返回内存使用统计',
  })
  async getMemoryUsage() {
    const stats = await this.advancedCacheService.getStats();
    const memUsage = process.memoryUsage();

    return {
      cache: {
        totalMemory: stats.strategies.reduce((sum, s) => sum + s.stats.memoryUsage, 0),
        strategies: stats.strategies.map((s) => ({
          name: s.name,
          memory: s.stats.memoryUsage,
          keys: s.stats.totalKeys,
        })),
      },
      process: {
        heapUsed: memUsage.heapUsed,
        heapTotal: memUsage.heapTotal,
        external: memUsage.external,
        rss: memUsage.rss,
      },
    };
  }

  /**
   * 根据数据源类型创建数据加载器
   */
  private createDataLoader(dataSource: string): (key: string) => Promise<any> {
    switch (dataSource) {
      case 'users':
        return async (key: string) => {
          // 这里应该调用用户服务获取数据
          return { type: 'user', key, timestamp: Date.now() };
        };
      case 'documents':
        return async (key: string) => {
          // 这里应该调用文档服务获取数据
          return { type: 'document', key, timestamp: Date.now() };
        };
      case 'analytics':
        return async (key: string) => {
          // 这里应该调用分析服务获取数据
          return { type: 'analytics', key, timestamp: Date.now() };
        };
      default:
        return async (key: string) => {
          return { type: 'unknown', key, timestamp: Date.now() };
        };
    }
  }
}
