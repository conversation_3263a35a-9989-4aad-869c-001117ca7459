import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseUUI<PERSON>ipe,
  HttpCode,
  HttpStatus,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiSecurity,
  ApiConsumes,
} from '@nestjs/swagger';
import { FileInterceptor } from '@/shared/interceptors/fastify-file.interceptor';
import { DocumentsService } from '@/modules/documents/services/documents.service';
import { CreateDocumentDto } from '@/modules/documents/dto/create-document.dto';
import { UpdateDocumentDto } from '@/modules/documents/dto/update-document.dto';
import {
  DocumentQueryDto,
  DocumentStatsQueryDto,
} from '@/modules/documents/dto/document-query.dto';
import {
  DocumentResponseDto,
  DocumentStatsDto,
  DocumentUploadDto,
} from '@/modules/documents/dto/document-response.dto';
import { PaginatedResponseDto } from '@/shared/dto/pagination.dto';
import { MessageResponseDto } from '@/shared/dto/response.dto';
import { DocumentStatus } from '@/shared/entities/document.entity';

@ApiTags('Admin - Documents')
@ApiSecurity('JWT-auth')
@Controller({ path: 'admin/documents', version: '1' })
export class AdminDocumentsController {
  constructor(private readonly documentsService: DocumentsService) {}

  @Post()
  @ApiOperation({ summary: '创建新文档' })
  @ApiResponse({
    status: 201,
    description: '文档创建成功',
    type: DocumentResponseDto,
  })
  async create(
    @Body() createDocumentDto: CreateDocumentDto,
    // TODO: 获取当前用户ID
    // @CurrentUser() user: User,
  ): Promise<DocumentResponseDto> {
    // 暂时使用硬编码的用户ID，实际应从JWT中获取
    const currentUserId = 'admin-user-id';

    const document = await this.documentsService.create(createDocumentDto, currentUserId);
    return document.toSafeObject() as DocumentResponseDto;
  }

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: '上传文档文件' })
  @ApiResponse({
    status: 201,
    description: '文档上传成功',
    type: DocumentResponseDto,
  })
  async uploadFile(
    @UploadedFile() file: unknown,
    @Body() uploadDto: DocumentUploadDto,
    // TODO: 获取当前用户ID
    // @CurrentUser() user: User,
  ): Promise<DocumentResponseDto> {
    if (!file) {
      throw new BadRequestException('请选择要上传的文件');
    }

    const currentUserId = 'admin-user-id';

    // 构造创建文档的DTO
    const createDocumentDto: CreateDocumentDto = {
      title: uploadDto.title || file.originalname,
      description: uploadDto.description,
      fileName: file.originalname,
      filePath: file.path || `/uploads/${file.filename}`,
      fileSize: file.size,
      mimeType: file.mimetype,
      type: uploadDto.type || this.getDocumentTypeFromMime(file.mimetype),
      accessLevel: uploadDto.accessLevel,
      tags: uploadDto.tags ? JSON.parse(uploadDto.tags) : [],
      metadata: uploadDto.metadata ? JSON.parse(uploadDto.metadata) : {},
    };

    const document = await this.documentsService.create(createDocumentDto, currentUserId);
    return document.toSafeObject() as DocumentResponseDto;
  }

  @Get()
  @ApiOperation({ summary: '获取文档列表（分页）' })
  @ApiResponse({
    status: 200,
    description: '返回分页文档列表',
    type: PaginatedResponseDto<DocumentResponseDto>,
  })
  async findAll(
    @Query() query: DocumentQueryDto,
    // TODO: 获取当前用户信息
    // @CurrentUser() user: User,
  ): Promise<PaginatedResponseDto<DocumentResponseDto>> {
    const currentUserId = 'admin-user-id';
    const userRole = 'ADMIN';

    return this.documentsService.findAll(query, currentUserId, userRole) as Promise<
      PaginatedResponseDto<DocumentResponseDto>
    >;
  }

  @Get('stats')
  @ApiOperation({ summary: '获取文档统计信息' })
  @ApiResponse({
    status: 200,
    description: '返回文档统计数据',
    type: DocumentStatsDto,
  })
  async getStats(@Query() query: DocumentStatsQueryDto): Promise<DocumentStatsDto> {
    return this.documentsService.getStats(query);
  }

  @Get('search')
  @ApiOperation({ summary: '搜索文档' })
  @ApiResponse({
    status: 200,
    description: '返回搜索结果',
    type: [DocumentResponseDto],
  })
  async search(
    @Query('q') searchTerm: string,
    @Query('limit') limit: number = 10,
    // TODO: 获取当前用户信息
    // @CurrentUser() user: User,
  ): Promise<DocumentResponseDto[]> {
    if (!searchTerm) {
      throw new BadRequestException('搜索关键词不能为空');
    }

    const currentUserId = 'admin-user-id';
    const userRole = 'ADMIN';

    const documents = await this.documentsService.search(
      searchTerm,
      currentUserId,
      userRole,
      limit,
    );
    return documents.map((doc) => doc.toSafeObject() as DocumentResponseDto);
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID获取文档详情' })
  @ApiParam({ name: 'id', description: '文档UUID' })
  @ApiResponse({
    status: 200,
    description: '返回文档详情',
    type: DocumentResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: '文档不存在',
  })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
    // TODO: 获取当前用户信息
    // @CurrentUser() user: User,
  ): Promise<DocumentResponseDto> {
    const currentUserId = 'admin-user-id';
    const userRole = 'ADMIN';

    const document = await this.documentsService.findOne(id, currentUserId, userRole);
    return document.toSafeObject() as DocumentResponseDto;
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新文档信息' })
  @ApiParam({ name: 'id', description: '文档UUID' })
  @ApiResponse({
    status: 200,
    description: '文档更新成功',
    type: DocumentResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: '文档不存在',
  })
  @ApiResponse({
    status: 403,
    description: '没有权限修改此文档',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDocumentDto: UpdateDocumentDto,
    // TODO: 获取当前用户信息
    // @CurrentUser() user: User,
  ): Promise<DocumentResponseDto> {
    const currentUserId = 'admin-user-id';
    const userRole = 'ADMIN';

    const document = await this.documentsService.update(
      id,
      updateDocumentDto,
      currentUserId,
      userRole,
    );
    return document.toSafeObject() as DocumentResponseDto;
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '删除文档' })
  @ApiParam({ name: 'id', description: '文档UUID' })
  @ApiResponse({
    status: 204,
    description: '文档删除成功',
  })
  @ApiResponse({
    status: 404,
    description: '文档不存在',
  })
  @ApiResponse({
    status: 403,
    description: '没有权限删除此文档',
  })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    // TODO: 获取当前用户信息
    // @CurrentUser() user: User,
  ): Promise<void> {
    const currentUserId = 'admin-user-id';
    const userRole = 'ADMIN';

    await this.documentsService.remove(id, currentUserId, userRole);
  }

  @Patch(':id/soft-delete')
  @ApiOperation({ summary: '软删除文档' })
  @ApiParam({ name: 'id', description: '文档UUID' })
  @ApiResponse({
    status: 200,
    description: '文档软删除成功',
    type: DocumentResponseDto,
  })
  async softDelete(
    @Param('id', ParseUUIDPipe) id: string,
    // TODO: 获取当前用户信息
    // @CurrentUser() user: User,
  ): Promise<DocumentResponseDto> {
    const currentUserId = 'admin-user-id';
    const userRole = 'ADMIN';

    const document = await this.documentsService.softDelete(id, currentUserId, userRole);
    return document.toSafeObject() as DocumentResponseDto;
  }

  @Patch(':id/view')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '增加文档浏览次数' })
  @ApiParam({ name: 'id', description: '文档UUID' })
  @ApiResponse({
    status: 200,
    description: '浏览次数更新成功',
    type: MessageResponseDto,
  })
  async incrementViewCount(@Param('id', ParseUUIDPipe) id: string): Promise<MessageResponseDto> {
    await this.documentsService.incrementViewCount(id);
    return new MessageResponseDto('浏览次数已更新');
  }

  @Patch(':id/download')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '增加文档下载次数' })
  @ApiParam({ name: 'id', description: '文档UUID' })
  @ApiResponse({
    status: 200,
    description: '下载次数更新成功',
    type: MessageResponseDto,
  })
  async incrementDownloadCount(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<MessageResponseDto> {
    await this.documentsService.incrementDownloadCount(id);
    return new MessageResponseDto('下载次数已更新');
  }

  @Post('batch/status')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '批量更新文档状态' })
  @ApiResponse({
    status: 200,
    description: '文档状态更新成功',
    type: MessageResponseDto,
  })
  async batchUpdateStatus(
    @Body() body: { ids: string[]; status: DocumentStatus },
    // TODO: 获取当前用户信息
    // @CurrentUser() user: User,
  ): Promise<MessageResponseDto> {
    const currentUserId = 'admin-user-id';
    const userRole = 'ADMIN';

    await this.documentsService.batchUpdateStatus(body.ids, body.status, currentUserId, userRole);
    return new MessageResponseDto(`成功更新 ${body.ids.length} 个文档的状态`);
  }

  @Delete('batch')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '批量删除文档' })
  @ApiResponse({
    status: 200,
    description: '文档删除成功',
    type: MessageResponseDto,
  })
  async batchRemove(
    @Body() body: { ids: string[] },
    // TODO: 获取当前用户信息
    // @CurrentUser() user: User,
  ): Promise<MessageResponseDto> {
    const currentUserId = 'admin-user-id';
    const userRole = 'ADMIN';

    await this.documentsService.batchRemove(body.ids, currentUserId, userRole);
    return new MessageResponseDto(`成功删除 ${body.ids.length} 个文档`);
  }

  // 私有方法：根据MIME类型判断文档类型
  private getDocumentTypeFromMime(mimeType: string): any {
    const mimeToTypeMap = {
      'application/pdf': 'CONTRACT',
      'application/msword': 'REPORT',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'REPORT',
      'application/vnd.ms-excel': 'SPREADSHEET',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'SPREADSHEET',
      'application/vnd.ms-powerpoint': 'PRESENTATION',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'PRESENTATION',
      'text/plain': 'MANUAL',
      'image/jpeg': 'IMAGE',
      'image/png': 'IMAGE',
      'image/gif': 'IMAGE',
      'video/mp4': 'VIDEO',
      'audio/mpeg': 'AUDIO',
    };

    return (mimeToTypeMap as any)[mimeType] || 'OTHER';
  }
}
