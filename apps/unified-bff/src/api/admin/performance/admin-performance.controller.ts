import { RequirePermissions } from '@/core/auth/decorators/permissions.decorator';
import { JwtAuthGuard } from '@/core/auth/guards/jwt-auth.guard';
import { PermissionsGuard } from '@/core/auth/guards/permissions.guard';
import { DatabaseMetricsService } from '@/core/performance/services/database-metrics.service';
import { IndexOptimizationService } from '@/core/performance/services/index-optimization.service';
import { QueryOptimizationService } from '@/core/performance/services/query-optimization.service';
import { Permission } from '@/shared/enums/permission.enum';
import {
  Body,
  Controller,
  Get,
  // Version,
  HttpCode,
  HttpStatus,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiSecurity, ApiTags } from '@nestjs/swagger';

@ApiTags('Admin - Performance')
@ApiSecurity('JWT-auth')
@Controller({ path: 'admin/performance', version: '1' })
@UseGuards(JwtAuthGuard, PermissionsGuard)
@RequirePermissions(Permission.SYSTEM_MONITOR)
export class AdminPerformanceController {
  constructor(
    private readonly databaseMetricsService: DatabaseMetricsService,
    private readonly indexOptimizationService: IndexOptimizationService,
    private readonly queryOptimizationService: QueryOptimizationService,
  ) {}

  @Get('database/metrics')
  @ApiOperation({ summary: '获取数据库性能指标' })
  @ApiResponse({
    status: 200,
    description: '返回数据库性能指标',
  })
  async getDatabaseMetrics() {
    return this.databaseMetricsService.getDatabaseMetrics();
  }

  @Get('database/slow-queries')
  @ApiOperation({ summary: '获取慢查询列表' })
  @ApiResponse({
    status: 200,
    description: '返回慢查询列表',
  })
  async getSlowQueries() {
    return this.databaseMetricsService.getSlowQueries();
  }

  @Post('database/slow-queries/clear')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '清除慢查询记录' })
  @ApiResponse({
    status: 200,
    description: '慢查询记录已清除',
  })
  async clearSlowQueries() {
    this.databaseMetricsService.clearSlowQueries();
    return { message: '慢查询记录已清除' };
  }

  @Get('database/report')
  @ApiOperation({ summary: '生成数据库性能报告' })
  @ApiResponse({
    status: 200,
    description: '返回数据库性能报告',
  })
  async generatePerformanceReport() {
    return this.databaseMetricsService.generatePerformanceReport();
  }

  @Get('indexes/analysis')
  @ApiOperation({ summary: '分析索引使用情况' })
  @ApiResponse({
    status: 200,
    description: '返回索引分析结果',
  })
  async analyzeIndexes() {
    return this.indexOptimizationService.analyzeIndexes();
  }

  @Get('indexes/recommendations')
  @ApiOperation({ summary: '获取索引优化建议' })
  @ApiResponse({
    status: 200,
    description: '返回索引优化建议',
  })
  async getIndexRecommendations() {
    return this.indexOptimizationService.getIndexRecommendations();
  }

  @Post('indexes/create')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '创建推荐的索引' })
  @ApiResponse({
    status: 200,
    description: '索引创建结果',
  })
  async createRecommendedIndex(
    @Body() body: { tableName: string; recommendedIndex: string; sql: string },
  ) {
    const success = await this.indexOptimizationService.createRecommendedIndex({
      tableName: body.tableName,
      recommendedIndex: body.recommendedIndex,
      reason: 'Manual creation via API',
      estimatedImpact: 'MEDIUM',
      sql: body.sql,
    });

    return {
      success,
      message: success ? '索引创建成功' : '索引创建失败',
    };
  }

  @Post('indexes/cleanup')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '清理未使用的索引' })
  @ApiQuery({ name: 'dryRun', required: false, type: Boolean, description: '是否为模拟运行' })
  @ApiResponse({
    status: 200,
    description: '返回清理结果',
  })
  async removeUnusedIndexes(@Query('dryRun') dryRun: boolean = true) {
    const removedIndexes = await this.indexOptimizationService.removeUnusedIndexes(dryRun);

    return {
      dryRun,
      removedCount: removedIndexes.length,
      removedIndexes,
      message: dryRun
        ? `发现 ${removedIndexes.length} 个可删除的索引（模拟运行）`
        : `成功删除 ${removedIndexes.length} 个索引`,
    };
  }

  @Post('indexes/rebuild-stats')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '重建索引统计信息' })
  @ApiQuery({ name: 'tableName', required: false, type: String, description: '指定表名（可选）' })
  @ApiResponse({
    status: 200,
    description: '统计信息重建完成',
  })
  async rebuildIndexStatistics(@Query('tableName') tableName?: string) {
    await this.indexOptimizationService.rebuildIndexStatistics(tableName);

    return {
      message: tableName ? `表 ${tableName} 的统计信息已重建` : '所有表的统计信息已重建',
    };
  }

  @Get('indexes/maintenance')
  @ApiOperation({ summary: '获取索引维护建议' })
  @ApiResponse({
    status: 200,
    description: '返回索引维护建议',
  })
  async getMaintenanceRecommendations() {
    return this.indexOptimizationService.getMaintenanceRecommendations();
  }

  @Get('query/suggestions')
  @ApiOperation({ summary: '获取查询性能建议' })
  @ApiQuery({ name: 'entity', required: true, type: String, description: '实体名称' })
  @ApiResponse({
    status: 200,
    description: '返回查询性能建议',
  })
  async getQueryPerformanceSuggestions(@Query('entity') entity: string) {
    return this.queryOptimizationService.getQueryPerformanceSuggestions(entity);
  }
}
