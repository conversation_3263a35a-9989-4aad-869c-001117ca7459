import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsNumber, IsBoolean, Min, Max } from 'class-validator';

export class AskQuestionDto {
  @ApiProperty({
    description: '用户问题',
    example: '公司的休假政策是什么？',
  })
  @IsString()
  question!: string;

  @ApiProperty({
    description: '搜索的文档集合名称',
    example: 'company-docs',
  })
  @IsString()
  collectionName!: string;

  @ApiProperty({
    description: '返回最相关的文档数量',
    example: 5,
    minimum: 1,
    maximum: 20,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(20)
  topK?: number;

  @ApiProperty({
    description: '最小相似度分数阈值',
    example: 0.5,
    minimum: 0,
    maximum: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  minScore?: number;

  @ApiProperty({
    description: '是否包含上下文信息',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  includeContext?: boolean;

  @ApiProperty({
    description: '上下文窗口大小（字符数）',
    example: 2000,
    minimum: 100,
    maximum: 8000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(100)
  @Max(8000)
  contextWindow?: number;

  @ApiProperty({
    description: '生成温度',
    example: 0.3,
    minimum: 0,
    maximum: 2,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(2)
  temperature?: number;

  @ApiProperty({
    description: '最大生成令牌数',
    example: 500,
    minimum: 50,
    maximum: 2000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(50)
  @Max(2000)
  maxTokens?: number;
}
