import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsNumber, IsBoolean, IsObject, Min, Max } from 'class-validator';

export class SmartSearchDto {
  @ApiProperty({
    description: '搜索查询',
    example: '公司的休假政策',
  })
  @IsString()
  query!: string;

  @ApiProperty({
    description: '搜索的文档集合名称',
    example: 'company-docs',
  })
  @IsString()
  collectionName!: string;

  @ApiProperty({
    description: '返回结果数量',
    example: 10,
    minimum: 1,
    maximum: 50,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(50)
  topK?: number;

  @ApiProperty({
    description: '最小相似度分数',
    example: 0.3,
    minimum: 0,
    maximum: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  minScore?: number;

  @ApiProperty({
    description: '启用语义搜索',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  enableSemanticSearch?: boolean;

  @ApiProperty({
    description: '启用关键词搜索',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  enableKeywordSearch?: boolean;

  @ApiProperty({
    description: '语义搜索权重',
    example: 0.7,
    minimum: 0,
    maximum: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  semanticWeight?: number;

  @ApiProperty({
    description: '关键词搜索权重',
    example: 0.3,
    minimum: 0,
    maximum: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  keywordWeight?: number;

  @ApiProperty({
    description: '搜索过滤条件',
    example: {
      category: 'policy',
      author: 'HR部门',
    },
    required: false,
  })
  @IsOptional()
  @IsObject()
  filters?: Record<string, unknown>;

  @ApiProperty({
    description: '是否扩展查询',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  expandQuery?: boolean;

  @ApiProperty({
    description: '是否包含摘要片段',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  includeSnippets?: boolean;

  @ApiProperty({
    description: '摘要片段长度',
    example: 150,
    minimum: 50,
    maximum: 500,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(50)
  @Max(500)
  snippetLength?: number;
}
