import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsNumber,
  IsBoolean,
  IsArray,
  IsIn,
  Min,
  Max,
} from 'class-validator';

export class GenerateSummaryDto {
  @ApiProperty({
    description: '要摘要的内容',
    example: '这是一篇关于人工智能发展的长篇文章...',
  })
  @IsString()
  content!: string;

  @ApiProperty({
    description: '摘要最大长度（字符数）',
    example: 200,
    minimum: 50,
    maximum: 1000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(50)
  @Max(1000)
  maxLength?: number;

  @ApiProperty({
    description: '摘要风格',
    enum: ['concise', 'detailed', 'bullet', 'outline'],
    example: 'concise',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsIn(['concise', 'detailed', 'bullet', 'outline'])
  style?: 'concise' | 'detailed' | 'bullet' | 'outline';

  @ApiProperty({
    description: '重点关注的关键词',
    example: ['人工智能', '机器学习', '深度学习'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  focusKeywords?: string[];

  @ApiProperty({
    description: '是否包含关键要点',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  includeKeyPoints?: boolean;

  @ApiProperty({
    description: '是否包含统计信息',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  includeStatistics?: boolean;

  @ApiProperty({
    description: '语言',
    enum: ['zh', 'en'],
    example: 'zh',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsIn(['zh', 'en'])
  language?: 'zh' | 'en';

  @ApiProperty({
    description: '自定义提示模板',
    example: '请为以下内容生成一个专业的技术摘要：{content}',
    required: false,
  })
  @IsOptional()
  @IsString()
  customPrompt?: string;
}
