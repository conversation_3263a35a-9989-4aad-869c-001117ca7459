import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsString,
  IsOptional,
  IsNumber,
  Min,
  Max,
  IsIn,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

class ChatMessage {
  @ApiProperty({
    description: '消息角色',
    enum: ['user', 'assistant', 'system'],
    example: 'user',
  })
  @IsString()
  @IsIn(['user', 'assistant', 'system'])
  role!: 'user' | 'assistant' | 'system';

  @ApiProperty({
    description: '消息内容',
    example: '你好，请介绍一下人工智能的发展历史。',
  })
  @IsString()
  content!: string;
}

export class CreateChatDto {
  @ApiProperty({
    description: '对话消息列表',
    type: [ChatMessage],
    example: [
      {
        role: 'user',
        content: '你好，请介绍一下人工智能的发展历史。',
      },
    ],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ChatMessage)
  messages!: ChatMessage[];

  @ApiProperty({
    description: '生成温度，控制随机性 (0.0-2.0)',
    example: 0.7,
    minimum: 0,
    maximum: 2,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(2)
  temperature?: number;

  @ApiProperty({
    description: '最大生成令牌数',
    example: 500,
    minimum: 1,
    maximum: 4000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(4000)
  maxTokens?: number;
}
