import { CurrentUser } from '@/core/auth/decorators/current-user.decorator';
import { Roles } from '@/core/auth/decorators/roles.decorator';
import { JwtAuthGuard } from '@/core/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@/core/auth/guards/roles.guard';
import { UserRole } from '@/shared/enums/user-role.enum';
import { FileInterceptor } from '@/shared/interceptors/fastify-file.interceptor';
import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Query,
  Sse,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiConsumes, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import * as fs from 'fs';
import { Observable } from 'rxjs';
import { AskQuestionDto } from '../dto/ask-question.dto';
import { CreateChatDto } from '../dto/create-chat.dto';
import { CreateCompletionDto } from '../dto/create-completion.dto';
import { GenerateSummaryDto } from '../dto/generate-summary.dto';
import { ProcessDocumentDto } from '../dto/process-document.dto';
import { SmartSearchDto } from '../dto/smart-search.dto';
import { AIService } from '../services/ai.service';
import { DocumentEmbeddingService } from '../services/document-embedding.service';
import { QAOptions, QAService } from '../services/qa.service';
import { SearchService, SmartSearchOptions } from '../services/search.service';
import { SummaryOptions, SummaryService } from '../services/summary.service';
import { VectorStoreService } from '../services/vector-store.service';

@ApiTags('AI')
@Controller('ai')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class AIController {
  constructor(
    private readonly aiService: AIService,
    private readonly embeddingService: DocumentEmbeddingService,
    private readonly vectorStoreService: VectorStoreService,
    private readonly qaService: QAService,
    private readonly summaryService: SummaryService,
    private readonly searchService: SearchService,
  ) {}

  @Post('completion')
  @ApiOperation({ summary: '生成AI文本补全' })
  @ApiResponse({ status: 200, description: '成功生成文本补全' })
  @Roles(UserRole.USER, UserRole.ADMIN)
  async generateCompletion(
    @Body() createCompletionDto: CreateCompletionDto,
    @CurrentUser() _user: { id: string; role: string },
  ) {
    try {
      const result = await this.aiService.generateCompletion(createCompletionDto.prompt, {
        temperature: createCompletionDto.temperature,
        maxTokens: createCompletionDto.maxTokens,
        systemMessage: createCompletionDto.systemMessage,
      });

      return {
        success: true,
        data: {
          completion: result,
          usage: {
            promptTokens: createCompletionDto.prompt.length,
            completionTokens: result.length,
          },
        },
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new HttpException(
        `Failed to generate completion: ${errorMessage}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('chat')
  @ApiOperation({ summary: '生成对话回复' })
  @ApiResponse({ status: 200, description: '成功生成对话回复' })
  @Roles(UserRole.USER, UserRole.ADMIN)
  async generateChat(
    @Body() createChatDto: CreateChatDto,
    @CurrentUser() _user: { id: string; role: string },
  ) {
    try {
      const result = await this.aiService.generateChatResponse(createChatDto.messages, {
        temperature: createChatDto.temperature,
        maxTokens: createChatDto.maxTokens,
      });

      return {
        success: true,
        data: {
          response: result,
          conversation: [...createChatDto.messages, { role: 'assistant', content: result }],
        },
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new HttpException(
        `Failed to generate chat response: ${errorMessage}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Sse('chat/stream')
  @ApiOperation({ summary: '流式对话生成' })
  @Roles(UserRole.USER, UserRole.ADMIN)
  async streamChat(
    @Query('prompt') prompt: string,
    @Query('systemMessage') systemMessage?: string,
    @Query('temperature') temperature?: number,
    @Query('maxTokens') maxTokens?: number,
  ): Promise<Observable<MessageEvent>> {
    try {
      const streamGenerator = this.aiService.generateStream(prompt, {
        systemMessage,
        temperature: temperature ? parseFloat(temperature.toString()) : undefined,
        maxTokens: maxTokens ? parseInt(maxTokens.toString()) : undefined,
      });

      return new Observable((observer) => {
        (async () => {
          try {
            for await (const chunk of streamGenerator) {
              observer.next({
                data: JSON.stringify({ chunk }),
              } as MessageEvent);
            }
            observer.complete();
          } catch (error) {
            observer.error(error);
          }
        })();
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new HttpException(
        `Failed to start chat stream: ${errorMessage}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('documents/process')
  @ApiOperation({ summary: '处理文档并创建嵌入向量' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  @Roles(UserRole.USER, UserRole.ADMIN)
  async processDocument(
    @UploadedFile() file: { originalname: string; buffer: Buffer },
    @Body() processDocumentDto: ProcessDocumentDto,
    @CurrentUser() user: { id: string; role: string },
  ) {
    try {
      let chunks;

      if (file) {
        // 处理上传的文件
        const tempFilePath = `/tmp/${file.originalname}`;
        fs.writeFileSync(tempFilePath, file.buffer);

        chunks = await this.embeddingService.processDocumentFromPath(tempFilePath, {
          originalName: file.originalname,
          uploadedBy: user.id,
          ...processDocumentDto.metadata,
        });

        // 清理临时文件
        fs.unlinkSync(tempFilePath);
      } else if (processDocumentDto.content) {
        // 处理文本内容
        chunks = await this.embeddingService.processDocumentContent(processDocumentDto.content, {
          uploadedBy: user.id,
          ...processDocumentDto.metadata,
        });
      } else {
        throw new HttpException('Either file or content must be provided', HttpStatus.BAD_REQUEST);
      }

      // 生成嵌入向量
      const chunksWithEmbeddings = await this.embeddingService.generateEmbeddings(chunks);

      // 存储到向量数据库
      await this.vectorStoreService.addDocuments(
        processDocumentDto.collectionName,
        chunksWithEmbeddings,
      );

      return {
        success: true,
        data: {
          documentId: chunks[0]?.id,
          chunksCount: chunks.length,
          collectionName: processDocumentDto.collectionName,
          processedAt: new Date().toISOString(),
        },
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new HttpException(
        `Failed to process document: ${errorMessage}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('qa/ask')
  @ApiOperation({ summary: '文档智能问答' })
  @ApiResponse({ status: 200, description: '成功获得问答结果' })
  @Roles(UserRole.USER, UserRole.ADMIN)
  async askQuestion(
    @Body() askQuestionDto: AskQuestionDto,
    @CurrentUser() _user: { id: string; role: string },
  ) {
    try {
      const options: QAOptions = {
        collectionName: askQuestionDto.collectionName,
        topK: askQuestionDto.topK,
        minScore: askQuestionDto.minScore,
        includeContext: askQuestionDto.includeContext,
        contextWindow: askQuestionDto.contextWindow,
        temperature: askQuestionDto.temperature,
        maxTokens: askQuestionDto.maxTokens,
      };

      const result = await this.qaService.askQuestion(askQuestionDto.question, options);

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new HttpException(
        `Failed to answer question: ${errorMessage}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('qa/conversation')
  @ApiOperation({ summary: '对话式问答' })
  @ApiResponse({ status: 200, description: '成功获得对话回复' })
  @Roles(UserRole.USER, UserRole.ADMIN)
  async conversationalQA(
    @Body()
    body: {
      question: string;
      conversationHistory: Array<{ role: 'user' | 'assistant'; content: string }>;
      collectionName: string;
      options?: Partial<QAOptions>;
    },
    @CurrentUser() _user: { id: string; role: string },
  ) {
    try {
      const options: QAOptions = {
        collectionName: body.collectionName,
        ...body.options,
      };

      const result = await this.qaService.conversationalQA(
        body.question,
        body.conversationHistory,
        options,
      );

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new HttpException(
        `Failed to process conversational QA: ${errorMessage}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('summary/generate')
  @ApiOperation({ summary: '生成文档摘要' })
  @ApiResponse({ status: 200, description: '成功生成摘要' })
  @Roles(UserRole.USER, UserRole.ADMIN)
  async generateSummary(
    @Body() generateSummaryDto: GenerateSummaryDto,
    @CurrentUser() _user: { id: string; role: string },
  ) {
    try {
      const options: SummaryOptions = {
        maxLength: generateSummaryDto.maxLength,
        style: generateSummaryDto.style,
        focusKeywords: generateSummaryDto.focusKeywords,
        includeKeyPoints: generateSummaryDto.includeKeyPoints,
        includeStatistics: generateSummaryDto.includeStatistics,
        language: generateSummaryDto.language,
        customPrompt: generateSummaryDto.customPrompt,
      };

      const result = await this.summaryService.generateSummary(generateSummaryDto.content, options);

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new HttpException(
        `Failed to generate summary: ${errorMessage}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('summary/analyze')
  @ApiOperation({ summary: '生成摘要并分析内容' })
  @ApiResponse({ status: 200, description: '成功生成摘要和分析' })
  @Roles(UserRole.USER, UserRole.ADMIN)
  async summarizeAndAnalyze(
    @Body() body: { content: string; options?: SummaryOptions },
    @CurrentUser() _user: { id: string; role: string },
  ) {
    try {
      const result = await this.summaryService.summarizeAndAnalyze(
        body.content,
        body.options || {},
      );

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new HttpException(
        `Failed to summarize and analyze: ${errorMessage}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('search/smart')
  @ApiOperation({ summary: '智能搜索' })
  @ApiResponse({ status: 200, description: '成功执行智能搜索' })
  @Roles(UserRole.USER, UserRole.ADMIN)
  async smartSearch(
    @Body() smartSearchDto: SmartSearchDto,
    @CurrentUser() _user: { id: string; role: string },
  ) {
    try {
      const options: SmartSearchOptions = {
        collectionName: smartSearchDto.collectionName,
        topK: smartSearchDto.topK,
        minScore: smartSearchDto.minScore,
        enableSemanticSearch: smartSearchDto.enableSemanticSearch,
        enableKeywordSearch: smartSearchDto.enableKeywordSearch,
        semanticWeight: smartSearchDto.semanticWeight,
        keywordWeight: smartSearchDto.keywordWeight,
        filters: smartSearchDto.filters,
        expandQuery: smartSearchDto.expandQuery,
        includeSnippets: smartSearchDto.includeSnippets,
        snippetLength: smartSearchDto.snippetLength,
      };

      const result = await this.searchService.smartSearch(smartSearchDto.query, options);

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new HttpException(
        `Failed to perform smart search: ${errorMessage}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('search/suggestions')
  @ApiOperation({ summary: '获取搜索建议' })
  @ApiResponse({ status: 200, description: '成功获取搜索建议' })
  @Roles(UserRole.USER, UserRole.ADMIN)
  async getSearchSuggestions(
    @Query('query') query: string,
    @Query('collection') collectionName: string,
    @Query('limit') limit?: number,
  ) {
    try {
      const suggestions = await this.searchService.getSearchSuggestions(
        query,
        collectionName,
        limit ? parseInt(limit.toString()) : 5,
      );

      return {
        success: true,
        data: { suggestions },
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new HttpException(
        `Failed to get search suggestions: ${errorMessage}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('search/similar/:documentId')
  @ApiOperation({ summary: '查找相似文档' })
  @ApiResponse({ status: 200, description: '成功找到相似文档' })
  @Roles(UserRole.USER, UserRole.ADMIN)
  async findSimilarDocuments(
    @Param('documentId') documentId: string,
    @Query('collection') collectionName: string,
    @Query('limit') limit?: number,
  ) {
    try {
      const results = await this.searchService.findSimilarDocuments(
        documentId,
        collectionName,
        limit ? parseInt(limit.toString()) : 5,
      );

      return {
        success: true,
        data: { results },
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new HttpException(
        `Failed to find similar documents: ${errorMessage}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('collections')
  @ApiOperation({ summary: '获取所有集合列表' })
  @ApiResponse({ status: 200, description: '成功获取集合列表' })
  @Roles(UserRole.USER, UserRole.ADMIN)
  async listCollections() {
    try {
      const collections = await this.vectorStoreService.listCollections();

      return {
        success: true,
        data: { collections },
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new HttpException(
        `Failed to list collections: ${errorMessage}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('collections/:name/stats')
  @ApiOperation({ summary: '获取集合统计信息' })
  @ApiResponse({ status: 200, description: '成功获取集合统计' })
  @Roles(UserRole.USER, UserRole.ADMIN)
  async getCollectionStats(@Param('name') collectionName: string) {
    try {
      const stats = await this.vectorStoreService.getCollectionStats(collectionName);

      return {
        success: true,
        data: stats,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new HttpException(
        `Failed to get collection stats: ${errorMessage}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('search/analytics')
  @ApiOperation({ summary: '获取搜索分析数据' })
  @ApiResponse({ status: 200, description: '成功获取搜索分析' })
  @Roles(UserRole.ADMIN)
  async getSearchAnalytics(@Query('from') from?: string, @Query('to') to?: string) {
    try {
      const timeRange =
        from && to
          ? {
              from: new Date(from),
              to: new Date(to),
            }
          : undefined;

      const analytics = this.searchService.getSearchAnalytics(timeRange);

      return {
        success: true,
        data: analytics,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new HttpException(
        `Failed to get search analytics: ${errorMessage}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('health')
  @ApiOperation({ summary: '检查AI服务健康状态' })
  @ApiResponse({ status: 200, description: 'AI服务健康状态' })
  @Roles(UserRole.USER, UserRole.ADMIN)
  async checkHealth() {
    try {
      const [aiHealthy, vectorStoreHealthy] = await Promise.all([
        this.aiService.isHealthy(),
        this.vectorStoreService.isHealthy(),
      ]);

      const modelInfo = this.aiService.getModelInfo();
      const embeddingInfo = this.embeddingService.getEmbeddingInfo();

      return {
        success: true,
        data: {
          status: aiHealthy && vectorStoreHealthy ? 'healthy' : 'unhealthy',
          services: {
            ai: aiHealthy,
            vectorStore: vectorStoreHealthy,
          },
          configuration: {
            ai: modelInfo,
            embedding: embeddingInfo,
          },
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new HttpException(
        `Failed to check AI health: ${errorMessage}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
