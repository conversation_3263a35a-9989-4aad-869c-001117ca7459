import { OpenAIEmbeddings } from '@langchain/openai';
import { RecursiveCharacterTextSplitter } from '@langchain/textsplitters';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs/promises';
import * as mammoth from 'mammoth';
import * as path from 'path';
import pdfParse from 'pdf-parse';

export interface DocumentChunk {
  id: string;
  content: string;
  metadata: {
    source: string;
    chunk: number;
    totalChunks: number;
    startChar: number;
    endChar: number;
    [key: string]: unknown;
  };
  embedding?: number[];
}

@Injectable()
export class DocumentEmbeddingService {
  private readonly logger = new Logger(DocumentEmbeddingService.name);
  private readonly embeddings: OpenAIEmbeddings;
  private readonly textSplitter: RecursiveCharacterTextSplitter;

  constructor(private readonly configService: ConfigService) {
    const apiKey = this.configService.get<string>('OPENAI_API_KEY');

    this.embeddings = new OpenAIEmbeddings({
      openAIApiKey: apiKey,
      modelName: this.configService.get<string>('OPENAI_EMBEDDING_MODEL', 'text-embedding-ada-002'),
      batchSize: this.configService.get<number>('EMBEDDING_BATCH_SIZE', 1000),
    });

    this.textSplitter = new RecursiveCharacterTextSplitter({
      chunkSize: this.configService.get<number>('CHUNK_SIZE', 1000),
      chunkOverlap: this.configService.get<number>('CHUNK_OVERLAP', 200),
      separators: ['\n\n', '\n', '.', '!', '?', ';', ',', ' ', ''],
    });
  }

  /**
   * 从文件路径处理文档
   */
  async processDocumentFromPath(
    filePath: string,
    metadata: Record<string, unknown> = {},
  ): Promise<DocumentChunk[]> {
    try {
      const content = await this.extractTextFromFile(filePath);
      const fileName = path.basename(filePath);

      return await this.processDocumentContent(content, {
        source: fileName,
        filePath,
        ...metadata,
      });
    } catch {
      this.logger.error(`Error processing document ${filePath}:`);
      throw new Error(`Failed to process document: ${filePath}`);
    }
  }

  /**
   * 处理文档内容
   */
  async processDocumentContent(
    content: string,
    metadata: Record<string, unknown> = {},
  ): Promise<DocumentChunk[]> {
    try {
      // 分割文档
      const documents = await this.textSplitter.createDocuments([content], [metadata]);

      // 生成文档块
      const chunks: DocumentChunk[] = documents.map((doc, index) => ({
        id: this.generateChunkId(
          typeof metadata['source'] === 'string' ? metadata['source'] : 'document',
          index,
        ),
        content: doc.pageContent,
        metadata: {
          source: typeof metadata['source'] === 'string' ? metadata['source'] : 'document',
          ...doc.metadata,
          chunk: index,
          totalChunks: documents.length,
          startChar: 0, // 这里可以根据需要计算实际位置
          endChar: doc.pageContent.length,
        },
      }));

      this.logger.log(`Created ${chunks.length} chunks from document`);
      return chunks;
    } catch {
      this.logger.error('Error processing document content:');
      throw new Error('Failed to process document content');
    }
  }

  /**
   * 为文档块生成嵌入向量
   */
  async generateEmbeddings(chunks: DocumentChunk[]): Promise<DocumentChunk[]> {
    try {
      const contents = chunks.map((chunk) => chunk.content);
      const embeddings = await this.embeddings.embedDocuments(contents);

      return chunks.map((chunk, index) => ({
        ...chunk,
        embedding: embeddings[index],
      }));
    } catch {
      this.logger.error('Error generating embeddings:');
      throw new Error('Failed to generate embeddings');
    }
  }

  /**
   * 为查询生成嵌入向量
   */
  async generateQueryEmbedding(query: string): Promise<number[]> {
    try {
      return await this.embeddings.embedQuery(query);
    } catch {
      this.logger.error('Error generating query embedding:');
      throw new Error('Failed to generate query embedding');
    }
  }

  /**
   * 批量处理多个文档
   */
  async processMultipleDocuments(
    documents: Array<{
      content?: string;
      filePath?: string;
      metadata?: Record<string, unknown>;
    }>,
  ): Promise<DocumentChunk[]> {
    const allChunks: DocumentChunk[] = [];

    for (const doc of documents) {
      try {
        let chunks: DocumentChunk[];

        if (doc.filePath) {
          chunks = await this.processDocumentFromPath(doc.filePath, doc.metadata);
        } else if (doc.content) {
          chunks = await this.processDocumentContent(doc.content, doc.metadata);
        } else {
          this.logger.warn('Document has neither content nor filePath, skipping');
          continue;
        }

        allChunks.push(...chunks);
      } catch {
        this.logger.error(`Error processing document:`);
        // 继续处理其他文档，不中断整个批处理
      }
    }

    return allChunks;
  }

  /**
   * 从文件提取文本内容
   */
  private async extractTextFromFile(filePath: string): Promise<string> {
    const ext = path.extname(filePath).toLowerCase();

    switch (ext) {
      case '.txt':
      case '.md':
        return await fs.readFile(filePath, 'utf-8');

      case '.pdf':
        return await this.extractTextFromPDF(filePath);

      case '.docx':
        return await this.extractTextFromDocx(filePath);

      default:
        // 尝试作为纯文本读取
        try {
          return await fs.readFile(filePath, 'utf-8');
        } catch {
          throw new Error(`Unsupported file type: ${ext}`);
        }
    }
  }

  /**
   * 从PDF提取文本
   */
  private async extractTextFromPDF(filePath: string): Promise<string> {
    try {
      const buffer = await fs.readFile(filePath);
      const data = await pdfParse(buffer);
      return data.text;
    } catch {
      this.logger.error(`Error extracting text from PDF ${filePath}:`);
      throw new Error('Failed to extract text from PDF');
    }
  }

  /**
   * 从DOCX提取文本
   */
  private async extractTextFromDocx(filePath: string): Promise<string> {
    try {
      const buffer = await fs.readFile(filePath);
      const result = await mammoth.extractRawText({ buffer });
      return result.value;
    } catch {
      this.logger.error(`Error extracting text from DOCX ${filePath}:`);
      throw new Error('Failed to extract text from DOCX');
    }
  }

  /**
   * 生成文档块ID
   */
  private generateChunkId(source: string, chunkIndex: number): string {
    const timestamp = Date.now();
    return `${source}-${chunkIndex}-${timestamp}`;
  }

  /**
   * 计算文本相似度
   */
  async calculateSimilarity(text1: string, text2: string): Promise<number> {
    try {
      const [embedding1, embedding2] = await Promise.all([
        this.embeddings.embedQuery(text1),
        this.embeddings.embedQuery(text2),
      ]);

      return this.cosineSimilarity(embedding1, embedding2);
    } catch {
      this.logger.error('Error calculating similarity:');
      throw new Error('Failed to calculate similarity');
    }
  }

  /**
   * 计算余弦相似度
   */
  private cosineSimilarity(vecA: number[], vecB: number[]): number {
    if (vecA.length !== vecB.length) {
      throw new Error('Vectors must have the same length');
    }

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < vecA.length; i++) {
      const a = vecA[i] ?? 0;
      const b = vecB[i] ?? 0;
      dotProduct += a * b;
      normA += a * a;
      normB += b * b;
    }

    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
  }

  /**
   * 获取嵌入配置信息
   */
  getEmbeddingInfo(): {
    model: string;
    chunkSize: number;
    chunkOverlap: number;
    batchSize: number;
  } {
    return {
      model: this.configService.get<string>('OPENAI_EMBEDDING_MODEL', 'text-embedding-ada-002'),
      chunkSize: this.configService.get<number>('CHUNK_SIZE', 1000),
      chunkOverlap: this.configService.get<number>('CHUNK_OVERLAP', 200),
      batchSize: this.configService.get<number>('EMBEDDING_BATCH_SIZE', 1000),
    };
  }
}
