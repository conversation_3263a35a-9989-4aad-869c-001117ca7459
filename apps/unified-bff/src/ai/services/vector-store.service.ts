import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ChromaClient, Collection } from 'chromadb';
import { DocumentChunk } from './document-embedding.service';

export interface SearchResult {
  chunk: DocumentChunk;
  score: number;
  distance: number;
}

@Injectable()
export class VectorStoreService implements OnModuleInit {
  private readonly logger = new Logger(VectorStoreService.name);
  private chromaClient!: ChromaClient;
  private readonly collections: Map<string, Collection> = new Map();

  constructor(private readonly configService: ConfigService) {}

  async onModuleInit() {
    try {
      const { ChromaClient } = await import('chromadb');

      this.chromaClient = new ChromaClient({
        path: this.configService.get<string>('CHROMA_URL', 'http://localhost:8000'),
      });

      this.logger.log('Vector store service initialized');
    } catch {
      this.logger.error('Failed to initialize vector store:');
      // 不抛出错误，允许服务启动但功能受限
    }
  }

  /**
   * 创建或获取集合
   */
  async getOrCreateCollection(
    name: string,
    metadata?: Record<string, unknown>,
  ): Promise<Collection> {
    try {
      if (this.collections.has(name)) {
        const collection = this.collections.get(name);
        if (collection) {
          return collection;
        }
      }

      const collection = await this.chromaClient.getOrCreateCollection({
        name,
        metadata: {
          description: `Document collection: ${name}`,
          created: new Date().toISOString(),
          ...metadata,
        },
      });

      this.collections.set(name, collection);
      this.logger.log(`Collection '${name}' created/retrieved`);

      return collection;
    } catch {
      this.logger.error(`Error creating collection ${name}:`);
      throw new Error(`Failed to create collection: ${name}`);
    }
  }

  /**
   * 添加文档块到向量存储
   */
  async addDocuments(collectionName: string, chunks: DocumentChunk[]): Promise<void> {
    try {
      const collection = await this.getOrCreateCollection(collectionName);

      const validChunks = chunks.filter((chunk) => chunk.embedding && chunk.embedding.length > 0);

      if (validChunks.length === 0) {
        this.logger.warn('No valid chunks with embeddings to add');
        return;
      }

      await collection.add({
        ids: validChunks.map((chunk) => chunk.id),
        embeddings: validChunks.map((chunk) => chunk.embedding as number[]),
        documents: validChunks.map((chunk) => chunk.content),
        metadatas: validChunks.map((chunk) => chunk.metadata),
      });

      this.logger.log(`Added ${validChunks.length} documents to collection '${collectionName}'`);
    } catch {
      this.logger.error(`Error adding documents to collection ${collectionName}:`);
      throw new Error(`Failed to add documents to collection: ${collectionName}`);
    }
  }

  /**
   * 更新文档块
   */
  async updateDocuments(collectionName: string, chunks: DocumentChunk[]): Promise<void> {
    try {
      const collection = await this.getOrCreateCollection(collectionName);

      const validChunks = chunks.filter((chunk) => chunk.embedding && chunk.embedding.length > 0);

      if (validChunks.length === 0) {
        this.logger.warn('No valid chunks with embeddings to update');
        return;
      }

      await collection.update({
        ids: validChunks.map((chunk) => chunk.id),
        embeddings: validChunks.map((chunk) => chunk.embedding as number[]),
        documents: validChunks.map((chunk) => chunk.content),
        metadatas: validChunks.map((chunk) => chunk.metadata),
      });

      this.logger.log(`Updated ${validChunks.length} documents in collection '${collectionName}'`);
    } catch {
      this.logger.error(`Error updating documents in collection ${collectionName}:`);
      throw new Error(`Failed to update documents in collection: ${collectionName}`);
    }
  }

  /**
   * 删除文档块
   */
  async deleteDocuments(collectionName: string, documentIds: string[]): Promise<void> {
    try {
      const collection = await this.getOrCreateCollection(collectionName);

      await collection.delete({
        ids: documentIds,
      });

      this.logger.log(
        `Deleted ${documentIds.length} documents from collection '${collectionName}'`,
      );
    } catch {
      this.logger.error(`Error deleting documents from collection ${collectionName}:`);
      throw new Error(`Failed to delete documents from collection: ${collectionName}`);
    }
  }

  /**
   * 向量相似性搜索
   */
  async similaritySearch(
    collectionName: string,
    queryEmbedding: number[],
    options: {
      topK?: number;
      filter?: Record<string, any>;
      minScore?: number;
    } = {},
  ): Promise<SearchResult[]> {
    try {
      const collection = await this.getOrCreateCollection(collectionName);

      const { topK = 5, filter, minScore = 0 } = options;

      const results = await collection.query({
        queryEmbeddings: [queryEmbedding],
        nResults: topK,
        where: filter,
      });

      if (!results.ids || !results.ids[0] || results.ids[0].length === 0) {
        return [];
      }

      const searchResults: SearchResult[] = [];

      for (let i = 0; i < results.ids[0].length; i++) {
        const distance = results.distances?.[0]?.[i] ?? 1;
        const score = 1 - distance; // 转换距离为相似度分数

        if (score >= minScore) {
          searchResults.push({
            chunk: {
              id: results.ids[0][i],
              content: results.documents?.[0]?.[i] ?? '',
              metadata: {
                source: (results.metadatas?.[0]?.[i] as any)?.source ?? 'unknown',
                chunk: (results.metadatas?.[0]?.[i] as any)?.chunk ?? 0,
                totalChunks: (results.metadatas?.[0]?.[i] as any)?.totalChunks ?? 1,
                startChar: (results.metadatas?.[0]?.[i] as any)?.startChar ?? 0,
                endChar: (results.metadatas?.[0]?.[i] as any)?.endChar ?? 0,
                ...(results.metadatas?.[0]?.[i] ?? {}),
              },
            },
            score,
            distance,
          });
        }
      }

      this.logger.log(
        `Found ${searchResults.length} similar documents in collection '${collectionName}'`,
      );
      return searchResults;
    } catch {
      this.logger.error(`Error searching collection ${collectionName}:`);
      throw new Error(`Failed to search collection: ${collectionName}`);
    }
  }

  /**
   * 混合搜索（结合关键词和向量搜索）
   */
  async hybridSearch(
    collectionName: string,
    queryEmbedding: number[],
    keywords: string[],
    options: {
      topK?: number;
      vectorWeight?: number;
      keywordWeight?: number;
      minScore?: number;
    } = {},
  ): Promise<SearchResult[]> {
    try {
      const { topK = 5, vectorWeight = 0.7, keywordWeight = 0.3, minScore = 0 } = options;

      // 向量搜索
      const vectorResults = await this.similaritySearch(collectionName, queryEmbedding, {
        topK: topK * 2, // 获取更多结果用于重排序
        minScore: 0,
      });

      // 关键词匹配评分
      const hybridResults = vectorResults.map((result) => {
        let keywordScore = 0;
        const content = result.chunk.content.toLowerCase();

        for (const keyword of keywords) {
          const keywordLower = keyword.toLowerCase();
          const regex = new RegExp(keywordLower, 'g');
          let occurrences = 0;
          while (regex.exec(content) !== null) {
            occurrences++;
          }
          keywordScore += occurrences * (1 / keywords.length);
        }

        // 标准化关键词分数
        keywordScore = Math.min(keywordScore, 1);

        // 计算混合分数
        const hybridScore = result.score * vectorWeight + keywordScore * keywordWeight;

        return {
          ...result,
          score: hybridScore,
        };
      });

      // 按混合分数排序并过滤
      const filteredResults = hybridResults
        .filter((result) => result.score >= minScore)
        .sort((a, b) => b.score - a.score)
        .slice(0, topK);

      this.logger.log(
        `Hybrid search found ${filteredResults.length} results in collection '${collectionName}'`,
      );
      return filteredResults;
    } catch {
      this.logger.error(`Error in hybrid search for collection ${collectionName}:`);
      throw new Error(`Failed to perform hybrid search in collection: ${collectionName}`);
    }
  }

  /**
   * 获取集合统计信息
   */
  async getCollectionStats(collectionName: string): Promise<{
    name: string;
    count: number;
    metadata: Record<string, any>;
  }> {
    try {
      const collection = await this.getOrCreateCollection(collectionName);
      const count = await collection.count();

      return {
        name: collectionName,
        count,
        metadata: {}, // ChromaDB 可能不直接提供集合元数据
      };
    } catch {
      this.logger.error(`Error getting stats for collection ${collectionName}:`);
      throw new Error(`Failed to get collection stats: ${collectionName}`);
    }
  }

  /**
   * 列出所有集合
   */
  async listCollections(): Promise<string[]> {
    try {
      const collections = await this.chromaClient.listCollections();
      return collections.map((collection) => collection.name);
    } catch {
      this.logger.error('Error listing collections:');
      throw new Error('Failed to list collections');
    }
  }

  /**
   * 删除集合
   */
  async deleteCollection(collectionName: string): Promise<void> {
    try {
      await this.chromaClient.deleteCollection({ name: collectionName });
      this.collections.delete(collectionName);
      this.logger.log(`Collection '${collectionName}' deleted`);
    } catch {
      this.logger.error(`Error deleting collection ${collectionName}:`);
      throw new Error(`Failed to delete collection: ${collectionName}`);
    }
  }

  /**
   * 检查向量存储服务是否健康
   */
  async isHealthy(): Promise<boolean> {
    try {
      await this.chromaClient.heartbeat();
      return true;
    } catch {
      this.logger.error('Vector store health check failed:');
      return false;
    }
  }

  /**
   * 清理缓存的集合引用
   */
  clearCollectionCache(): void {
    this.collections.clear();
    this.logger.log('Collection cache cleared');
  }
}
