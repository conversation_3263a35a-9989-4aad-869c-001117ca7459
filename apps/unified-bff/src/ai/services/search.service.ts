import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AIService } from './ai.service';
import { DocumentEmbeddingService } from './document-embedding.service';
import { SearchResult, VectorStoreService } from './vector-store.service';

export interface SmartSearchOptions {
  collectionName: string;
  topK?: number;
  minScore?: number;
  enableSemanticSearch?: boolean;
  enableKeywordSearch?: boolean;
  semanticWeight?: number;
  keywordWeight?: number;
  filters?: Record<string, any>;
  expandQuery?: boolean;
  includeSnippets?: boolean;
  snippetLength?: number;
}

export interface SmartSearchResult {
  results: Array<{
    id: string;
    title?: string;
    content: string;
    snippet?: string;
    metadata: Record<string, any>;
    score: number;
    relevanceType: 'semantic' | 'keyword' | 'hybrid';
    highlights?: string[];
  }>;
  totalResults: number;
  searchTime: number;
  searchQuery: {
    original: string;
    expanded?: string;
    keywords: string[];
  };
  suggestions?: string[];
}

export interface SearchAnalytics {
  query: string;
  resultCount: number;
  clickedResults: string[];
  searchTime: number;
  timestamp: Date;
  userId?: string;
}

@Injectable()
export class SearchService {
  private readonly logger = new Logger(SearchService.name);
  private searchAnalytics: Map<string, SearchAnalytics[]> = new Map();

  constructor(
    private readonly aiService: AIService,
    private readonly embeddingService: DocumentEmbeddingService,
    private readonly vectorStoreService: VectorStoreService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 智能搜索
   */
  async smartSearch(query: string, options: SmartSearchOptions): Promise<SmartSearchResult> {
    const startTime = Date.now();

    try {
      this.logger.log(`Smart search query: "${query}"`);

      // 1. 查询预处理和扩展
      const processedQuery = await this.preprocessQuery(query, options.expandQuery);
      const keywords = this.extractKeywords(processedQuery.expanded || processedQuery.original);

      // 2. 根据配置执行不同类型的搜索
      let results: SearchResult[] = [];

      if (options.enableSemanticSearch && options.enableKeywordSearch) {
        // 混合搜索
        results = await this.performHybridSearch(
          processedQuery.expanded || processedQuery.original,
          keywords,
          options,
        );
      } else if (options.enableSemanticSearch) {
        // 语义搜索
        results = await this.performSemanticSearch(
          processedQuery.expanded || processedQuery.original,
          options,
        );
      } else {
        // 关键词搜索
        results = await this.performKeywordSearch(keywords, options);
      }

      // 3. 后处理结果
      const processedResults = await this.postProcessResults(results, query, options);

      // 4. 生成搜索建议
      const suggestions = await this.generateSearchSuggestions(query, results);

      const searchTime = Date.now() - startTime;

      // 5. 记录搜索分析
      this.recordSearchAnalytics({
        query,
        resultCount: processedResults.length,
        clickedResults: [],
        searchTime,
        timestamp: new Date(),
      });

      const searchResult: SmartSearchResult = {
        results: processedResults,
        totalResults: processedResults.length,
        searchTime,
        searchQuery: {
          original: query,
          expanded: processedQuery.expanded,
          keywords,
        },
        suggestions,
      };

      this.logger.log(
        `Smart search completed in ${searchTime}ms with ${processedResults.length} results`,
      );
      return searchResult;
    } catch {
      this.logger.error('Error in smart search:');
      throw new Error('Failed to perform smart search');
    }
  }

  /**
   * 搜索建议
   */
  async getSearchSuggestions(
    partialQuery: string,
    collectionName: string,
    limit: number = 5,
  ): Promise<string[]> {
    try {
      // 基于历史搜索记录生成建议
      const historicalSuggestions = this.getHistoricalSuggestions(partialQuery, limit);

      // 使用AI生成智能建议
      const aiSuggestions = await this.generateAISuggestions(partialQuery, limit);

      // 合并和去重
      const allSuggestions = [...historicalSuggestions, ...aiSuggestions];
      const uniqueSuggestions = Array.from(new Set(allSuggestions));

      return uniqueSuggestions.slice(0, limit);
    } catch {
      this.logger.error('Error generating search suggestions:');
      return [];
    }
  }

  /**
   * 相似文档推荐
   */
  async findSimilarDocuments(
    documentId: string,
    collectionName: string,
    limit: number = 5,
  ): Promise<SearchResult[]> {
    try {
      // 获取目标文档的嵌入向量
      const collection = await this.vectorStoreService.getOrCreateCollection(collectionName);
      const documentData = await collection.get({ ids: [documentId] });

      if (!documentData.embeddings || documentData.embeddings.length === 0) {
        return [];
      }

      const embedding = documentData.embeddings[0];

      // 执行相似性搜索
      const results = await this.vectorStoreService.similaritySearch(collectionName, embedding, {
        topK: limit + 1, // +1 因为会包含原文档
        minScore: 0.3,
      });

      // 过滤掉原文档
      return results.filter((result) => result.chunk.id !== documentId);
    } catch {
      this.logger.error('Error finding similar documents:');
      throw new Error('Failed to find similar documents');
    }
  }

  /**
   * 高级过滤搜索
   */
  async advancedSearch(
    filters: {
      query?: string;
      dateRange?: { from: Date; to: Date };
      contentType?: string[];
      tags?: string[];
      author?: string;
      minScore?: number;
    },
    collectionName: string,
    options: Omit<SmartSearchOptions, 'collectionName'> = {},
  ): Promise<SmartSearchResult> {
    try {
      // 构建查询条件
      const searchOptions: SmartSearchOptions = {
        ...options,
        collectionName,
        filters: this.buildFilterConditions(filters),
      };

      // 如果有查询文本，执行智能搜索
      if (filters.query) {
        return await this.smartSearch(filters.query, searchOptions);
      }

      // 否则执行基于过滤器的搜索
      return await this.filterOnlySearch(searchOptions);
    } catch {
      this.logger.error('Error in advanced search:');
      throw new Error('Failed to perform advanced search');
    }
  }

  /**
   * 搜索分析和统计
   */
  getSearchAnalytics(timeRange?: { from: Date; to: Date }): {
    totalSearches: number;
    averageResultCount: number;
    averageSearchTime: number;
    topQueries: Array<{ query: string; count: number }>;
    lowResultQueries: Array<{ query: string; resultCount: number }>;
  } {
    const allAnalytics = Array.from(this.searchAnalytics.values()).flat();

    let filteredAnalytics = allAnalytics;
    if (timeRange) {
      filteredAnalytics = allAnalytics.filter(
        (analytics) => analytics.timestamp >= timeRange.from && analytics.timestamp <= timeRange.to,
      );
    }

    // 统计计算
    const totalSearches = filteredAnalytics.length;
    const averageResultCount =
      filteredAnalytics.reduce((sum, a) => sum + a.resultCount, 0) / totalSearches || 0;
    const averageSearchTime =
      filteredAnalytics.reduce((sum, a) => sum + a.searchTime, 0) / totalSearches || 0;

    // 热门查询
    const queryCount = new Map<string, number>();
    filteredAnalytics.forEach((analytics) => {
      queryCount.set(analytics.query, (queryCount.get(analytics.query) || 0) + 1);
    });

    const topQueries = Array.from(queryCount.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([query, count]) => ({ query, count }));

    // 低结果查询
    const lowResultQueries = filteredAnalytics
      .filter((analytics) => analytics.resultCount < 3)
      .sort((a, b) => a.resultCount - b.resultCount)
      .slice(0, 10)
      .map((analytics) => ({ query: analytics.query, resultCount: analytics.resultCount }));

    return {
      totalSearches,
      averageResultCount,
      averageSearchTime,
      topQueries,
      lowResultQueries,
    };
  }

  /**
   * 查询预处理
   */
  private async preprocessQuery(
    query: string,
    expandQuery?: boolean,
  ): Promise<{
    original: string;
    expanded?: string;
  }> {
    const processed: { original: string; expanded?: string } = { original: query };

    if (expandQuery) {
      try {
        const template = `请扩展以下搜索查询，添加相关的同义词和相关概念，以提高搜索效果：

原始查询：{query}

扩展后的查询：`;

        const expanded = await this.aiService.generateFromTemplate(
          template,
          { query },
          { temperature: 0.3, maxTokens: 100 },
        );

        processed.expanded = expanded.trim();
      } catch (error) {
        this.logger.warn('Failed to expand query:', error);
      }
    }

    return processed;
  }

  /**
   * 提取关键词
   */
  private extractKeywords(query: string): string[] {
    // 简单的关键词提取，可以集成更专业的NLP库
    return query
      .toLowerCase()
      .split(/\s+/)
      .filter((word) => word.length > 2)
      .filter(
        (word) =>
          ![
            '的',
            '是',
            '在',
            '有',
            '和',
            '与',
            '或',
            '但',
            '如果',
            'the',
            'is',
            'and',
            'or',
            'but',
            'if',
          ].includes(word),
      );
  }

  /**
   * 执行语义搜索
   */
  private async performSemanticSearch(
    query: string,
    options: SmartSearchOptions,
  ): Promise<SearchResult[]> {
    const queryEmbedding = await this.embeddingService.generateQueryEmbedding(query);

    return await this.vectorStoreService.similaritySearch(options.collectionName, queryEmbedding, {
      topK: options.topK || 10,
      filter: options.filters,
      minScore: options.minScore || 0.3,
    });
  }

  /**
   * 执行关键词搜索
   */
  private async performKeywordSearch(
    keywords: string[],
    options: SmartSearchOptions,
  ): Promise<SearchResult[]> {
    // 这里可以实现基于关键词的搜索逻辑
    // 简化版本：使用向量搜索但给关键词匹配更高权重
    const keywordQuery = keywords.join(' ');
    const queryEmbedding = await this.embeddingService.generateQueryEmbedding(keywordQuery);

    return await this.vectorStoreService.similaritySearch(options.collectionName, queryEmbedding, {
      topK: options.topK || 10,
      filter: options.filters,
      minScore: options.minScore || 0.3,
    });
  }

  /**
   * 执行混合搜索
   */
  private async performHybridSearch(
    query: string,
    keywords: string[],
    options: SmartSearchOptions,
  ): Promise<SearchResult[]> {
    const queryEmbedding = await this.embeddingService.generateQueryEmbedding(query);

    return await this.vectorStoreService.hybridSearch(
      options.collectionName,
      queryEmbedding,
      keywords,
      {
        topK: options.topK || 10,
        vectorWeight: options.semanticWeight || 0.7,
        keywordWeight: options.keywordWeight || 0.3,
        minScore: options.minScore || 0.3,
      },
    );
  }

  /**
   * 后处理搜索结果
   */
  private async postProcessResults(
    results: SearchResult[],
    originalQuery: string,
    options: SmartSearchOptions,
  ): Promise<SmartSearchResult['results']> {
    return await Promise.all(
      results.map(async (result) => {
        let title = 'Untitled';
        if (typeof result.chunk.metadata['title'] === 'string') {
          title = result.chunk.metadata['title'];
        } else if (typeof result.chunk.metadata['source'] === 'string') {
          title = result.chunk.metadata['source'];
        }

        const processedResult: SmartSearchResult['results'][0] = {
          id: result.chunk.id,
          title,
          content: result.chunk.content,
          metadata: result.chunk.metadata,
          score: result.score,
          relevanceType: 'hybrid' as const,
          highlights: [] as string[],
        };

        // 生成片段
        if (options.includeSnippets) {
          processedResult.snippet = this.generateSnippet(
            result.chunk.content,
            originalQuery,
            options.snippetLength || 150,
          );
        }

        // 生成高亮
        processedResult.highlights = this.generateHighlights(result.chunk.content, originalQuery);

        return processedResult;
      }),
    );
  }

  /**
   * 生成搜索建议
   */
  private async generateSearchSuggestions(
    query: string,
    results: SearchResult[],
  ): Promise<string[]> {
    if (results.length === 0) {
      return await this.generateAISuggestions(query, 3);
    }

    // 基于搜索结果生成相关建议
    const contentSample = results
      .slice(0, 3)
      .map((r) => r.chunk.content.substring(0, 200))
      .join(' ');

    const template = `基于以下搜索结果内容，为用户的查询"${query}"生成3个相关的搜索建议：

内容样本：
{content}

请以JSON数组格式返回建议，例如：["建议1", "建议2", "建议3"]

搜索建议：`;

    try {
      const response = await this.aiService.generateFromTemplate(
        template,
        { content: contentSample },
        { temperature: 0.5, maxTokens: 150 },
      );

      const suggestions = JSON.parse(response);
      return Array.isArray(suggestions) ? suggestions : [];
    } catch {
      return [];
    }
  }

  /**
   * 生成片段
   */
  private generateSnippet(content: string, query: string, maxLength: number): string {
    const words = query.toLowerCase().split(' ');
    const contentLower = content.toLowerCase();

    // 找到第一个匹配的关键词位置
    let bestPosition = 0;
    for (const word of words) {
      const position = contentLower.indexOf(word);
      if (position !== -1) {
        bestPosition = Math.max(0, position - 50);
        break;
      }
    }

    let snippet = content.substring(bestPosition, bestPosition + maxLength);

    // 确保片段不会在单词中间截断
    const lastSpaceIndex = snippet.lastIndexOf(' ');
    if (lastSpaceIndex > maxLength * 0.8) {
      snippet = snippet.substring(0, lastSpaceIndex);
    }

    return bestPosition > 0 ? `...${snippet}...` : `${snippet}...`;
  }

  /**
   * 生成高亮
   */
  private generateHighlights(content: string, query: string): string[] {
    const words = query
      .toLowerCase()
      .split(' ')
      .filter((word) => word.length > 2);
    const highlights: string[] = [];
    const _contentLower = content.toLowerCase();

    for (const word of words) {
      const regex = new RegExp(`\\b${word}\\b`, 'gi');
      let match;
      while ((match = regex.exec(content)) !== null) {
        highlights.push(match[0]);
      }
    }

    return Array.from(new Set(highlights));
  }

  /**
   * 构建过滤条件
   */
  private buildFilterConditions(filters: Record<string, unknown>): Record<string, unknown> {
    const conditions: Record<string, unknown> = {};

    if (filters.dateRange) {
      conditions.created_at = {
        $gte: filters.dateRange.from,
        $lte: filters.dateRange.to,
      };
    }

    if (filters.contentType) {
      conditions.type = { $in: filters.contentType };
    }

    if (filters.tags) {
      conditions.tags = { $in: filters.tags };
    }

    if (filters.author) {
      conditions.author = filters.author;
    }

    return conditions;
  }

  /**
   * 纯过滤器搜索
   */
  private async filterOnlySearch(_options: SmartSearchOptions): Promise<SmartSearchResult> {
    // 实现基于过滤器的搜索逻辑
    // 这里需要根据具体的向量数据库实现
    return {
      results: [],
      totalResults: 0,
      searchTime: 0,
      searchQuery: {
        original: '',
        keywords: [],
      },
    };
  }

  /**
   * 获取历史搜索建议
   */
  private getHistoricalSuggestions(partialQuery: string, limit: number): string[] {
    const allQueries = Array.from(this.searchAnalytics.values())
      .flat()
      .map((analytics) => analytics.query)
      .filter((query) => query.toLowerCase().includes(partialQuery.toLowerCase()));

    const queryCount = new Map<string, number>();
    allQueries.forEach((query) => {
      queryCount.set(query, (queryCount.get(query) || 0) + 1);
    });

    return Array.from(queryCount.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, limit)
      .map(([query]) => query);
  }

  /**
   * 生成AI建议
   */
  private async generateAISuggestions(partialQuery: string, limit: number): Promise<string[]> {
    const template = `基于部分查询"${partialQuery}"，生成${limit}个相关的搜索建议：

请以JSON数组格式返回建议，例如：["建议1", "建议2", "建议3"]

搜索建议：`;

    try {
      const response = await this.aiService.generateCompletion(template, {
        temperature: 0.7,
        maxTokens: 100,
      });

      const suggestions = JSON.parse(response);
      return Array.isArray(suggestions) ? suggestions : [];
    } catch {
      return [];
    }
  }

  /**
   * 记录搜索分析
   */
  private recordSearchAnalytics(analytics: SearchAnalytics): void {
    const date = analytics.timestamp.toISOString().split('T')[0];

    if (!this.searchAnalytics.has(date)) {
      this.searchAnalytics.set(date, []);
    }

    const dateAnalytics = this.searchAnalytics.get(date);
    if (dateAnalytics) {
      dateAnalytics.push(analytics);
    }

    // 清理旧数据（保留30天）
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    for (const [date, _analyticsArray] of this.searchAnalytics.entries()) {
      if (new Date(date) < thirtyDaysAgo) {
        this.searchAnalytics.delete(date);
      }
    }
  }
}
