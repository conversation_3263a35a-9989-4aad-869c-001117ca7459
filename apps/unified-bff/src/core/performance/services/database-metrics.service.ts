import { Injectable, Logger } from '@nestjs/common';
import { InjectConnection } from '@nestjs/typeorm';
import { Connection } from 'typeorm';
import { CacheService } from '@/core/cache/cache.service';

export interface DatabaseMetrics {
  connectionInfo: {
    activeConnections: number;
    totalConnections: number;
    maxConnections: number;
    idleConnections: number;
  };
  queryPerformance: {
    avgQueryTime: number;
    slowQueryCount: number;
    totalQueries: number;
    queriesPerSecond: number;
  };
  tableStats: {
    tableName: string;
    rowCount: number;
    tableSize: string;
    indexSize: string;
    totalSize: string;
  }[];
  indexUsage: {
    indexName: string;
    tableName: string;
    usageCount: number;
    effectiveRatio: number;
  }[];
  lockInfo: {
    blockedQueries: number;
    lockWaitTime: number;
  };
}

export interface SlowQueryInfo {
  query: string;
  executionTime: number;
  callCount: number;
  avgTime: number;
  timestamp: Date;
}

@Injectable()
export class DatabaseMetricsService {
  private readonly logger = new Logger(DatabaseMetricsService.name);
  private slowQueries: Map<string, SlowQueryInfo> = new Map();
  private queryStats = {
    totalQueries: 0,
    totalExecutionTime: 0,
    slowQueryThreshold: 1000, // 1秒
  };

  constructor(
    @InjectConnection()
    private connection: Connection,
    private cacheService: CacheService,
  ) {}

  /**
   * 获取数据库性能指标
   */
  async getDatabaseMetrics(): Promise<DatabaseMetrics> {
    const cacheKey = 'database:metrics';
    let metrics = await this.cacheService.get<DatabaseMetrics>(cacheKey);

    if (!metrics) {
      const [connectionInfo, queryPerformance, tableStats, indexUsage, lockInfo] =
        await Promise.all([
          this.getConnectionInfo(),
          this.getQueryPerformance(),
          this.getTableStats(),
          this.getIndexUsage(),
          this.getLockInfo(),
        ]);

      metrics = {
        connectionInfo,
        queryPerformance,
        tableStats,
        indexUsage,
        lockInfo,
      };

      // 缓存5分钟
      await this.cacheService.set(cacheKey, metrics, 5 * 60 * 1000);
    }

    return metrics;
  }

  /**
   * 记录查询执行时间
   */
  recordQueryExecution(sql: string, executionTime: number): void {
    this.queryStats.totalQueries++;
    this.queryStats.totalExecutionTime += executionTime;

    // 记录慢查询
    if (executionTime >= this.queryStats.slowQueryThreshold) {
      const normalizedQuery = this.normalizeQuery(sql);
      const existing = this.slowQueries.get(normalizedQuery);

      if (existing) {
        existing.callCount++;
        existing.avgTime =
          (existing.avgTime * (existing.callCount - 1) + executionTime) / existing.callCount;
        existing.timestamp = new Date();
      } else {
        this.slowQueries.set(normalizedQuery, {
          query: normalizedQuery,
          executionTime,
          callCount: 1,
          avgTime: executionTime,
          timestamp: new Date(),
        });
      }

      // 保持慢查询记录在合理数量
      if (this.slowQueries.size > 100) {
        const oldestKey = Array.from(this.slowQueries.entries()).sort(
          ([, a], [, b]) => a.timestamp.getTime() - b.timestamp.getTime(),
        )[0][0];
        this.slowQueries.delete(oldestKey);
      }
    }
  }

  /**
   * 获取慢查询列表
   */
  getSlowQueries(): SlowQueryInfo[] {
    return Array.from(this.slowQueries.values())
      .sort((a, b) => b.avgTime - a.avgTime)
      .slice(0, 20); // 返回最慢的20个查询
  }

  /**
   * 清除慢查询记录
   */
  clearSlowQueries(): void {
    this.slowQueries.clear();
    this.queryStats = {
      totalQueries: 0,
      totalExecutionTime: 0,
      slowQueryThreshold: 1000,
    };
  }

  /**
   * 获取查询性能建议
   */
  async getPerformanceRecommendations(): Promise<string[]> {
    const recommendations: string[] = [];
    const metrics = await this.getDatabaseMetrics();

    // 连接池建议
    if (metrics.connectionInfo.activeConnections / metrics.connectionInfo.maxConnections > 0.8) {
      recommendations.push('考虑增加数据库连接池大小');
    }

    // 慢查询建议
    if (metrics.queryPerformance.slowQueryCount > 10) {
      recommendations.push('存在较多慢查询，建议优化查询语句或添加索引');
    }

    // 索引使用建议
    const lowUsageIndexes = metrics.indexUsage.filter((idx) => idx.effectiveRatio < 0.1);
    if (lowUsageIndexes.length > 0) {
      recommendations.push(`发现${lowUsageIndexes.length}个低使用率索引，考虑删除以节省空间`);
    }

    // 表大小建议
    const largeTables = metrics.tableStats.filter((table) => {
      const sizeNum = parseFloat(table.totalSize.replace(/[^\d.]/g, ''));
      return sizeNum > 1000; // 大于1GB
    });
    if (largeTables.length > 0) {
      recommendations.push('存在大表，考虑分区或归档历史数据');
    }

    return recommendations;
  }

  /**
   * 生成性能报告
   */
  async generatePerformanceReport(): Promise<{
    summary: any;
    details: DatabaseMetrics;
    slowQueries: SlowQueryInfo[];
    recommendations: string[];
  }> {
    const [details, slowQueries, recommendations] = await Promise.all([
      this.getDatabaseMetrics(),
      Promise.resolve(this.getSlowQueries()),
      this.getPerformanceRecommendations(),
    ]);

    const summary = {
      avgQueryTime: details.queryPerformance.avgQueryTime,
      slowQueryCount: details.queryPerformance.slowQueryCount,
      connectionUsage: `${details.connectionInfo.activeConnections}/${details.connectionInfo.maxConnections}`,
      totalTableSize: details.tableStats.reduce((sum, table) => {
        const sizeNum = parseFloat(table.totalSize.replace(/[^\d.]/g, ''));
        return sum + sizeNum;
      }, 0),
      recommendationCount: recommendations.length,
    };

    return {
      summary,
      details,
      slowQueries,
      recommendations,
    };
  }

  /**
   * 私有方法：获取连接信息
   */
  private async getConnectionInfo(): Promise<DatabaseMetrics['connectionInfo']> {
    try {
      const result = await this.connection.query(`
        SELECT 
          count(*) as total_connections,
          count(*) FILTER (WHERE state = 'active') as active_connections,
          count(*) FILTER (WHERE state = 'idle') as idle_connections
        FROM pg_stat_activity 
        WHERE datname = current_database()
      `);

      const maxConnResult = await this.connection.query('SHOW max_connections');
      const maxConnections = parseInt(maxConnResult[0].max_connections);

      return {
        totalConnections: parseInt(result[0].total_connections),
        activeConnections: parseInt(result[0].active_connections),
        idleConnections: parseInt(result[0].idle_connections),
        maxConnections,
      };
    } catch (error) {
      this.logger.error('Error occurred', error);
      return {
        totalConnections: 0,
        activeConnections: 0,
        idleConnections: 0,
        maxConnections: 100,
      };
    }
  }

  /**
   * 私有方法：获取查询性能
   */
  private async getQueryPerformance(): Promise<DatabaseMetrics['queryPerformance']> {
    const avgQueryTime =
      this.queryStats.totalQueries > 0
        ? this.queryStats.totalExecutionTime / this.queryStats.totalQueries
        : 0;

    return {
      avgQueryTime: Math.round(avgQueryTime * 100) / 100,
      slowQueryCount: this.slowQueries.size,
      totalQueries: this.queryStats.totalQueries,
      queriesPerSecond:
        (this.queryStats.totalQueries / (Date.now() / 1000 - process.uptime() * 1000)) * 1000,
    };
  }

  /**
   * 私有方法：获取表统计信息
   */
  private async getTableStats(): Promise<DatabaseMetrics['tableStats']> {
    try {
      const result = await this.connection.query(`
        SELECT 
          schemaname,
          tablename as table_name,
          n_tup_ins + n_tup_upd + n_tup_del as row_count,
          pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size,
          pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
          pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) as index_size
        FROM pg_stat_user_tables 
        ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
        LIMIT 20
      `);

      return result.map((row: unknown) => ({
        tableName: row.table_name,
        rowCount: parseInt(row.row_count) || 0,
        tableSize: row.table_size,
        indexSize: row.index_size,
        totalSize: row.total_size,
      }));
    } catch (error) {
      this.logger.error('Error occurred', error);
      return [];
    }
  }

  /**
   * 私有方法：获取索引使用情况
   */
  private async getIndexUsage(): Promise<DatabaseMetrics['indexUsage']> {
    try {
      const result = await this.connection.query(`
        SELECT 
          schemaname,
          tablename as table_name,
          indexname as index_name,
          idx_tup_read as usage_count,
          CASE 
            WHEN idx_tup_read + idx_tup_fetch > 0 
            THEN round(idx_tup_read::numeric / (idx_tup_read + idx_tup_fetch), 4)
            ELSE 0 
          END as effective_ratio
        FROM pg_stat_user_indexes 
        ORDER BY idx_tup_read DESC
        LIMIT 50
      `);

      return result.map((row: unknown) => ({
        indexName: row.index_name,
        tableName: row.table_name,
        usageCount: parseInt(row.usage_count) || 0,
        effectiveRatio: parseFloat(row.effective_ratio) || 0,
      }));
    } catch (error) {
      this.logger.error('Error occurred', error);
      return [];
    }
  }

  /**
   * 私有方法：获取锁信息
   */
  private async getLockInfo(): Promise<DatabaseMetrics['lockInfo']> {
    try {
      const result = await this.connection.query(`
        SELECT 
          count(*) FILTER (WHERE NOT granted) as blocked_queries,
          avg(EXTRACT(EPOCH FROM (now() - query_start)) * 1000) FILTER (WHERE NOT granted) as avg_lock_wait_time
        FROM pg_locks l
        LEFT JOIN pg_stat_activity a ON l.pid = a.pid
        WHERE l.database = (SELECT oid FROM pg_database WHERE datname = current_database())
      `);

      return {
        blockedQueries: parseInt(result[0].blocked_queries) || 0,
        lockWaitTime: Math.round(parseFloat(result[0].avg_lock_wait_time)) || 0,
      };
    } catch (error) {
      this.logger.error('Error occurred', error);
      return {
        blockedQueries: 0,
        lockWaitTime: 0,
      };
    }
  }

  /**
   * 私有方法：标准化查询语句
   */
  private normalizeQuery(sql: string): string {
    return sql
      .replace(/\$\d+/g, '?') // 替换参数占位符
      .replace(/\s+/g, ' ') // 压缩空白字符
      .trim()
      .substring(0, 200); // 截取前200字符
  }
}
