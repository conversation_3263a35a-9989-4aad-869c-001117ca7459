import { AnalyticsDailyStats, AnalyticsEvent } from '@/shared/entities/analytics.entity';
import { Document } from '@/shared/entities/document.entity';
import { User } from '@/shared/entities/user.entity';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ObjectLiteral, Repository, SelectQueryBuilder } from 'typeorm';

export interface QueryOptimizationConfig {
  enableQueryCache?: boolean;
  cacheTimeout?: number;
  enableEagerLoading?: boolean;
  maxJoinDepth?: number;
  enableBatchLoading?: boolean;
  batchSize?: number;
}

@Injectable()
export class QueryOptimizationService {
  private readonly logger = new Logger(QueryOptimizationService.name);
  private readonly defaultConfig: QueryOptimizationConfig = {
    enableQueryCache: true,
    cacheTimeout: 300000, // 5分钟
    enableEagerLoading: false,
    maxJoinDepth: 3,
    enableBatchLoading: true,
    batchSize: 100,
  };

  constructor(
    @InjectRepository(User)
    private readonly usersRepository: Repository<User>,
    @InjectRepository(Document)
    private readonly documentsRepository: Repository<Document>,
    @InjectRepository(AnalyticsEvent)
    private readonly analyticsEventRepository: Repository<AnalyticsEvent>,
    @InjectRepository(AnalyticsDailyStats)
    private readonly dailyStatsRepository: Repository<AnalyticsDailyStats>,
  ) {}

  /**
   * 优化查询构建器
   */
  optimizeQueryBuilder<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    config: Partial<QueryOptimizationConfig> = {},
  ): SelectQueryBuilder<T> {
    const opts = { ...this.defaultConfig, ...config };

    // 启用查询缓存
    if (opts.enableQueryCache) {
      queryBuilder.cache(opts.cacheTimeout);
    }

    // 添加查询提示
    this.addQueryHints(queryBuilder);

    return queryBuilder;
  }

  /**
   * 优化分页查询
   */
  async optimizedPagination<T>(
    repository: Repository<T>,
    page: number,
    pageSize: number,
    queryBuilder?: SelectQueryBuilder<T>,
    config: Partial<QueryOptimizationConfig> = {},
  ): Promise<{ data: T[]; total: number; hasMore: boolean }> {
    const opts = { ...this.defaultConfig, ...config };
    const skip = (page - 1) * pageSize;
    const take = Math.min(pageSize, opts.batchSize || 100);

    let qb = queryBuilder || repository.createQueryBuilder();
    qb = this.optimizeQueryBuilder(qb, config);

    // 使用子查询优化大表分页
    if (skip > 1000) {
      qb = this.optimizeLargePagination(qb, skip, take);
    } else {
      qb.skip(skip).take(take + 1); // +1 用于检查是否有更多数据
    }

    const results = await qb.getMany();
    const hasMore = results.length > take;

    if (hasMore) {
      results.pop(); // 移除多获取的一条记录
    }

    // 对于小偏移量或需要准确总数的情况，获取总数
    let total = 0;
    if (skip < 1000 || page === 1) {
      const countQb = repository.createQueryBuilder();
      // 复制查询条件但不包括排序、分页
      this.copyWhereConditions(queryBuilder, countQb);
      total = await countQb.getCount();
    } else {
      // 对于大偏移量，估算总数以提高性能
      total = skip + results.length + (hasMore ? pageSize : 0);
    }

    return {
      data: results,
      total,
      hasMore,
    };
  }

  /**
   * 批量获取实体
   */
  async batchFind<T>(
    repository: Repository<T>,
    ids: string[],
    config: Partial<QueryOptimizationConfig> = {},
  ): Promise<T[]> {
    const opts = { ...this.defaultConfig, ...config };

    if (!opts.enableBatchLoading || ids.length <= (opts.batchSize || 100)) {
      return repository.findByIds(ids);
    }

    // 分批处理大量ID
    const batches: string[][] = [];
    const batchSize = opts.batchSize || 100;
    for (let i = 0; i < ids.length; i += batchSize) {
      batches.push(ids.slice(i, i + batchSize));
    }

    const results = await Promise.all(batches.map((batch) => repository.findByIds(batch)));

    return results.flat();
  }

  /**
   * 优化关联查询
   */
  optimizeRelationQuery<T>(
    queryBuilder: SelectQueryBuilder<T>,
    relations: string[],
    config: Partial<QueryOptimizationConfig> = {},
  ): SelectQueryBuilder<T> {
    const opts = { ...this.defaultConfig, ...config };

    // 限制JOIN深度
    const filteredRelations = relations.filter((relation) => {
      const depth = relation.split('.').length;
      return depth <= (opts.maxJoinDepth || 3);
    });

    // 使用LEFT JOIN而不是多次查询
    filteredRelations.forEach((relation) => {
      const parts = relation.split('.');
      let currentAlias = queryBuilder.alias;
      let joinPath = '';

      parts.forEach((part, index) => {
        joinPath = joinPath ? `${joinPath}.${part}` : `${currentAlias}.${part}`;
        const joinAlias = parts.slice(0, index + 1).join('_');

        queryBuilder.leftJoinAndSelect(joinPath, joinAlias);
        currentAlias = joinAlias;
      });
    });

    return queryBuilder;
  }

  /**
   * 分析慢查询
   */
  async analyzeSlowQuery(sql: string, parameters: unknown[], executionTime: number): Promise<void> {
    if (executionTime > 1000) {
      // 超过1秒的查询
      this.logger.warn(`Slow Query Detected (${executionTime}ms)`, {
        sql: sql.substring(0, 500), // 截取前500字符
        parameters,
        executionTime,
        timestamp: new Date().toISOString(),
      });

      // 可以在这里添加更多分析逻辑，如：
      // - 发送到监控系统
      // - 记录到专门的慢查询日志
      // - 触发自动优化建议
    }
  }

  /**
   * 获取查询性能建议
   */
  async getQueryPerformanceSuggestions(entity: string): Promise<string[]> {
    const suggestions: string[] = [];

    switch (entity.toLowerCase()) {
      case 'user':
        suggestions.push(
          '考虑在 lastLoginAt 字段上添加索引以优化活跃用户查询',
          '对于用户搜索，建议使用全文搜索索引',
          '考虑按创建时间分区用户表',
        );
        break;

      case 'document':
        suggestions.push(
          '建议在 (createdBy, status) 上创建复合索引',
          '对于文档搜索，考虑使用 PostgreSQL 的全文搜索功能',
          '在 tags 字段上使用 GIN 索引以优化标签查询',
          '考虑将大文档内容存储在单独的表中',
        );
        break;

      case 'analytics':
        suggestions.push(
          '建议在 (eventType, createdAt) 上创建复合索引',
          '考虑按时间分区分析事件表',
          '使用时间窗口聚合来预计算统计数据',
          '对于实时分析，考虑使用物化视图',
        );
        break;
    }

    return suggestions;
  }

  /**
   * 优化统计查询
   */
  async optimizeStatsQuery<T>(
    repository: Repository<T>,
    statsQuery: () => Promise<any>,
  ): Promise<any> {
    const startTime = Date.now();

    try {
      const result = await statsQuery();
      const executionTime = Date.now() - startTime;

      if (executionTime > 500) {
        this.logger.warn(`Stats query took ${executionTime}ms - consider optimization`);
      }

      return result;
    } catch (error) {
      this.logger.error('Error occurred', error);
      throw error;
    }
  }

  /**
   * 私有方法：添加查询提示
   */
  private addQueryHints<T>(_queryBuilder: SelectQueryBuilder<T>): void {
    // PostgreSQL特定的查询提示
    // 这些提示可以根据具体的查询模式进行调整
    // 对于大表查询，使用索引扫描
    // queryBuilder.addSelect('/* USE INDEX */');
  }

  /**
   * 私有方法：优化大表分页
   */
  private optimizeLargePagination<T>(
    queryBuilder: SelectQueryBuilder<T>,
    skip: number,
    take: number,
  ): SelectQueryBuilder<T> {
    // 使用子查询方式优化大偏移量分页
    // 这种方式对于PostgreSQL特别有效

    const alias = queryBuilder.alias;
    const subQuery = queryBuilder.clone().select(`${alias}.id`).skip(skip).take(take);

    // 对于TypeORM，需要创建新的查询构建器来避免skip/take的影响
    const mainQuery = queryBuilder
      .andWhere(`${alias}.id IN (${subQuery.getQuery()})`)
      .setParameters(subQuery.getParameters());

    return mainQuery;
  }

  /**
   * 私有方法：复制WHERE条件
   */
  private copyWhereConditions<T>(
    source: SelectQueryBuilder<T> | undefined,
    target: SelectQueryBuilder<T>,
  ): void {
    if (!source) return;

    // 这里需要根据TypeORM的具体实现来复制WHERE条件
    // 简化实现，实际使用时可能需要更复杂的逻辑
    const whereExpression = source.expressionMap.wheres;
    if (whereExpression && whereExpression.length > 0) {
      // 复制WHERE条件到目标查询构建器
      target.expressionMap.wheres = [...whereExpression];
      target.expressionMap.parameters = { ...source.expressionMap.parameters };
    }
  }
}
