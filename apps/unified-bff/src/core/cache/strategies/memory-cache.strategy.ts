import { Injectable, Logger } from '@nestjs/common';
import {
  CacheEntry,
  CacheEvictionPolicy,
  CacheKey,
  CacheOptions,
  CacheStats,
  CacheStrategy,
} from './cache-strategy.interface';

interface MemoryCacheConfig {
  maxSize: number;
  defaultTtl: number;
  evictionPolicy: CacheEvictionPolicy;
  checkPeriod: number; // 清理过期缓存的检查周期
}

@Injectable()
export class MemoryCacheStrategy implements CacheStrategy {
  readonly name = 'memory';
  private readonly logger = new Logger(MemoryCacheStrategy.name);
  private readonly cache = new Map<string, CacheEntry>();
  private readonly accessTimes = new Map<string, number>();
  private readonly accessCounts = new Map<string, number>();
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    hitRate: 0,
    totalKeys: 0,
    memoryUsage: 0,
    evictions: 0,
  };

  private cleanupInterval: ReturnType<typeof setInterval>;

  constructor(private readonly config: MemoryCacheConfig) {
    // 定期清理过期缓存
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, config.checkPeriod);
  }

  async get<T>(key: string | CacheKey): Promise<T | null> {
    const keyStr = this.normalizeKey(key);
    const entry = this.cache.get(keyStr);

    if (!entry) {
      this.stats.misses++;
      this.updateHitRate();
      return null;
    }

    // 检查是否过期
    if (this.isExpired(entry)) {
      this.cache.delete(keyStr);
      this.accessTimes.delete(keyStr);
      this.accessCounts.delete(keyStr);
      this.stats.misses++;
      this.updateHitRate();
      return null;
    }

    // 更新访问统计
    this.updateAccessStats(keyStr);
    entry.hits++;
    this.stats.hits++;
    this.updateHitRate();

    return entry.data as T;
  }

  async set<T>(key: string | CacheKey, value: T, options: CacheOptions = {}): Promise<void> {
    const keyStr = this.normalizeKey(key);
    const ttl = options.ttl || this.config.defaultTtl;
    const size = this.calculateSize(value);

    // 检查是否需要腾出空间
    if (this.cache.size >= this.config.maxSize) {
      await this.evict();
    }

    const entry: CacheEntry<T> = {
      data: value,
      timestamp: Date.now(),
      ttl,
      tags: options.tags || [],
      hits: 0,
      size,
    };

    this.cache.set(keyStr, entry);
    this.updateAccessStats(keyStr);
    this.updateStats();
  }

  async del(key: string | CacheKey): Promise<boolean> {
    const keyStr = this.normalizeKey(key);
    const existed = this.cache.has(keyStr);

    if (existed) {
      this.cache.delete(keyStr);
      this.accessTimes.delete(keyStr);
      this.accessCounts.delete(keyStr);
      this.updateStats();
    }

    return existed;
  }

  async invalidateByTags(tags: string[]): Promise<number> {
    let invalidated = 0;
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (entry.tags.some((tag) => tags.includes(tag))) {
        keysToDelete.push(key);
        invalidated++;
      }
    }

    for (const key of keysToDelete) {
      this.cache.delete(key);
      this.accessTimes.delete(key);
      this.accessCounts.delete(key);
    }

    this.updateStats();
    return invalidated;
  }

  async clear(): Promise<void> {
    this.cache.clear();
    this.accessTimes.clear();
    this.accessCounts.clear();
    this.stats = {
      hits: 0,
      misses: 0,
      hitRate: 0,
      totalKeys: 0,
      memoryUsage: 0,
      evictions: 0,
    };
  }

  async exists(key: string | CacheKey): Promise<boolean> {
    const keyStr = this.normalizeKey(key);
    const entry = this.cache.get(keyStr);
    return entry !== undefined && !this.isExpired(entry);
  }

  async ttl(key: string | CacheKey): Promise<number> {
    const keyStr = this.normalizeKey(key);
    const entry = this.cache.get(keyStr);

    if (!entry) {
      return -1;
    }

    const remaining = entry.ttl - (Date.now() - entry.timestamp);
    return Math.max(0, remaining);
  }

  async getStats(): Promise<CacheStats> {
    return { ...this.stats };
  }

  /**
   * 预热缓存
   */
  async warmup(
    keys: Array<{ key: string | CacheKey; loader: () => Promise<any>; options?: CacheOptions }>,
  ): Promise<void> {
    const promises = keys.map(async ({ key, loader, options }) => {
      try {
        const value = await loader();
        await this.set(key, value, options);
      } catch (error) {
        this.logger.warn(`Failed to warmup cache for key: ${this.normalizeKey(key)}`, error);
      }
    });

    await Promise.allSettled(promises);
  }

  /**
   * 批量获取
   */
  async mget<T>(keys: Array<string | CacheKey>): Promise<Array<T | null>> {
    const results = await Promise.all(keys.map((key) => this.get<T>(key)));
    return results;
  }

  /**
   * 批量设置
   */
  async mset<T>(
    entries: Array<{ key: string | CacheKey; value: T; options?: CacheOptions }>,
  ): Promise<void> {
    await Promise.all(entries.map(({ key, value, options }) => this.set(key, value, options)));
  }

  /**
   * 获取缓存键的详细信息
   */
  async getKeyInfo(key: string | CacheKey): Promise<{
    exists: boolean;
    ttl: number;
    hits: number;
    size: number;
    tags: string[];
  } | null> {
    const keyStr = this.normalizeKey(key);
    const entry = this.cache.get(keyStr);

    if (!entry || this.isExpired(entry)) {
      return null;
    }

    return {
      exists: true,
      ttl: await this.ttl(key),
      hits: entry.hits,
      size: entry.size,
      tags: entry.tags,
    };
  }

  private normalizeKey(key: string | CacheKey): string {
    if (typeof key === 'string') {
      return key;
    }

    const parts = [key.prefix, key.identifier];
    if (key.version) {
      parts.push(`v${key.version}`);
    }

    return parts.join(':');
  }

  private isExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.timestamp > entry.ttl;
  }

  private updateAccessStats(key: string): void {
    this.accessTimes.set(key, Date.now());
    this.accessCounts.set(key, (this.accessCounts.get(key) || 0) + 1);
  }

  private updateStats(): void {
    this.stats.totalKeys = this.cache.size;
    this.stats.memoryUsage = this.calculateTotalMemoryUsage();
  }

  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? this.stats.hits / total : 0;
  }

  private calculateSize(value: unknown): number {
    // 简化的大小计算
    const jsonStr = JSON.stringify(value);
    return jsonStr.length * 2; // 假设每个字符占用2字节
  }

  private calculateTotalMemoryUsage(): number {
    let total = 0;
    for (const entry of this.cache.values()) {
      total += entry.size;
    }
    return total;
  }

  private async evict(): Promise<void> {
    if (this.cache.size === 0) {
      return;
    }

    let keyToEvict: string;

    switch (this.config.evictionPolicy) {
      case CacheEvictionPolicy.LRU:
        keyToEvict = this.findLRUKey();
        break;
      case CacheEvictionPolicy.LFU:
        keyToEvict = this.findLFUKey();
        break;
      case CacheEvictionPolicy.FIFO:
        keyToEvict = this.cache.keys().next().value;
        break;
      case CacheEvictionPolicy.TTL:
        keyToEvict = this.findExpiredOrOldestKey();
        break;
      default:
        keyToEvict = this.findLRUKey();
    }

    if (keyToEvict) {
      await this.del(keyToEvict);
      this.stats.evictions++;
    }
  }

  private findLRUKey(): string {
    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, time] of this.accessTimes.entries()) {
      if (time < oldestTime) {
        oldestTime = time;
        oldestKey = key;
      }
    }

    return oldestKey;
  }

  private findLFUKey(): string {
    let leastUsedKey = '';
    let leastCount = Infinity;

    for (const [key, count] of this.accessCounts.entries()) {
      if (count < leastCount) {
        leastCount = count;
        leastUsedKey = key;
      }
    }

    return leastUsedKey;
  }

  private findExpiredOrOldestKey(): string {
    // 首先寻找过期的键
    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        return key;
      }
    }

    // 如果没有过期的键，返回最旧的键
    return this.findLRUKey();
  }

  private cleanup(): void {
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        expiredKeys.push(key);
      }
    }

    for (const key of expiredKeys) {
      this.cache.delete(key);
      this.accessTimes.delete(key);
      this.accessCounts.delete(key);
    }

    if (expiredKeys.length > 0) {
      this.updateStats();
      this.logger.debug(`Cleaned up ${expiredKeys.length} expired cache entries`);
    }
  }

  onModuleDestroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
  }
}
