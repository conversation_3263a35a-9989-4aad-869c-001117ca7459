import { Injectable, Logger } from '@nestjs/common';
// Redis暂时禁用直到依赖问题解决
// import { InjectRedis } from '@nestjs-modules/ioredis';
// import Redis from 'ioredis';
import * as zlib from 'zlib';
import { promisify } from 'util';
import { CacheStrategy, CacheKey, CacheOptions, CacheStats } from './cache-strategy.interface';

const gzip = promisify(zlib.gzip);
const gunzip = promisify(zlib.gunzip);

interface RedisCacheConfig {
  keyPrefix: string;
  defaultTtl: number;
  compressionThreshold: number; // 超过此大小的数据将被压缩
  maxRetries: number;
  retryDelayMs: number;
}

@Injectable()
export class RedisCacheStrategy implements CacheStrategy {
  readonly name = 'redis';
  private readonly logger = new Logger(RedisCacheStrategy.name);
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    hitRate: 0,
    totalKeys: 0,
    memoryUsage: 0,
    evictions: 0,
  };

  constructor(
    // @InjectRedis() private readonly redis: Redis,
    private readonly redis: unknown, // 暂时使用any类型
    private readonly config: RedisCacheConfig,
  ) {}

  async get<T>(key: string | CacheKey): Promise<T | null> {
    const keyStr = this.normalizeKey(key);

    try {
      const result = await this.redis.get(keyStr);

      if (result === null) {
        this.stats.misses++;
        this.updateHitRate();
        return null;
      }

      // 增加访问计数
      await this.redis.incr(`${keyStr}:hits`);

      this.stats.hits++;
      this.updateHitRate();

      return await this.deserialize<T>(result);
    } catch (error) {
      this.logger.error('Error occurred', error);
      this.stats.misses++;
      this.updateHitRate();
      return null;
    }
  }

  async set<T>(key: string | CacheKey, value: T, options: CacheOptions = {}): Promise<void> {
    const keyStr = this.normalizeKey(key);
    const ttl = options.ttl || this.config.defaultTtl;

    try {
      const serialized = await this.serialize(value, options);

      // 使用管道操作提高性能
      const pipeline = this.redis.pipeline();

      if (ttl > 0) {
        pipeline.setex(keyStr, Math.ceil(ttl / 1000), serialized);
      } else {
        pipeline.set(keyStr, serialized);
      }

      // 设置标签索引
      if (options.tags && options.tags.length > 0) {
        for (const tag of options.tags) {
          pipeline.sadd(`tag:${tag}`, keyStr);
          if (ttl > 0) {
            pipeline.expire(`tag:${tag}`, Math.ceil(ttl / 1000));
          }
        }
      }

      // 初始化访问计数
      pipeline.set(`${keyStr}:hits`, '0');
      if (ttl > 0) {
        pipeline.expire(`${keyStr}:hits`, Math.ceil(ttl / 1000));
      }

      await pipeline.exec();
    } catch (error) {
      this.logger.error('Error occurred', error);
      throw error;
    }
  }

  async del(key: string | CacheKey): Promise<boolean> {
    const keyStr = this.normalizeKey(key);

    try {
      const result = await this.redis.del(keyStr, `${keyStr}:hits`);
      return result > 0;
    } catch (error) {
      this.logger.error('Error occurred', error);
      return false;
    }
  }

  async invalidateByTags(tags: string[]): Promise<number> {
    let totalInvalidated = 0;

    try {
      for (const tag of tags) {
        const tagKey = `tag:${tag}`;
        const keys = await this.redis.smembers(tagKey);

        if (keys.length > 0) {
          // 批量删除键和对应的访问计数
          const keysToDelete = keys.flatMap((key: string) => [key, `${key}:hits`]);
          const deleted = await this.redis.del(...keysToDelete);
          totalInvalidated += Math.floor(deleted / 2); // 因为每个缓存键有两个键（数据+计数）
        }

        // 删除标签集合
        await this.redis.del(tagKey);
      }
    } catch (error) {
      this.logger.error('Error occurred', error);
    }

    return totalInvalidated;
  }

  async clear(): Promise<void> {
    try {
      const pattern = `${this.config.keyPrefix}*`;
      const keys = await this.redis.keys(pattern);

      if (keys.length > 0) {
        await this.redis.del(...keys);
      }

      // 重置统计
      this.stats = {
        hits: 0,
        misses: 0,
        hitRate: 0,
        totalKeys: 0,
        memoryUsage: 0,
        evictions: 0,
      };
    } catch (error) {
      this.logger.error('Error occurred', error);
      throw error;
    }
  }

  async exists(key: string | CacheKey): Promise<boolean> {
    const keyStr = this.normalizeKey(key);

    try {
      const result = await this.redis.exists(keyStr);
      return result === 1;
    } catch (error) {
      this.logger.error('Error occurred', error);
      return false;
    }
  }

  async ttl(key: string | CacheKey): Promise<number> {
    const keyStr = this.normalizeKey(key);

    try {
      const result = await this.redis.ttl(keyStr);
      return result * 1000; // 转换为毫秒
    } catch (error) {
      this.logger.error('Error occurred', error);
      return -1;
    }
  }

  async getStats(): Promise<CacheStats> {
    try {
      // 获取Redis统计信息
      const info = await this.redis.info('memory');
      const memoryMatch = info.match(/used_memory:(\d+)/);
      const memoryUsage = memoryMatch ? parseInt(memoryMatch[1]) : 0;

      // 统计键数量
      const pattern = `${this.config.keyPrefix}*`;
      const keys = await this.redis.keys(pattern);
      const totalKeys = keys.filter((key: string) => !key.includes(':hits')).length;

      return {
        ...this.stats,
        totalKeys,
        memoryUsage,
      };
    } catch (error) {
      this.logger.error('Error occurred', error);
      return this.stats;
    }
  }

  /**
   * 批量获取
   */
  async mget<T>(keys: Array<string | CacheKey>): Promise<Array<T | null>> {
    const keyStrs = keys.map((key) => this.normalizeKey(key));

    try {
      const results = await this.redis.mget(...keyStrs);

      const deserializedResults = await Promise.all(
        results.map(async (result: unknown, index: number) => {
          if (result === null) {
            this.stats.misses++;
            return null;
          }

          this.stats.hits++;
          // 增加访问计数
          await this.redis.incr(`${keyStrs[index]}:hits`);
          return await this.deserialize<T>(result);
        }),
      );

      this.updateHitRate();
      return deserializedResults;
    } catch (error) {
      this.logger.error('Error occurred', error);
      this.stats.misses += keys.length;
      this.updateHitRate();
      return keys.map((): null => null);
    }
  }

  /**
   * 批量设置
   */
  async mset<T>(
    entries: Array<{ key: string | CacheKey; value: T; options?: CacheOptions }>,
  ): Promise<void> {
    const pipeline = this.redis.pipeline();

    try {
      for (const { key, value, options = {} } of entries) {
        const keyStr = this.normalizeKey(key);
        const ttl = options.ttl || this.config.defaultTtl;
        const serialized = await this.serialize(value, options);

        if (ttl > 0) {
          pipeline.setex(keyStr, Math.ceil(ttl / 1000), serialized);
        } else {
          pipeline.set(keyStr, serialized);
        }

        // 设置标签索引
        if (options.tags && options.tags.length > 0) {
          for (const tag of options.tags) {
            pipeline.sadd(`tag:${tag}`, keyStr);
            if (ttl > 0) {
              pipeline.expire(`tag:${tag}`, Math.ceil(ttl / 1000));
            }
          }
        }

        // 初始化访问计数
        pipeline.set(`${keyStr}:hits`, '0');
        if (ttl > 0) {
          pipeline.expire(`${keyStr}:hits`, Math.ceil(ttl / 1000));
        }
      }

      await pipeline.exec();
    } catch (error) {
      this.logger.error('Error occurred', error);
      throw error;
    }
  }

  /**
   * 获取缓存键的详细信息
   */
  async getKeyInfo(key: string | CacheKey): Promise<{
    exists: boolean;
    ttl: number;
    hits: number;
    size: number;
    tags: string[];
  } | null> {
    const keyStr = this.normalizeKey(key);

    try {
      const [exists, ttl, hits, value] = await Promise.all([
        this.redis.exists(keyStr),
        this.redis.ttl(keyStr),
        this.redis.get(`${keyStr}:hits`).then((h: string) => parseInt(h || '0')),
        this.redis.get(keyStr),
      ]);

      if (!exists) {
        return null;
      }

      // 查找相关标签
      const tagPattern = 'tag:*';
      const tagKeys = await this.redis.keys(tagPattern);
      const tags: string[] = [];

      for (const tagKey of tagKeys) {
        const isMember = await this.redis.sismember(tagKey, keyStr);
        if (isMember) {
          tags.push(tagKey.replace('tag:', ''));
        }
      }

      return {
        exists: exists === 1,
        ttl: ttl * 1000, // 转换为毫秒
        hits,
        size: value ? Buffer.byteLength(value, 'utf8') : 0,
        tags,
      };
    } catch (error) {
      this.logger.error('Error occurred', error);
      return null;
    }
  }

  /**
   * 设置过期时间
   */
  async expire(key: string | CacheKey, ttl: number): Promise<boolean> {
    const keyStr = this.normalizeKey(key);

    try {
      const result = await this.redis.expire(keyStr, Math.ceil(ttl / 1000));
      return result === 1;
    } catch (error) {
      this.logger.error('Error occurred', error);
      return false;
    }
  }

  /**
   * 原子递增
   */
  async incr(key: string | CacheKey, delta: number = 1): Promise<number> {
    const keyStr = this.normalizeKey(key);

    try {
      return await this.redis.incrby(keyStr, delta);
    } catch (error) {
      this.logger.error('Error occurred', error);
      throw error;
    }
  }

  private normalizeKey(key: string | CacheKey): string {
    const baseKey =
      typeof key === 'string'
        ? key
        : `${key.prefix}:${key.identifier}${key.version ? `:v${key.version}` : ''}`;

    return `${this.config.keyPrefix}${baseKey}`;
  }

  private async serialize<T>(value: T, options: CacheOptions = {}): Promise<string> {
    let serialized = JSON.stringify(value);

    // 如果启用压缩且数据大小超过阈值
    if (options.compress && Buffer.byteLength(serialized) > this.config.compressionThreshold) {
      const compressed = await gzip(Buffer.from(serialized));
      serialized = `gzip:${compressed.toString('base64')}`;
    }

    return serialized;
  }

  private async deserialize<T>(data: string): Promise<T> {
    try {
      // 检查是否为压缩数据
      if (data.startsWith('gzip:')) {
        const compressed = Buffer.from(data.slice(5), 'base64');
        const decompressed = await gunzip(compressed);
        data = decompressed.toString();
      }

      return JSON.parse(data);
    } catch (error) {
      this.logger.error('Error occurred', error);
      throw error;
    }
  }

  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? this.stats.hits / total : 0;
  }
}
