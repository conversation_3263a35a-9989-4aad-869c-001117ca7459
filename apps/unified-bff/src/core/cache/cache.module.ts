import { CacheModule as NestCacheModule } from '@nestjs/cache-manager';
import { Global, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AdvancedCacheService } from './advanced-cache.service';
import { CacheService } from './cache.service';
import { MemoryCacheStrategy } from './strategies/memory-cache.strategy';

import {
  CacheEvictionPolicy,
  CacheStrategy,
  MultiLevelCacheConfig,
} from './strategies/cache-strategy.interface';

@Global()
@Module({
  imports: [
    ConfigModule,
    // 保持原有的NestJS缓存模块用于向后兼容
    NestCacheModule.register({
      ttl: 300, // 5分钟默认TTL
      max: 1000, // 最大缓存条目数
    }),
  ],
  providers: [
    CacheService,
    // 内存缓存策略
    {
      provide: 'MEMORY_CACHE_STRATEGY',
      useFactory: () =>
        new MemoryCacheStrategy({
          maxSize: 1000,
          defaultTtl: 5 * 60 * 1000, // 5分钟
          evictionPolicy: CacheEvictionPolicy.LRU,
          checkPeriod: 60 * 1000, // 1分钟检查一次
        }),
    },
    // 缓存策略注册表
    {
      provide: 'CACHE_STRATEGIES',
      useFactory: (memoryStrategy: CacheStrategy) => {
        const strategies = new Map<string, CacheStrategy>();
        strategies.set('memory', memoryStrategy);
        return strategies;
      },
      inject: ['MEMORY_CACHE_STRATEGY'],
    },
    // 多级缓存配置
    {
      provide: 'MULTI_LEVEL_CONFIG',
      useValue: {
        l1: {
          strategy: 'memory',
          maxSize: 500,
          ttl: 5 * 60 * 1000, // 5分钟
        },
        l2: {
          strategy: 'memory', // 暂时使用内存，实际应该是Redis
          ttl: 30 * 60 * 1000, // 30分钟
        },
      } as MultiLevelCacheConfig,
    },
    // 缓存配置
    {
      provide: 'CACHE_CONFIG',
      useFactory: (_configService: ConfigService) => ({
        defaultStrategy: 'memory',
        patterns: [
          // 用户数据使用内存缓存，TTL较短
          {
            pattern: 'users:*',
            strategy: 'memory',
            options: { ttl: 5 * 60 * 1000, tags: ['users'] },
          },
          // 文档数据使用多级缓存
          {
            pattern: 'documents:*',
            strategy: 'memory',
            options: { ttl: 15 * 60 * 1000, tags: ['documents'] },
          },
          // 分析数据缓存时间较长
          {
            pattern: 'analytics:*',
            strategy: 'memory',
            options: { ttl: 60 * 60 * 1000, tags: ['analytics'] },
          },
          // 系统配置缓存时间很长
          {
            pattern: 'config:*',
            strategy: 'memory',
            options: { ttl: 4 * 60 * 60 * 1000, tags: ['config'] },
          },
        ],
        enableMetrics: true,
        enableCircuitBreaker: true,
        circuitBreakerConfig: {
          failureThreshold: 5,
          timeout: 60 * 1000, // 1分钟
          monitoringPeriod: 10 * 60 * 1000, // 10分钟
        },
      }),
      inject: [ConfigService],
    },
    // 高级缓存服务
    AdvancedCacheService,
  ],
  exports: [CacheService, NestCacheModule, AdvancedCacheService, 'CACHE_STRATEGIES'],
})
export class CacheModule {}
