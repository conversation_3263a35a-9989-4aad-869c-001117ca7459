import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { SecurityService } from '../security.service';

// IP白名单装饰器
export const IP_WHITELIST_KEY = 'ipWhitelist';
export const RequireIpWhitelist = () => Reflect.metadata(IP_WHITELIST_KEY, true);

// 跳过IP检查装饰器
export const SKIP_IP_CHECK_KEY = 'skipIpCheck';
export const SkipIpCheck = () => Reflect.metadata(SKIP_IP_CHECK_KEY, true);

@Injectable()
export class IpWhitelistGuard implements CanActivate {
  constructor(
    private readonly reflector: Reflector,
    private readonly securityService: SecurityService,
  ) {}

  canActivate(context: ExecutionContext): boolean {
    // 检查是否跳过IP检查
    const skipIpCheck = this.reflector.getAllAndOverride<boolean>(SKIP_IP_CHECK_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (skipIpCheck) {
      return true;
    }

    // 检查是否需要IP白名单验证
    const requireWhitelist = this.reflector.getAllAndOverride<boolean>(IP_WHITELIST_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requireWhitelist) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const clientIp = this.getClientIp(request);

    // 验证IP是否在白名单中
    if (!this.securityService.isIpWhitelisted(clientIp)) {
      throw new ForbiddenException(`IP地址 ${clientIp} 不在白名单中`);
    }

    return true;
  }

  /**
   * 获取客户端真实IP
   */
  private getClientIp(request: unknown): string {
    return (
      request.headers['x-forwarded-for']?.split(',')[0]?.trim() ||
      request.headers['x-real-ip'] ||
      request.connection?.remoteAddress ||
      request.socket?.remoteAddress ||
      request.ip ||
      '0.0.0.0'
    );
  }
}
