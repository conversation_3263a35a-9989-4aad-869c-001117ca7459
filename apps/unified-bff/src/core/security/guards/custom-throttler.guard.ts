import { ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ThrottlerGuard } from '@nestjs/throttler';
import { SecurityService } from '../security.service';

// 跳过限流的装饰器
export const SKIP_THROTTLE_KEY = 'skipThrottle';
export const SkipThrottle = () => Reflect.metadata(SKIP_THROTTLE_KEY, true);

// 自定义限流配置装饰器
export const THROTTLE_CONFIG_KEY = 'throttleConfig';
export const CustomThrottle = (config: { name: string; limit: number; ttl: number }) =>
  Reflect.metadata(THROTTLE_CONFIG_KEY, config);

@Injectable()
export class CustomThrottlerGuard extends ThrottlerGuard {
  constructor(
    options: unknown,
    storageService: unknown,
    reflector: Reflector,
    private readonly securityService: SecurityService,
  ) {
    super(options, storageService, reflector);
  }

  protected override async shouldSkip(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    // 检查是否标记跳过限流
    const skipThrottle = this.reflector.getAllAndOverride<boolean>(SKIP_THROTTLE_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (skipThrottle) {
      return true;
    }

    // 健康检查接口跳过限流
    if (request.url === '/health' || request.url.startsWith('/health/')) {
      return true;
    }

    // API文档接口跳过限流
    if (request.url.startsWith('/api/docs')) {
      return true;
    }

    // 白名单IP跳过限流
    const clientIp = this.getClientIp(request);
    if (this.securityService.isIpWhitelisted(clientIp)) {
      return true;
    }

    return false;
  }

  protected override getTracker(req: unknown): Promise<string> {
    // 基于IP和用户ID的组合追踪
    const clientIp = this.getClientIp(req);
    const userId = req.user?.userId || 'anonymous';

    return Promise.resolve(`${clientIp}-${userId}`);
  }

  protected override generateKey(context: ExecutionContext, tracker: string, name: string): string {
    const request = context.switchToHttp().getRequest();
    const method = request.method;
    const route = request.route?.path || request.url;

    // 生成更精确的key，包含路由信息
    return `${tracker}-${name}-${method}-${route}`;
  }

  /**
   * 获取客户端真实IP
   */
  private getClientIp(request: unknown): string {
    return (
      request.headers['x-forwarded-for']?.split(',')[0] ||
      request.headers['x-real-ip'] ||
      request.connection?.remoteAddress ||
      request.socket?.remoteAddress ||
      '0.0.0.0'
    );
  }
}
