import { SetMetadata } from '@nestjs/common';
import { Permission } from '@/shared/enums/permission.enum';

export const PERMISSIONS_KEY = 'permissions';

/**
 * 权限装饰器 - 用于标记接口所需的权限
 * @param permissions 所需权限列表
 */
export const RequirePermissions = (...permissions: Permission[]) =>
  SetMetadata(PERMISSIONS_KEY, permissions);

/**
 * 检查权限装饰器 - 用于方法级别权限检查
 * @param permission 单个权限
 */
export const CheckPermission = (permission: Permission) =>
  SetMetadata(PERMISSIONS_KEY, [permission]);

/**
 * 资源所有者装饰器 - 用于标记用户只能访问自己的资源
 */
export const RESOURCE_OWNER_KEY = 'resource_owner';
export const ResourceOwnerOnly = () => SetMetadata(RESOURCE_OWNER_KEY, true);
