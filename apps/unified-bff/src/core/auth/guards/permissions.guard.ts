import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Permission, RolePermissions } from '@/shared/enums/permission.enum';
import { UserRole } from '@/shared/enums/user-role.enum';
import { PERMISSIONS_KEY, RESOURCE_OWNER_KEY } from '../decorators/permissions.decorator';
import { IS_PUBLIC_KEY } from '../decorators/public.decorator';

@Injectable()
export class PermissionsGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    // 检查是否为公共接口
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    // 获取所需权限
    const requiredPermissions = this.reflector.getAllAndOverride<Permission[]>(PERMISSIONS_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    // 如果没有设置权限要求，允许通过（向后兼容）
    if (!requiredPermissions || requiredPermissions.length === 0) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      throw new UnauthorizedException('用户未认证');
    }

    // 检查资源所有者权限
    const isResourceOwnerOnly = this.reflector.getAllAndOverride<boolean>(RESOURCE_OWNER_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isResourceOwnerOnly) {
      const resourceUserId = this.extractResourceUserId(request);
      if (user.userId !== resourceUserId && !this.isSuperAdmin(user.role)) {
        throw new ForbiddenException('只能访问自己的资源');
      }
    }

    // 获取用户权限
    const userPermissions = this.getUserPermissions(user.role);

    // 检查用户是否拥有所需权限
    const hasPermission = requiredPermissions.some((permission) =>
      userPermissions.includes(permission),
    );

    if (!hasPermission) {
      throw new ForbiddenException(`缺少所需权限: ${requiredPermissions.join(', ')}`);
    }

    return true;
  }

  /**
   * 获取用户角色对应的权限列表
   */
  private getUserPermissions(role: UserRole): Permission[] {
    return RolePermissions[role] || [];
  }

  /**
   * 检查是否为超级管理员
   */
  private isSuperAdmin(role: UserRole): boolean {
    return role === UserRole.SUPER_ADMIN;
  }

  /**
   * 从请求中提取资源用户ID
   * 支持路径参数、查询参数和请求体
   */
  private extractResourceUserId(request: unknown): string | null {
    // 从路径参数中获取
    if (request.params?.userId) {
      return request.params.userId;
    }

    if (request.params?.id) {
      return request.params.id;
    }

    // 从查询参数中获取
    if (request.query?.userId) {
      return request.query.userId;
    }

    // 从请求体中获取
    if (request.body?.userId) {
      return request.body.userId;
    }

    return null;
  }
}
