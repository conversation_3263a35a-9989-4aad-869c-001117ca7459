import { CallHandler, ExecutionContext, Injectable, Logger, NestInterceptor } from '@nestjs/common';
import { Request, Response } from 'express';
import { Observable } from 'rxjs';
import { catchError, finalize, tap } from 'rxjs/operators';
import { PrometheusService } from '../prometheus/prometheus.service';

@Injectable()
export class PrometheusInterceptor implements NestInterceptor {
  private readonly logger = new Logger(PrometheusInterceptor.name);

  constructor(private readonly prometheusService: PrometheusService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const startTime = Date.now();
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();

    const { method, url, route } = request;
    const routePath = route?.path || this.sanitizeUrl(url);
    const apiVersion = this.extractApiVersion(url);

    // 增加正在处理的请求计数
    this.prometheusService.incrementRequestsInFlight();

    return next.handle().pipe(
      tap(() => {
        const duration = Date.now() - startTime;
        const { statusCode } = response;

        // 记录HTTP请求指标
        this.prometheusService.recordHttpRequest(
          method,
          routePath,
          statusCode,
          duration,
          apiVersion,
        );

        // 记录业务指标
        this.recordBusinessMetrics(method, url, statusCode, request);
      }),
      catchError((error) => {
        const duration = Date.now() - startTime;
        const statusCode = error.status || 500;

        // 记录HTTP请求指标（包含错误）
        this.prometheusService.recordHttpRequest(
          method,
          routePath,
          statusCode,
          duration,
          apiVersion,
        );

        // 记录错误指标
        this.recordErrorMetrics(error, method, url);

        throw error;
      }),
      finalize(() => {
        // 减少正在处理的请求计数
        this.prometheusService.decrementRequestsInFlight();
      }),
    );
  }

  /**
   * 记录业务指标
   */
  private recordBusinessMetrics(
    method: string,
    url: string,
    statusCode: number,
    request: Request,
  ): void {
    // 用户注册
    if (url.includes('/auth/register') && method === 'POST' && statusCode === 201) {
      this.prometheusService.recordUserRegistration('web');
    }

    // 用户认证
    if (url.includes('/auth/login') && method === 'POST') {
      const result = statusCode === 200 ? 'success' : 'failure';
      this.prometheusService.recordAuthenticationAttempt('login', result);
    }

    if (url.includes('/auth/refresh') && method === 'POST') {
      const result = statusCode === 200 ? 'success' : 'failure';
      this.prometheusService.recordAuthenticationAttempt('refresh', result);
    }

    if (url.includes('/auth/logout') && method === 'POST') {
      this.prometheusService.recordAuthenticationAttempt('logout', 'success');
    }

    // 文档上传
    if (url.includes('/documents') && method === 'POST' && statusCode === 201) {
      const userRole = this.extractUserRole(request);
      this.prometheusService.recordDocumentUpload('document', userRole);
    }

    if (url.includes('/documents/upload') && method === 'POST' && statusCode === 200) {
      const userRole = this.extractUserRole(request);
      this.prometheusService.recordDocumentUpload('file', userRole);
    }

    // API使用统计
    if (url.startsWith('/api/')) {
      // 这里可以添加更多的API使用统计
    }
  }

  /**
   * 记录错误指标
   */
  private recordErrorMetrics(error: unknown, method: string, _url: string): void {
    const errorType = this.getErrorType(error);

    // 数据库相关错误
    if (this.isDatabaseError(error)) {
      this.prometheusService.recordDatabaseError(errorType, method.toLowerCase());
    }

    // 缓存相关错误
    if (this.isCacheError(error)) {
      this.prometheusService.recordCacheError(errorType, 'read');
    }

    // 认证相关错误
    if (error.status === 401 || error.status === 403) {
      this.prometheusService.recordAuthenticationAttempt('login', 'failure');
    }
  }

  /**
   * 提取API版本
   */
  private extractApiVersion(url: string): string {
    const versionMatch = url.match(/\/v(\d+)\//);
    return versionMatch ? `v${versionMatch[1]}` : 'v1';
  }

  /**
   * 提取用户角色
   */
  private extractUserRole(request: Request): string {
    if (request.user) {
      return (request.user as any).role || 'user';
    }
    return 'anonymous';
  }

  /**
   * 清理URL，移除查询参数和动态参数
   */
  private sanitizeUrl(url: string): string {
    // 移除查询参数
    const baseUrl = url.split('?')[0];

    // 替换UUID和数字ID为参数占位符
    return baseUrl
      .replace(/\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi, '/:id')
      .replace(/\/\d+/g, '/:id')
      .replace(/\/[a-f0-9]{24}/g, '/:id'); // MongoDB ObjectId
  }

  /**
   * 获取错误类型
   */
  private getErrorType(error: unknown): string {
    if (error.name) {
      return error.name;
    }

    const status = error.status || error.statusCode;
    switch (status) {
      case 400:
        return 'BadRequestException';
      case 401:
        return 'UnauthorizedException';
      case 403:
        return 'ForbiddenException';
      case 404:
        return 'NotFoundException';
      case 409:
        return 'ConflictException';
      case 422:
        return 'ValidationException';
      case 429:
        return 'ThrottlerException';
      case 500:
        return 'InternalServerErrorException';
      case 502:
        return 'BadGatewayException';
      case 503:
        return 'ServiceUnavailableException';
      case 504:
        return 'GatewayTimeoutException';
      default:
        return 'UnknownException';
    }
  }

  /**
   * 检查是否为数据库错误
   */
  private isDatabaseError(error: unknown): boolean {
    if (!error.message) return false;

    const databaseErrorKeywords = [
      'connection',
      'query',
      'database',
      'postgres',
      'sql',
      'transaction',
      'deadlock',
      'timeout',
      'constraint',
    ];

    const message = error.message.toLowerCase();
    return databaseErrorKeywords.some((keyword) => message.includes(keyword));
  }

  /**
   * 检查是否为缓存错误
   */
  private isCacheError(error: unknown): boolean {
    if (!error.message) return false;

    const cacheErrorKeywords = ['cache', 'redis', 'memory', 'eviction', 'expired'];

    const message = error.message.toLowerCase();
    return cacheErrorKeywords.some((keyword) => message.includes(keyword));
  }
}
