import { Injectable, NestInterceptor, ExecutionContext, CallHandler, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { Request, Response } from 'express';
import { MonitoringService } from '../monitoring.service';
import { LoggingService } from '../../logging/logging.service';

@Injectable()
export class MonitoringInterceptor implements NestInterceptor {
  private readonly logger = new Logger(MonitoringInterceptor.name);

  constructor(
    private readonly monitoringService: MonitoringService,
    private readonly loggingService: LoggingService,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const startTime = Date.now();
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();

    const { method, url, route } = request;
    const routePath = route?.path || url;

    return next.handle().pipe(
      tap(() => {
        const responseTime = Date.now() - startTime;
        const { statusCode } = response;

        // 记录API请求指标
        this.monitoringService.recordApiRequest(method, routePath, statusCode, responseTime);

        // 记录特殊业务指标
        this.recordBusinessMetrics(method, url, statusCode, request);

        // 记录慢请求
        if (responseTime > 1000) {
          this.loggingService.logPerformance('SLOW_REQUEST', responseTime, {
            method,
            url: routePath,
            statusCode,
            userId: this.extractUserId(request),
          });
        }
      }),
      catchError((error) => {
        const responseTime = Date.now() - startTime;
        const statusCode = error.status || 500;

        // 记录API请求指标（包含错误）
        this.monitoringService.recordApiRequest(method, routePath, statusCode, responseTime);

        // 记录错误指标
        this.monitoringService.recordError(this.getErrorType(error), error);

        // 记录安全相关错误
        if (statusCode === 401 || statusCode === 403 || statusCode === 429) {
          this.loggingService.logSecurityEvent(`HTTP_${statusCode}`, {
            method,
            url: routePath,
            userAgent: request.headers['user-agent'],
            ip: this.getClientIp(request),
            userId: this.extractUserId(request),
          });
        }

        throw error;
      }),
    );
  }

  /**
   * 记录业务指标
   */
  private recordBusinessMetrics(
    method: string,
    url: string,
    statusCode: number,
    request: Request,
  ): void {
    // 认证相关指标
    if (url.includes('/auth/login') && statusCode === 200) {
      this.monitoringService.recordAuthEvent('login');
    } else if (url.includes('/auth/logout') && statusCode === 200) {
      this.monitoringService.recordAuthEvent('logout');
    } else if (url.includes('/auth/login') && statusCode >= 400) {
      this.monitoringService.recordAuthEvent('failure');
    }

    // 用户管理指标
    if (url.includes('/users')) {
      if (method === 'POST' && statusCode === 201) {
        this.loggingService.logBusinessEvent('USER_CREATED', {
          adminId: this.extractUserId(request),
        });
      } else if (method === 'DELETE' && statusCode === 200) {
        this.loggingService.logBusinessEvent('USER_DELETED', {
          adminId: this.extractUserId(request),
        });
      } else if (method === 'PUT' && statusCode === 200) {
        this.loggingService.logBusinessEvent('USER_UPDATED', {
          adminId: this.extractUserId(request),
        });
      }
    }

    // 文档管理指标
    if (url.includes('/documents')) {
      if (method === 'POST' && statusCode === 201) {
        this.loggingService.logBusinessEvent('DOCUMENT_CREATED', {
          userId: this.extractUserId(request),
        });
      } else if (method === 'DELETE' && statusCode === 200) {
        this.loggingService.logBusinessEvent('DOCUMENT_DELETED', {
          userId: this.extractUserId(request),
        });
      } else if (url.includes('/upload') && method === 'POST' && statusCode === 200) {
        this.loggingService.logBusinessEvent('DOCUMENT_UPLOADED', {
          userId: this.extractUserId(request),
        });
      }
    }

    // 系统管理指标
    if (url.includes('/admin/')) {
      this.loggingService.logBusinessEvent('ADMIN_ACTION', {
        action: `${method} ${url}`,
        adminId: this.extractUserId(request),
        statusCode,
      });
    }

    // API使用统计
    if (url.includes('/api/')) {
      const apiVersion = this.extractApiVersion(url);
      if (apiVersion) {
        this.loggingService.logBusinessEvent('API_USAGE', {
          version: apiVersion,
          method,
          endpoint: url,
          userId: this.extractUserId(request),
        });
      }
    }
  }

  /**
   * 获取错误类型
   */
  private getErrorType(error: unknown): string {
    if (error.name) {
      return error.name;
    }

    const status = error.status || error.statusCode;
    switch (status) {
      case 400:
        return 'BadRequestException';
      case 401:
        return 'UnauthorizedException';
      case 403:
        return 'ForbiddenException';
      case 404:
        return 'NotFoundException';
      case 409:
        return 'ConflictException';
      case 422:
        return 'ValidationException';
      case 429:
        return 'ThrottlerException';
      case 500:
        return 'InternalServerErrorException';
      case 502:
        return 'BadGatewayException';
      case 503:
        return 'ServiceUnavailableException';
      case 504:
        return 'GatewayTimeoutException';
      default:
        return 'UnknownException';
    }
  }

  /**
   * 提取用户ID
   */
  private extractUserId(request: Request): string | undefined {
    if (request.user) {
      return (request.user as any).id || (request.user as any).sub;
    }
    return undefined;
  }

  /**
   * 提取API版本
   */
  private extractApiVersion(url: string): string | null {
    const versionMatch = url.match(/\/v(\d+)\//);
    return versionMatch ? `v${versionMatch[1]}` : null;
  }

  /**
   * 获取客户端IP
   */
  private getClientIp(request: Request): string {
    return (
      (request.headers['x-forwarded-for'] as string) ||
      (request.headers['x-real-ip'] as string) ||
      request.connection?.remoteAddress ||
      request.socket?.remoteAddress ||
      'unknown'
    );
  }
}
