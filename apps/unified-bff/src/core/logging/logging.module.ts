import { Module, Global } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { LoggingService } from './logging.service';
import { LoggingInterceptor } from './interceptors/logging.interceptor';
import { MonitoringService } from '../monitoring/monitoring.service';
import { MonitoringInterceptor } from '../monitoring/interceptors/monitoring.interceptor';
import { PrometheusService } from '../monitoring/prometheus/prometheus.service';
import { PrometheusInterceptor } from '../monitoring/interceptors/prometheus.interceptor';

@Global()
@Module({
  imports: [ConfigModule],
  providers: [
    LoggingService,
    MonitoringService,
    PrometheusService,
    // 全局注册日志拦截器
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },
    // 全局注册监控拦截器
    {
      provide: APP_INTERCEPTOR,
      useClass: MonitoringInterceptor,
    },
    // 全局注册Prometheus拦截器
    {
      provide: APP_INTERCEPTOR,
      useClass: PrometheusInterceptor,
    },
  ],
  exports: [LoggingService, MonitoringService, PrometheusService],
})
export class LoggingModule {}
