import { HttpExceptionFilter } from '@/shared/filters/http-exception.filter';
import { LoggingInterceptor } from '@/shared/interceptors/logging.interceptor';
import { ResponseInterceptor } from '@/shared/interceptors/response.interceptor';
import { ValidationPipe, VersioningType } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create<NestFastifyApplication>(AppModule, new FastifyAdapter());
  const configService = app.get(ConfigService);

  // 注册Fastify插件
  await app.register(await import('@fastify/helmet'), {
    contentSecurityPolicy: {
      directives: {
        defaultSrc: [`'self'`],
        styleSrc: [`'self'`, `'unsafe-inline'`],
        scriptSrc: [`'self'`],
        objectSrc: [`'none'`],
        imgSrc: [`'self'`, 'data:', 'validator.swagger.io'],
        fontSrc: [`'self'`],
      },
    },
  });

  // 注册multipart支持
  await app.register(await import('@fastify/multipart'), {
    limits: {
      fileSize: 100 * 1024 * 1024, // 100MB
      files: 10,
    },
  });

  // 启用CORS
  const corsConfig = {
    origin: [
      'http://localhost:3200', // Admin Web
      'http://localhost:3100', // Mobile Web
      'http://localhost:8080', // Dev server
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  };
  app.enableCors(corsConfig);

  // API版本控制
  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
  });

  // 全局管道、过滤器、拦截器
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  app.useGlobalFilters(new HttpExceptionFilter());
  app.useGlobalInterceptors(new LoggingInterceptor(), new ResponseInterceptor());

  // API文档配置
  const config = new DocumentBuilder()
    .setTitle('MasteryOS Unified API')
    .setDescription('MasteryOS 统一 BFF API - 支持管理端和移动端访问')
    .setVersion('1.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token',
        in: 'header',
      },
      'JWT-auth',
    )
    .addTag('Auth', '认证相关接口')
    .addTag('Admin - Users', '管理端 - 用户管理')
    .addTag('Admin - Documents', '管理端 - 文档管理')
    .addTag('Admin - Analytics', '管理端 - 数据分析')
    .addTag('Mobile - Profile', '移动端 - 个人资料')
    .addTag('Mobile - Learning', '移动端 - 学习模块')
    .addTag('Mobile - Documents', '移动端 - 文档浏览')
    .addTag('Common', '通用接口')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
      tagsSorter: 'alpha',
      operationsSorter: 'alpha',
    },
  });

  // 健康检查端点
  app
    .getHttpAdapter()
    .getInstance()
    .get('/health', async (_request, _reply) => {
      const healthData = {
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env['npm_package_version'] || '1.0.0',
        environment: configService.get('NODE_ENV') || 'development',
        memory: {
          used: `${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB`,
          total: `${Math.round(process.memoryUsage().heapTotal / 1024 / 1024)}MB`,
        },
      };
      return healthData;
    });

  // 根路径重定向到API文档
  app
    .getHttpAdapter()
    .getInstance()
    .get('/', async (request, reply) => {
      return reply.redirect('/api/docs');
    });

  const port = configService.get('PORT') || 3102;
  await app.listen(port, '0.0.0.0');

  console.info(`🚀 MasteryOS Unified BFF running on http://localhost:${port}`);
  console.info(`📚 API docs available at http://localhost:${port}/api/docs`);
  console.info(`🏥 Health check at http://localhost:${port}/health`);
  console.info(`🌍 Environment: ${configService.get('NODE_ENV') || 'development'}`);
}

void bootstrap().catch((error) => {
  console.error('应用启动失败:', error);
  process.exit(1);
});
