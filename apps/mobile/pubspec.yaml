name: masteryos_mobile
description: "MasteryOS Mobile - 智能技能发展移动应用"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # UI & Navigation - 最新版本
  cupertino_icons: ^1.0.8
  go_router: ^16.1.0             # 最新稳定版路由
  
  # State Management - 最新版本
  flutter_bloc: ^9.1.1           # 最新BLoC状态管理
  bloc: ^9.0.0                   # BLoC核心
  equatable: ^2.0.7              # 对象比较工具
  
  # HTTP & API - 最新版本
  dio: ^5.8.0                    # 最新HTTP客户端
  retrofit: ^4.5.0               # REST API代码生成
  json_annotation: ^4.9.0        # JSON序列化注解
  connectivity_plus: ^6.1.1      # 网络连接检测
  
  # Local Storage - 最新版本
  shared_preferences: ^2.5.3     # 键值对存储 (最新)
  flutter_secure_storage: ^9.2.2 # 安全存储
  hive_flutter: ^1.1.0          # 高性能NoSQL数据库
  
  # Dependency Injection - 最新版本
  get_it: ^8.0.2                 # 服务定位器
  injectable: ^2.5.0             # 依赖注入代码生成
  # provider已被get_it替代，但保留以支持flutter_bloc内部使用
  
  # Utils - 最新版本
  intl: ^0.20.2                  # 国际化 (最新)
  uuid: ^4.5.1                   # UUID生成器
  path_provider: ^2.1.5          # 文件路径
  logging: ^1.3.0                # 日志记录
  
  # File Handling - 最新版本
  file_picker: ^10.2.1           # 文件选择器
  path: ^1.9.1                   # 路径操作
  image_picker: ^1.1.2           # 图片选择器
  
  # PDF & Documents - 最新版本
  syncfusion_flutter_pdfviewer: ^30.1.42  # PDF查看器
  
  # Flutter 3.32新特性支持
  animations: ^2.0.11            # 优雅动画
  cached_network_image: ^3.4.1   # 图片缓存
  
  # 性能监控和错误追踪
  sentry_flutter: ^8.13.0        # 错误追踪和性能监控
  
  # 环境配置
  flutter_dotenv: ^5.2.1         # 环境变量管理

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Linting & Code Quality - 最新版本
  flutter_lints: ^6.0.0
  very_good_analysis: ^7.0.0    # 更严格的代码分析
  
  # Code Generation - 最新稳定版本
  build_runner: ^2.6.0           # 代码生成器
  retrofit_generator: ^10.0.1    # Retrofit生成器
  json_serializable: ^6.10.0     # JSON序列化
  injectable_generator: ^2.8.1   # 依赖注入生成器
  freezed: ^3.2.0                # 不可变对象生成器
  freezed_annotation: ^3.1.0
  # envied_generator: ^0.5.4+1     # 环境变量代码生成
  
  # Testing - 最新版本
  bloc_test: ^10.0.0             # BLoC测试
  mocktail: ^1.0.4               # Mock框架
  flutter_test_robots: ^0.0.24   # UI测试辅助

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true
  
  # 国际化支持
  generate: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
