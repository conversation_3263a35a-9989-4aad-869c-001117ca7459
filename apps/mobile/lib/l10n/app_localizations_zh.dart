// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appTitle => '万时通';

  @override
  String get welcome => '欢迎使用万时通';

  @override
  String get home => '首页';

  @override
  String get documents => '文档';

  @override
  String get learning => '学习';

  @override
  String get profile => '个人';

  @override
  String get skills => '技能';

  @override
  String get progress => '进度';

  @override
  String get achievements => '成就';

  @override
  String hoursTracked(int count) {
    return '已记录 $count 小时';
  }

  @override
  String get startLearning => '开始学习';

  @override
  String get continueSession => '继续学习';

  @override
  String get endSession => '结束学习';

  @override
  String get uploadDocument => '上传文档';

  @override
  String get askQuestion => '提问';

  @override
  String get loading => '加载中...';

  @override
  String get error => '发生错误';

  @override
  String get retry => '重试';

  @override
  String get settings => '设置';

  @override
  String get darkMode => '深色模式';

  @override
  String get notifications => '通知';

  @override
  String get language => '语言';
}
