// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'MasteryOS';

  @override
  String get welcome => 'Welcome to MasteryOS';

  @override
  String get home => 'Home';

  @override
  String get documents => 'Documents';

  @override
  String get learning => 'Learning';

  @override
  String get profile => 'Profile';

  @override
  String get skills => 'Skills';

  @override
  String get progress => 'Progress';

  @override
  String get achievements => 'Achievements';

  @override
  String hoursTracked(int count) {
    return '$count hours tracked';
  }

  @override
  String get startLearning => 'Start Learning';

  @override
  String get continueSession => 'Continue Session';

  @override
  String get endSession => 'End Session';

  @override
  String get uploadDocument => 'Upload Document';

  @override
  String get askQuestion => 'Ask a Question';

  @override
  String get loading => 'Loading...';

  @override
  String get error => 'An error occurred';

  @override
  String get retry => 'Retry';

  @override
  String get settings => 'Settings';

  @override
  String get darkMode => 'Dark Mode';

  @override
  String get notifications => 'Notifications';

  @override
  String get language => 'Language';
}
