import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:masteryos_mobile/core/app/app.dart';
import 'package:masteryos_mobile/core/app/app_config.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 加载环境变量
  await dotenv.load(fileName: '.env');

  // 初始化Sentry错误追踪和性能监控
  await SentryFlutter.init(
    (options) {
      options.dsn = kReleaseMode
          ? dotenv.env['SENTRY_DSN_PROD'] ??
                '' // 生产环境DSN
          : dotenv.env['SENTRY_DSN_DEV'] ?? ''; // 开发环境DSN

      // 设置采样率
      options.tracesSampleRate = kReleaseMode ? 0.3 : 1.0; // 生产环境30%，开发环境100%

      // 设置环境
      options.environment = kReleaseMode ? 'production' : 'development';

      // 启用性能监控
      options.enableAutoPerformanceTracing = true;

      // 设置发布版本
      options.release = 'masteryos-mobile@1.0.0+1';

      // 启用屏幕截图附件（仅错误时）
      options.attachScreenshot = true;

      // 启用视图层次结构附件
      options.attachViewHierarchy = true;

      // 附加用户上下文
      options.beforeSend = (SentryEvent event, Hint hint) async {
        // 可以在这里添加用户信息或其他上下文
        return event;
      };
    },
    appRunner: () {
      // 配置应用环境 - 使用unified-bff服务
      final config = AppConfig(
        appName: 'MasteryOS Mobile',
        baseUrl:
            dotenv.env['API_BASE_URL'] ??
            'http://localhost:3102', // unified-bff服务端口
        environment: AppEnvironment.development,
      );

      runApp(MasteryOSApp(config: config));
    },
  );
}
