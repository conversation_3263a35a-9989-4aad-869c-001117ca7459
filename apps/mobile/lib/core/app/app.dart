import 'package:flutter/material.dart';
import 'package:masteryos_mobile/core/app/app_config.dart';
import 'package:masteryos_mobile/core/router/app_router.dart';
import 'package:masteryos_mobile/core/theme/app_theme.dart';

class MasteryOSApp extends StatelessWidget {
  final AppConfig config;

  const MasteryOSApp({required this.config, super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: config.appName,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      routerConfig: AppRouter.router,
      debugShowCheckedModeBanner: config.isDevelopment,
    );
  }
}
