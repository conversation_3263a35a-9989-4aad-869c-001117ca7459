enum AppEnvironment { development, staging, production }

class AppConfig {
  final String appName;
  final String baseUrl;
  final AppEnvironment environment;

  const AppConfig({
    required this.appName,
    required this.baseUrl,
    required this.environment,
  });

  bool get isDevelopment => environment == AppEnvironment.development;
  bool get isStaging => environment == AppEnvironment.staging;
  bool get isProduction => environment == AppEnvironment.production;

  // API配置 - 使用unified-bff服务的路径结构
  String get apiUrl => '$baseUrl/api';
  String get commonApiUrl => '$baseUrl/api/v1';
  String get mobileApiUrl => '$baseUrl/api/v1/mobile';

  // 认证API端点 (公共API)
  static const String loginEndpoint = '/v1/auth/login';
  static const String registerEndpoint = '/v1/auth/register';
  static const String refreshTokenEndpoint = '/v1/auth/refresh';
  static const String profileEndpoint = '/v1/auth/profile';
  static const String logoutEndpoint = '/v1/auth/logout';

  // 移动端API端点
  static const String documentsEndpoint = '/v1/mobile/documents';
  static const String learningSessionsEndpoint = '/v1/mobile/learning-sessions';
  static const String skillsEndpoint = '/v1/mobile/skills';
  static const String userProfileEndpoint = '/v1/mobile/profile';

  // 健康检查
  static const String healthEndpoint = '/health';
}
