import 'package:admin_web/features/analytics/services/analytics_api_service.dart';
import 'package:admin_web/shared/models/analytics.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// Events
abstract class AnalyticsEvent extends Equatable {
  const AnalyticsEvent();

  @override
  List<Object?> get props => [];
}

class LoadDashboardStats extends AnalyticsEvent {
  final int days;
  final String? organizationId;

  const LoadDashboardStats({this.days = 30, this.organizationId});

  @override
  List<Object?> get props => [days, organizationId];
}

class LoadUserActivityTrend extends AnalyticsEvent {
  final int days;
  final String? organizationId;

  const LoadUserActivityTrend({this.days = 30, this.organizationId});

  @override
  List<Object?> get props => [days, organizationId];
}

class LoadEventTypeDistribution extends AnalyticsEvent {
  final int days;
  final String? organizationId;

  const LoadEventTypeDistribution({this.days = 30, this.organizationId});

  @override
  List<Object?> get props => [days, organizationId];
}

class LoadTopActiveUsers extends AnalyticsEvent {
  final int days;
  final String? organizationId;

  const LoadTopActiveUsers({this.days = 30, this.organizationId});

  @override
  List<Object?> get props => [days, organizationId];
}

class LoadHourlyActivityPattern extends AnalyticsEvent {
  final int days;
  final String? organizationId;

  const LoadHourlyActivityPattern({this.days = 30, this.organizationId});

  @override
  List<Object?> get props => [days, organizationId];
}

class RefreshAllAnalytics extends AnalyticsEvent {
  final int days;
  final String? organizationId;

  const RefreshAllAnalytics({this.days = 30, this.organizationId});

  @override
  List<Object?> get props => [days, organizationId];
}

// States
abstract class AnalyticsState extends Equatable {
  const AnalyticsState();

  @override
  List<Object?> get props => [];
}

class AnalyticsInitial extends AnalyticsState {}

class AnalyticsLoading extends AnalyticsState {}

class AnalyticsLoaded extends AnalyticsState {
  final DashboardStats? dashboardStats;
  final List<UserActivityTrend>? userActivityTrend;
  final List<EventTypeDistribution>? eventTypeDistribution;
  final List<TopActiveUser>? topActiveUsers;
  final List<HourlyActivityPattern>? hourlyActivityPattern;
  final int currentTimeRange;

  const AnalyticsLoaded({
    this.dashboardStats,
    this.userActivityTrend,
    this.eventTypeDistribution,
    this.topActiveUsers,
    this.hourlyActivityPattern,
    this.currentTimeRange = 30,
  });

  AnalyticsLoaded copyWith({
    DashboardStats? dashboardStats,
    List<UserActivityTrend>? userActivityTrend,
    List<EventTypeDistribution>? eventTypeDistribution,
    List<TopActiveUser>? topActiveUsers,
    List<HourlyActivityPattern>? hourlyActivityPattern,
    int? currentTimeRange,
  }) {
    return AnalyticsLoaded(
      dashboardStats: dashboardStats ?? this.dashboardStats,
      userActivityTrend: userActivityTrend ?? this.userActivityTrend,
      eventTypeDistribution:
          eventTypeDistribution ?? this.eventTypeDistribution,
      topActiveUsers: topActiveUsers ?? this.topActiveUsers,
      hourlyActivityPattern:
          hourlyActivityPattern ?? this.hourlyActivityPattern,
      currentTimeRange: currentTimeRange ?? this.currentTimeRange,
    );
  }

  @override
  List<Object?> get props => [
    dashboardStats,
    userActivityTrend,
    eventTypeDistribution,
    topActiveUsers,
    hourlyActivityPattern,
    currentTimeRange,
  ];
}

class AnalyticsError extends AnalyticsState {
  final String message;

  const AnalyticsError(this.message);

  @override
  List<Object?> get props => [message];
}

// Bloc
class AnalyticsBloc extends Bloc<AnalyticsEvent, AnalyticsState> {
  final AnalyticsApiService _analyticsApiService;

  AnalyticsBloc(this._analyticsApiService) : super(AnalyticsInitial()) {
    on<LoadDashboardStats>(_onLoadDashboardStats);
    on<LoadUserActivityTrend>(_onLoadUserActivityTrend);
    on<LoadEventTypeDistribution>(_onLoadEventTypeDistribution);
    on<LoadTopActiveUsers>(_onLoadTopActiveUsers);
    on<LoadHourlyActivityPattern>(_onLoadHourlyActivityPattern);
    on<RefreshAllAnalytics>(_onRefreshAllAnalytics);
  }

  Future<void> _onLoadDashboardStats(
    LoadDashboardStats event,
    Emitter<AnalyticsState> emit,
  ) async {
    try {
      final dashboardStats = await _analyticsApiService.getDashboardStats(
        days: event.days,
        organizationId: event.organizationId,
      );

      if (state is AnalyticsLoaded) {
        final currentState = state as AnalyticsLoaded;
        emit(
          currentState.copyWith(
            dashboardStats: dashboardStats,
            currentTimeRange: event.days,
          ),
        );
      } else {
        emit(
          AnalyticsLoaded(
            dashboardStats: dashboardStats,
            currentTimeRange: event.days,
          ),
        );
      }
    } catch (e) {
      emit(AnalyticsError('加载仪表盘数据失败: ${e.toString()}'));
    }
  }

  Future<void> _onLoadUserActivityTrend(
    LoadUserActivityTrend event,
    Emitter<AnalyticsState> emit,
  ) async {
    try {
      final userActivityTrend = await _analyticsApiService.getUserActivityTrend(
        days: event.days,
        organizationId: event.organizationId,
      );

      if (state is AnalyticsLoaded) {
        final currentState = state as AnalyticsLoaded;
        emit(
          currentState.copyWith(
            userActivityTrend: userActivityTrend,
            currentTimeRange: event.days,
          ),
        );
      } else {
        emit(
          AnalyticsLoaded(
            userActivityTrend: userActivityTrend,
            currentTimeRange: event.days,
          ),
        );
      }
    } catch (e) {
      emit(AnalyticsError('加载用户活动趋势失败: ${e.toString()}'));
    }
  }

  Future<void> _onLoadEventTypeDistribution(
    LoadEventTypeDistribution event,
    Emitter<AnalyticsState> emit,
  ) async {
    try {
      final eventTypeDistribution = await _analyticsApiService
          .getEventTypeDistribution(
            days: event.days,
            organizationId: event.organizationId,
          );

      if (state is AnalyticsLoaded) {
        final currentState = state as AnalyticsLoaded;
        emit(
          currentState.copyWith(
            eventTypeDistribution: eventTypeDistribution,
            currentTimeRange: event.days,
          ),
        );
      } else {
        emit(
          AnalyticsLoaded(
            eventTypeDistribution: eventTypeDistribution,
            currentTimeRange: event.days,
          ),
        );
      }
    } catch (e) {
      emit(AnalyticsError('加载事件分布数据失败: ${e.toString()}'));
    }
  }

  Future<void> _onLoadTopActiveUsers(
    LoadTopActiveUsers event,
    Emitter<AnalyticsState> emit,
  ) async {
    try {
      final topActiveUsers = await _analyticsApiService.getTopActiveUsers(
        days: event.days,
        organizationId: event.organizationId,
      );

      if (state is AnalyticsLoaded) {
        final currentState = state as AnalyticsLoaded;
        emit(
          currentState.copyWith(
            topActiveUsers: topActiveUsers,
            currentTimeRange: event.days,
          ),
        );
      } else {
        emit(
          AnalyticsLoaded(
            topActiveUsers: topActiveUsers,
            currentTimeRange: event.days,
          ),
        );
      }
    } catch (e) {
      emit(AnalyticsError('加载活跃用户数据失败: ${e.toString()}'));
    }
  }

  Future<void> _onLoadHourlyActivityPattern(
    LoadHourlyActivityPattern event,
    Emitter<AnalyticsState> emit,
  ) async {
    try {
      final hourlyActivityPattern = await _analyticsApiService
          .getHourlyActivityPattern(
            days: event.days,
            organizationId: event.organizationId,
          );

      if (state is AnalyticsLoaded) {
        final currentState = state as AnalyticsLoaded;
        emit(
          currentState.copyWith(
            hourlyActivityPattern: hourlyActivityPattern,
            currentTimeRange: event.days,
          ),
        );
      } else {
        emit(
          AnalyticsLoaded(
            hourlyActivityPattern: hourlyActivityPattern,
            currentTimeRange: event.days,
          ),
        );
      }
    } catch (e) {
      emit(AnalyticsError('加载时间模式数据失败: ${e.toString()}'));
    }
  }

  Future<void> _onRefreshAllAnalytics(
    RefreshAllAnalytics event,
    Emitter<AnalyticsState> emit,
  ) async {
    emit(AnalyticsLoading());

    try {
      final [
        dashboardStats,
        userActivityTrend,
        eventTypeDistribution,
        topActiveUsers,
        hourlyActivityPattern,
      ] = await Future.wait([
        _analyticsApiService.getDashboardStats(
          days: event.days,
          organizationId: event.organizationId,
        ),
        _analyticsApiService.getUserActivityTrend(
          days: event.days,
          organizationId: event.organizationId,
        ),
        _analyticsApiService.getEventTypeDistribution(
          days: event.days,
          organizationId: event.organizationId,
        ),
        _analyticsApiService.getTopActiveUsers(
          days: event.days,
          organizationId: event.organizationId,
        ),
        _analyticsApiService.getHourlyActivityPattern(
          days: event.days,
          organizationId: event.organizationId,
        ),
      ]);

      emit(
        AnalyticsLoaded(
          dashboardStats: dashboardStats as DashboardStats,
          userActivityTrend: userActivityTrend as List<UserActivityTrend>,
          eventTypeDistribution:
              eventTypeDistribution as List<EventTypeDistribution>,
          topActiveUsers: topActiveUsers as List<TopActiveUser>,
          hourlyActivityPattern:
              hourlyActivityPattern as List<HourlyActivityPattern>,
          currentTimeRange: event.days,
        ),
      );
    } catch (e) {
      emit(AnalyticsError('刷新分析数据失败: ${e.toString()}'));
    }
  }
}
