import 'package:admin_web/shared/models/analytics.dart';
import 'package:dio/dio.dart';

class AnalyticsApiService {
  final Dio _dio;

  AnalyticsApiService(this._dio);

  Future<DashboardStats> getDashboardStats({
    int days = 30,
    String? organizationId,
  }) async {
    final response = await _dio.get<Map<String, dynamic>>(
      '/analytics/dashboard',
      queryParameters: {
        'days': days,
        if (organizationId != null) 'organizationId': organizationId,
      },
    );

    return DashboardStats.fromJson(response.data ?? {});
  }

  Future<List<UserActivityTrend>> getUserActivityTrend({
    int days = 30,
    String? organizationId,
  }) async {
    final response = await _dio.get<List<dynamic>>(
      '/analytics/user-activity-trend',
      queryParameters: {
        'days': days,
        if (organizationId != null) 'organizationId': organizationId,
      },
    );

    return (response.data ?? [])
        .map((item) => UserActivityTrend.fromJson(item as Map<String, dynamic>))
        .toList();
  }

  Future<List<EventTypeDistribution>> getEventTypeDistribution({
    int days = 30,
    String? organizationId,
  }) async {
    final response = await _dio.get<List<dynamic>>(
      '/analytics/event-distribution',
      queryParameters: {
        'days': days,
        if (organizationId != null) 'organizationId': organizationId,
      },
    );

    return (response.data ?? [])
        .map((item) => EventTypeDistribution.fromJson(item as Map<String, dynamic>))
        .toList();
  }

  Future<List<TopActiveUser>> getTopActiveUsers({
    int days = 30,
    String? organizationId,
  }) async {
    final response = await _dio.get<List<dynamic>>(
      '/analytics/top-active-users',
      queryParameters: {
        'days': days,
        if (organizationId != null) 'organizationId': organizationId,
      },
    );

    return (response.data ?? [])
        .map((item) => TopActiveUser.fromJson(item as Map<String, dynamic>))
        .toList();
  }

  Future<List<HourlyActivityPattern>> getHourlyActivityPattern({
    int days = 30,
    String? organizationId,
  }) async {
    final response = await _dio.get<List<dynamic>>(
      '/analytics/hourly-pattern',
      queryParameters: {
        'days': days,
        if (organizationId != null) 'organizationId': organizationId,
      },
    );

    return (response.data ?? [])
        .map((item) => HourlyActivityPattern.fromJson(item as Map<String, dynamic>))
        .toList();
  }

  Future<List<AnalyticsEvent>> getEvents({
    String? startDate,
    String? endDate,
    String? organizationId,
    String? userId,
    AnalyticsEventType? eventType,
  }) async {
    final response = await _dio.get<List<dynamic>>(
      '/analytics/events',
      queryParameters: {
        if (startDate != null) 'startDate': startDate,
        if (endDate != null) 'endDate': endDate,
        if (organizationId != null) 'organizationId': organizationId,
        if (userId != null) 'userId': userId,
        if (eventType != null) 'eventType': eventType.name,
      },
    );

    return (response.data ?? [])
        .map((item) => AnalyticsEvent.fromJson(item as Map<String, dynamic>))
        .toList();
  }

  Future<AnalyticsEvent> createEvent({
    required AnalyticsEventType eventType,
    String? userId,
    String? organizationId,
    String? sessionId,
    Map<String, dynamic>? metadata,
  }) async {
    final response = await _dio.post<Map<String, dynamic>>(
      '/analytics/events',
      data: {
        'eventType': eventType.name,
        if (userId != null) 'userId': userId,
        if (organizationId != null) 'organizationId': organizationId,
        if (sessionId != null) 'sessionId': sessionId,
        if (metadata != null) 'metadata': metadata,
      },
    );

    return AnalyticsEvent.fromJson(response.data ?? {});
  }
}
