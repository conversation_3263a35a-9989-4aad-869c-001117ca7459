import 'package:admin_web/shared/models/document.dart';
import 'package:admin_web/shared/models/pagination.dart';
import 'package:dio/dio.dart';

class DocumentsApiService {
  final Dio _dio;

  DocumentsApiService(this._dio);

  Future<PaginatedResponse<Document>> getDocuments({
    int page = 1,
    int pageSize = 20,
    String? search,
    DocumentType? type,
    DocumentStatus? status,
    AccessLevel? accessLevel,
    String? organizationId,
    String? createdBy,
    List<String>? tags,
    String sortBy = 'createdAt',
    String sortOrder = 'DESC',
    String? startDate,
    String? endDate,
    int? minSize,
    int? maxSize,
  }) async {
    final response = await _dio.get<Map<String, dynamic>>(
      '/documents',
      queryParameters: {
        'page': page,
        'pageSize': pageSize,
        if (search != null) 'search': search,
        if (type != null) 'type': type.name,
        if (status != null) 'status': status.name,
        if (accessLevel != null) 'accessLevel': accessLevel.name,
        if (organizationId != null) 'organizationId': organizationId,
        if (createdBy != null) 'createdBy': createdBy,
        if (tags != null) 'tags': tags,
        'sortBy': sortBy,
        'sortOrder': sortOrder,
        if (startDate != null) 'startDate': startDate,
        if (endDate != null) 'endDate': endDate,
        if (minSize != null) 'minSize': minSize,
        if (maxSize != null) 'maxSize': maxSize,
      },
    );

    return PaginatedResponse.fromJson(
      response.data ?? {},
      (json) => Document.fromJson(json as Map<String, dynamic>),
    );
  }

  Future<Document> getDocument(String id) async {
    final response = await _dio.get<Map<String, dynamic>>('/documents/$id');
    return Document.fromJson(response.data ?? {});
  }

  Future<Document> createDocument({
    required String title,
    required String content,
    DocumentType type = DocumentType.other,
    AccessLevel accessLevel = AccessLevel.private,
    String? organizationId,
    String? description,
    List<String>? tags,
    Map<String, dynamic>? metadata,
  }) async {
    final response = await _dio.post<Map<String, dynamic>>(
      '/documents',
      data: {
        'title': title,
        'content': content,
        'type': type.name,
        'accessLevel': accessLevel.name,
        if (organizationId != null) 'organizationId': organizationId,
        if (description != null) 'description': description,
        if (tags != null) 'tags': tags,
        if (metadata != null) 'metadata': metadata,
      },
    );

    return Document.fromJson(response.data ?? {});
  }

  Future<Document> updateDocument(
    String id, {
    String? title,
    String? content,
    DocumentType? type,
    DocumentStatus? status,
    AccessLevel? accessLevel,
    String? organizationId,
    String? description,
    List<String>? tags,
    Map<String, dynamic>? metadata,
  }) async {
    final data = <String, dynamic>{};

    if (title != null) {
      data['title'] = title;
    }
    if (content != null) {
      data['content'] = content;
    }
    if (type != null) {
      data['type'] = type.name;
    }
    if (status != null) {
      data['status'] = status.name;
    }
    if (accessLevel != null) {
      data['accessLevel'] = accessLevel.name;
    }
    if (organizationId != null) {
      data['organizationId'] = organizationId;
    }
    if (description != null) {
      data['description'] = description;
    }
    if (tags != null) {
      data['tags'] = tags;
    }
    if (metadata != null) {
      data['metadata'] = metadata;
    }

    final response = await _dio.patch<Map<String, dynamic>>(
      '/documents/$id',
      data: data,
    );
    return Document.fromJson(response.data ?? {});
  }

  Future<void> deleteDocument(String id) async {
    await _dio.delete<void>('/documents/$id');
  }

  Future<DocumentStats> getDocumentStats() async {
    final response = await _dio.get<Map<String, dynamic>>('/documents/stats');
    return DocumentStats.fromJson(response.data ?? {});
  }

  Future<List<Document>> getRecentDocuments({int limit = 10}) async {
    final response = await _dio.get<Map<String, dynamic>>(
      '/documents/recent',
      queryParameters: {'limit': limit},
    );

    final data = response.data ?? {};
    final documentsList = data['data'] as List? ?? [];
    return documentsList
        .map((item) => Document.fromJson(item as Map<String, dynamic>))
        .toList();
  }

  Future<List<Document>> searchDocuments({
    required String query,
    DocumentType? type,
    String? organizationId,
    int page = 1,
    int pageSize = 20,
  }) async {
    final response = await _dio.get<Map<String, dynamic>>(
      '/documents/search',
      queryParameters: {
        'query': query,
        if (type != null) 'type': type.name,
        if (organizationId != null) 'organizationId': organizationId,
        'page': page,
        'pageSize': pageSize,
      },
    );

    final data = response.data ?? {};
    final documentsList = data['data'] as List? ?? [];
    return documentsList
        .map((item) => Document.fromJson(item as Map<String, dynamic>))
        .toList();
  }

  Future<List<Document>> getDocumentsByTag({
    required String tag,
    String? organizationId,
    int page = 1,
    int pageSize = 20,
  }) async {
    final response = await _dio.get<Map<String, dynamic>>(
      '/documents/by-tag',
      queryParameters: {
        'tag': tag,
        if (organizationId != null) 'organizationId': organizationId,
        'page': page,
        'pageSize': pageSize,
      },
    );

    final data = response.data ?? {};
    final documentsList = data['data'] as List? ?? [];
    return documentsList
        .map((item) => Document.fromJson(item as Map<String, dynamic>))
        .toList();
  }

  Future<Map<String, dynamic>> uploadFile({
    required String filePath,
    required String fileName,
    String? organizationId,
    String? description,
    List<String>? tags,
  }) async {
    final formData = FormData.fromMap({
      'file': await MultipartFile.fromFile(filePath, filename: fileName),
      if (organizationId != null) 'organizationId': organizationId,
      if (description != null) 'description': description,
      if (tags != null) 'tags': tags,
    });

    final response = await _dio.post<Map<String, dynamic>>(
      '/documents/upload',
      data: formData,
    );

    return response.data ?? {};
  }
}
