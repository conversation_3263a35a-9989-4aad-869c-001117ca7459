import 'package:admin_web/shared/models/pagination.dart';
import 'package:admin_web/shared/models/user.dart';
import 'package:dio/dio.dart';

class UsersApiService {
  final Dio _dio;

  UsersApiService(this._dio);

  Future<PaginatedResponse<User>> getUsers({
    int page = 1,
    int pageSize = 20,
    String? search,
    UserRole? role,
    UserStatus? status,
    String sortBy = 'createdAt',
    String sortOrder = 'DESC',
  }) async {
    final response = await _dio.get<Map<String, dynamic>>(
      '/users',
      queryParameters: {
        'page': page,
        'pageSize': pageSize,
        if (search != null) 'search': search,
        if (role != null) 'role': role.name,
        if (status != null) 'status': status.name,
        'sortBy': sortBy,
        'sortOrder': sortOrder,
      },
    );

    return PaginatedResponse.fromJson(
      response.data ?? {},
      (json) => User.fromJson(json as Map<String, dynamic>),
    );
  }

  Future<User> getUser(String id) async {
    final response = await _dio.get<Map<String, dynamic>>('/users/$id');
    return User.fromJson(response.data ?? {});
  }

  Future<User> createUser({
    required String email,
    required String username,
    required String password,
    String? firstName,
    String? lastName,
    UserRole? role,
    UserStatus? status,
    String? organizationId,
    String? phone,
    String? bio,
  }) async {
    final response = await _dio.post<Map<String, dynamic>>(
      '/users',
      data: {
        'email': email,
        'username': username,
        'password': password,
        if (firstName != null) 'firstName': firstName,
        if (lastName != null) 'lastName': lastName,
        if (role != null) 'role': role.name,
        if (status != null) 'status': status.name,
        if (organizationId != null) 'organizationId': organizationId,
        if (phone != null) 'phone': phone,
        if (bio != null) 'bio': bio,
      },
    );

    return User.fromJson(response.data ?? {});
  }

  Future<User> updateUser(
    String id, {
    String? email,
    String? username,
    String? firstName,
    String? lastName,
    UserRole? role,
    UserStatus? status,
    String? organizationId,
    String? phone,
    String? bio,
  }) async {
    final data = <String, dynamic>{};

    if (email != null) {
      data['email'] = email;
    }
    if (username != null) {
      data['username'] = username;
    }
    if (firstName != null) {
      data['firstName'] = firstName;
    }
    if (lastName != null) {
      data['lastName'] = lastName;
    }
    if (role != null) {
      data['role'] = role.name;
    }
    if (status != null) {
      data['status'] = status.name;
    }
    if (organizationId != null) {
      data['organizationId'] = organizationId;
    }
    if (phone != null) {
      data['phone'] = phone;
    }
    if (bio != null) {
      data['bio'] = bio;
    }

    final response = await _dio.patch<Map<String, dynamic>>(
      '/users/$id',
      data: data,
    );
    return User.fromJson(response.data ?? {});
  }

  Future<void> deleteUser(String id) async {
    await _dio.delete<void>('/users/$id');
  }

  Future<void> updatePassword(String id, String password) async {
    await _dio.patch<void>('/users/$id/password', data: {'password': password});
  }

  Future<Map<String, dynamic>> getUserStats() async {
    final response = await _dio.get<Map<String, dynamic>>('/users/stats');
    return response.data ?? {};
  }
}
