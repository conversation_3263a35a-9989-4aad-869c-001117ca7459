import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

class DashboardPage extends StatelessWidget {
  const DashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('仪表盘')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 统计卡片
            GridView.count(
              shrinkWrap: true,
              crossAxisCount: 4,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 1.5,
              children: [
                _buildStatCard(
                  context,
                  title: '总用户数',
                  value: '1,234',
                  icon: Icons.people,
                  color: Colors.blue,
                ),
                _buildStatCard(
                  context,
                  title: '活跃用户',
                  value: '856',
                  icon: Icons.trending_up,
                  color: Colors.green,
                ),
                _buildStatCard(
                  context,
                  title: '文档总数',
                  value: '432',
                  icon: Icons.folder,
                  color: Colors.orange,
                ),
                _buildStatCard(
                  context,
                  title: '学习时长',
                  value: '2,456h',
                  icon: Icons.access_time,
                  color: Colors.purple,
                ),
              ],
            ),
            const SizedBox(height: 32),
            // 图表区域
            Expanded(
              child: Row(
                children: [
                  // 学习趋势图
                  Expanded(
                    flex: 2,
                    child: Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '学习趋势',
                              style: Theme.of(context).textTheme.titleLarge,
                            ),
                            const SizedBox(height: 16),
                            Expanded(
                              child: LineChart(
                                LineChartData(
                                  gridData: const FlGridData(show: false),
                                  titlesData: const FlTitlesData(show: false),
                                  borderData: FlBorderData(show: false),
                                  lineBarsData: [
                                    LineChartBarData(
                                      spots: _generateSampleData(),
                                      isCurved: true,
                                      color: Theme.of(context).primaryColor,
                                      barWidth: 3,
                                      isStrokeCapRound: true,
                                      dotData: const FlDotData(show: false),
                                      belowBarData: BarAreaData(
                                        show: true,
                                        color: Theme.of(
                                          context,
                                        ).primaryColor.withValues(alpha: 0.1),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  // 技能分布图
                  Expanded(
                    child: Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '技能分布',
                              style: Theme.of(context).textTheme.titleLarge,
                            ),
                            const SizedBox(height: 16),
                            Expanded(
                              child: PieChart(
                                PieChartData(
                                  sections: _generatePieData(),
                                  sectionsSpace: 2,
                                  centerSpaceRadius: 40,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context, {
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Icon(icon, color: color, size: 32),
                Text(
                  value,
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).textTheme.bodySmall?.color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<FlSpot> _generateSampleData() {
    return List.generate(
      7,
      (index) => FlSpot(index.toDouble(), (index * 10 + 20).toDouble()),
    );
  }

  List<PieChartSectionData> _generatePieData() {
    return [
      PieChartSectionData(
        color: Colors.blue,
        value: 35,
        title: '编程',
        radius: 50,
      ),
      PieChartSectionData(
        color: Colors.green,
        value: 25,
        title: '设计',
        radius: 50,
      ),
      PieChartSectionData(
        color: Colors.orange,
        value: 20,
        title: '语言',
        radius: 50,
      ),
      PieChartSectionData(
        color: Colors.purple,
        value: 20,
        title: '其他',
        radius: 50,
      ),
    ];
  }
}
