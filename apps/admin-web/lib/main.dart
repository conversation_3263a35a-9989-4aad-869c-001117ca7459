import 'package:admin_web/core/app/app.dart';
import 'package:admin_web/core/config/app_config.dart';
import 'package:admin_web/core/services/storage_service.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 加载环境变量
  await dotenv.load(fileName: '.env');

  // Flutter Web性能优化
  if (kIsWeb) {
    // 禁用调试工具以提高性能（生产环境）
    if (kReleaseMode) {
      debugPrint = (String? message, {int? wrapWidth}) {};
    }
  }

  // 初始化Sentry错误追踪和性能监控
  await SentryFlutter.init(
    (options) {
      options.dsn = kReleaseMode
          ? dotenv.env['SENTRY_DSN_PROD'] ??
                '' // 生产环境DSN
          : dotenv.env['SENTRY_DSN_DEV'] ?? ''; // 开发环境DSN

      // 设置采样率
      options.tracesSampleRate = kReleaseMode ? 0.3 : 1.0; // 生产环境30%，开发环境100%

      // 设置环境
      options.environment = kReleaseMode ? 'production' : 'development';

      // 启用性能监控
      options.enableAutoPerformanceTracing = true;

      // 设置发布版本
      options.release = 'masteryos-admin@1.0.0+1';

      // 附加用户上下文
      options.beforeSend = (SentryEvent event, Hint hint) async {
        // 可以在这里添加用户信息或其他上下文
        return event;
      };
    },
    appRunner: () async {
      // 初始化应用配置
      await AppConfig.init();

      // 初始化本地存储
      await StorageService.init();

      runApp(const AdminApp());
    },
  );
}
