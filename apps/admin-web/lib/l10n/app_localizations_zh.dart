// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appTitle => 'MasteryOS 管理后台';

  @override
  String get welcome => '欢迎使用 MasteryOS';

  @override
  String get login => '登录';

  @override
  String get logout => '退出';

  @override
  String get email => '邮箱';

  @override
  String get password => '密码';

  @override
  String get dashboard => '仪表板';

  @override
  String get users => '用户';

  @override
  String get documents => '文档';

  @override
  String get analytics => '分析';

  @override
  String get settings => '设置';

  @override
  String get profile => '个人资料';

  @override
  String get loading => '加载中...';

  @override
  String get error => '发生错误';

  @override
  String get success => '成功';

  @override
  String get cancel => '取消';

  @override
  String get save => '保存';

  @override
  String get delete => '删除';

  @override
  String get edit => '编辑';

  @override
  String get search => '搜索';
}
