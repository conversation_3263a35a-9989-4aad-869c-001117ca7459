import 'package:admin_web/core/services/storage_service.dart';
import 'package:admin_web/core/widgets/main_layout.dart';
import 'package:admin_web/features/auth/presentation/login_page.dart';
import 'package:admin_web/features/dashboard/presentation/dashboard_page.dart';
import 'package:admin_web/features/documents/presentation/documents_page.dart';
import 'package:admin_web/features/settings/presentation/settings_page.dart';
import 'package:admin_web/features/users/presentation/users_page.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class AppRouter {
  static final _rootNavigatorKey = GlobalKey<NavigatorState>();
  static final _shellNavigatorKey = GlobalKey<NavigatorState>();

  static final router = GoRouter(
    navigatorKey: _rootNavigatorKey,
    initialLocation: '/dashboard',
    redirect: (context, state) {
      final isAuthenticated = StorageService.getToken() != null;
      final isAuthRoute = state.matchedLocation == '/login';

      if (!isAuthenticated && !isAuthRoute) {
        return '/login';
      }

      if (isAuthenticated && isAuthRoute) {
        return '/dashboard';
      }

      return null;
    },
    routes: [
      GoRoute(
        path: '/login',
        pageBuilder: (context, state) =>
            MaterialPage(key: state.pageKey, child: const LoginPage()),
      ),
      ShellRoute(
        navigatorKey: _shellNavigatorKey,
        builder: (context, state, child) => MainLayout(child: child),
        routes: [
          GoRoute(
            path: '/dashboard',
            pageBuilder: (context, state) =>
                MaterialPage(key: state.pageKey, child: const DashboardPage()),
          ),
          GoRoute(
            path: '/users',
            pageBuilder: (context, state) =>
                MaterialPage(key: state.pageKey, child: const UsersPage()),
          ),
          GoRoute(
            path: '/documents',
            pageBuilder: (context, state) =>
                MaterialPage(key: state.pageKey, child: const DocumentsPage()),
          ),
          GoRoute(
            path: '/settings',
            pageBuilder: (context, state) =>
                MaterialPage(key: state.pageKey, child: const SettingsPage()),
          ),
        ],
      ),
    ],
  );
}
