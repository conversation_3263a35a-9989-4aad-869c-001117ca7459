class AppConfig {
  static const String appName = 'MasteryOS Admin';
  static const String version = '1.0.0';

  // API 配置 - 使用unified-bff服务
  static const String apiBaseUrl = String.fromEnvironment(
    'API_BASE_URL',
    defaultValue: 'http://localhost:3100', // unified-bff服务端口
  );

  // API版本和路径前缀
  static const String apiVersion = 'v1';
  static const String adminApiPrefix = '/$apiVersion/admin';
  static const String commonApiPrefix = '/$apiVersion';

  // 认证配置
  static const Duration tokenRefreshInterval = Duration(minutes: 30);
  static const String tokenKey = 'auth_token';
  static const String refreshTokenKey = 'refresh_token';

  // 分页配置
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // 初始化配置
  static Future<void> init() async {
    // 可以在这里加载环境变量或远程配置
  }
}
