import 'package:admin_web/shared/models/user.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'document.g.dart';

enum DocumentType {
  @JsonValue('pdf')
  pdf,
  @JsonValue('word')
  word,
  @JsonValue('excel')
  excel,
  @JsonValue('powerpoint')
  powerpoint,
  @JsonValue('text')
  text,
  @JsonValue('image')
  image,
  @JsonValue('video')
  video,
  @JsonValue('audio')
  audio,
  @JsonValue('other')
  other,
}

enum DocumentStatus {
  @JsonValue('draft')
  draft,
  @JsonValue('published')
  published,
  @JsonValue('archived')
  archived,
  @JsonValue('deleted')
  deleted,
}

enum AccessLevel {
  @JsonValue('public')
  public,
  @JsonValue('internal')
  internal,
  @JsonValue('private')
  private,
  @JsonValue('restricted')
  restricted,
}

@JsonSerializable()
class Document extends Equatable {
  final String id;
  final String title;
  final String? description;
  final String fileName;
  final String filePath;
  final int fileSize;
  final String? mimeType;
  final DocumentType type;
  final DocumentStatus status;
  final AccessLevel accessLevel;
  final String? organizationId;
  final String createdBy;
  final User? creator;
  final String? updatedBy;
  final User? updater;
  final List<String> tags;
  final Map<String, dynamic>? metadata;
  final int viewCount;
  final int downloadCount;
  final String? thumbnailPath;
  final bool isSearchable;
  final String? content;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Document({
    required this.id,
    required this.title,
    required this.fileName,
    required this.filePath,
    required this.fileSize,
    required this.type,
    required this.status,
    required this.accessLevel,
    required this.createdBy,
    required this.tags,
    required this.viewCount,
    required this.downloadCount,
    required this.isSearchable,
    required this.createdAt,
    required this.updatedAt,
    this.description,
    this.mimeType,
    this.organizationId,
    this.creator,
    this.updatedBy,
    this.updater,
    this.metadata,
    this.thumbnailPath,
    this.content,
  });

  factory Document.fromJson(Map<String, dynamic> json) =>
      _$DocumentFromJson(json);
  Map<String, dynamic> toJson() => _$DocumentToJson(this);

  String get fileExtension {
    return fileName.split('.').last.toLowerCase();
  }

  String get sizeFormatted {
    const units = ['B', 'KB', 'MB', 'GB'];
    double size = fileSize.toDouble();
    int unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return '${size.toStringAsFixed(1)} ${units[unitIndex]}';
  }

  String get typeDisplayName {
    switch (type) {
      case DocumentType.pdf:
        return 'PDF文档';
      case DocumentType.word:
        return 'Word文档';
      case DocumentType.excel:
        return 'Excel表格';
      case DocumentType.powerpoint:
        return 'PowerPoint演示';
      case DocumentType.text:
        return '文本文档';
      case DocumentType.image:
        return '图片';
      case DocumentType.video:
        return '视频';
      case DocumentType.audio:
        return '音频';
      case DocumentType.other:
        return '其他';
    }
  }

  String get statusDisplayName {
    switch (status) {
      case DocumentStatus.draft:
        return '草稿';
      case DocumentStatus.published:
        return '已发布';
      case DocumentStatus.archived:
        return '已归档';
      case DocumentStatus.deleted:
        return '已删除';
    }
  }

  String get accessLevelDisplayName {
    switch (accessLevel) {
      case AccessLevel.public:
        return '公开';
      case AccessLevel.internal:
        return '内部';
      case AccessLevel.private:
        return '私有';
      case AccessLevel.restricted:
        return '受限';
    }
  }

  @override
  List<Object?> get props => [
    id,
    title,
    description,
    fileName,
    filePath,
    fileSize,
    mimeType,
    type,
    status,
    accessLevel,
    organizationId,
    createdBy,
    creator,
    updatedBy,
    updater,
    tags,
    metadata,
    viewCount,
    downloadCount,
    thumbnailPath,
    isSearchable,
    content,
    createdAt,
    updatedAt,
  ];
}

@JsonSerializable()
class DocumentStats extends Equatable {
  final DocumentOverviewStats overview;
  final DocumentActivityStats activity;
  final DocumentDistributionStats distribution;

  const DocumentStats({
    required this.overview,
    required this.activity,
    required this.distribution,
  });

  factory DocumentStats.fromJson(Map<String, dynamic> json) =>
      _$DocumentStatsFromJson(json);
  Map<String, dynamic> toJson() => _$DocumentStatsToJson(this);

  @override
  List<Object?> get props => [overview, activity, distribution];
}

@JsonSerializable()
class DocumentOverviewStats extends Equatable {
  final int totalDocuments;
  final int publishedDocuments;
  final int draftDocuments;
  final int archivedDocuments;
  final int recentDocuments;

  const DocumentOverviewStats({
    required this.totalDocuments,
    required this.publishedDocuments,
    required this.draftDocuments,
    required this.archivedDocuments,
    required this.recentDocuments,
  });

  factory DocumentOverviewStats.fromJson(Map<String, dynamic> json) =>
      _$DocumentOverviewStatsFromJson(json);
  Map<String, dynamic> toJson() => _$DocumentOverviewStatsToJson(this);

  @override
  List<Object?> get props => [
    totalDocuments,
    publishedDocuments,
    draftDocuments,
    archivedDocuments,
    recentDocuments,
  ];
}

@JsonSerializable()
class DocumentActivityStats extends Equatable {
  final int totalViews;
  final int totalDownloads;

  const DocumentActivityStats({
    required this.totalViews,
    required this.totalDownloads,
  });

  factory DocumentActivityStats.fromJson(Map<String, dynamic> json) =>
      _$DocumentActivityStatsFromJson(json);
  Map<String, dynamic> toJson() => _$DocumentActivityStatsToJson(this);

  @override
  List<Object?> get props => [totalViews, totalDownloads];
}

@JsonSerializable()
class DocumentDistributionStats extends Equatable {
  final List<DocumentTypeDistribution> byType;

  const DocumentDistributionStats({required this.byType});

  factory DocumentDistributionStats.fromJson(Map<String, dynamic> json) =>
      _$DocumentDistributionStatsFromJson(json);
  Map<String, dynamic> toJson() => _$DocumentDistributionStatsToJson(this);

  @override
  List<Object?> get props => [byType];
}

@JsonSerializable()
class DocumentTypeDistribution extends Equatable {
  final DocumentType type;
  final int count;

  const DocumentTypeDistribution({required this.type, required this.count});

  factory DocumentTypeDistribution.fromJson(Map<String, dynamic> json) =>
      _$DocumentTypeDistributionFromJson(json);
  Map<String, dynamic> toJson() => _$DocumentTypeDistributionToJson(this);

  @override
  List<Object?> get props => [type, count];
}
