name: admin_web
description: MasteryOS Flutter Web Admin Dashboard
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: ^3.8.0

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  
  # 核心依赖 - 使用最新稳定版本
  dio: ^5.8.0                    # HTTP 客户端 (最新版)
  dio_web_adapter: ^2.1.1        # Web平台适配器
  go_router: ^16.1.0             # 路由管理 (最新稳定版)
  flutter_bloc: ^9.1.1           # 状态管理 (最新版)
  bloc: ^9.0.0                   # Bloc核心
  equatable: ^2.0.7              # 对象比较 (最新版)
  
  # 依赖注入 - 统一使用get_it
  get_it: ^8.0.2                 # 服务定位器
  injectable: ^2.5.0             # 依赖注入代码生成
  
  # UI 组件 - 最新版本
  data_table_2: ^2.6.0           # 数据表格 (最新版)
  fl_chart: ^1.0.0               # 图表 (最新稳定版)
  
  # 工具类 - 最新版本
  intl: ^0.20.2                  # 国际化和日期格式化 (最新版)
  shared_preferences: ^2.5.3     # 本地存储 (最新版)
  jwt_decoder: ^2.0.1            # JWT 解析
  json_annotation: ^4.9.0        # JSON 序列化注解
  
  # 额外优化包
  web: ^1.1.1                    # Web平台支持
  logging: ^1.3.0                # 日志记录
  
  # 性能监控和错误追踪
  sentry_flutter: ^8.13.0        # 错误追踪和性能监控
  
  # 环境配置
  flutter_dotenv: ^5.2.1         # 环境变量管理
  
dev_dependencies:
  flutter_test:
    sdk: flutter
  very_good_analysis: ^7.0.0    # 更严格的代码规范
  build_runner: ^2.6.0           # 最新版
  json_serializable: ^6.10.0     # 最新版
  source_gen: ^3.0.0             # 代码生成
  injectable_generator: ^2.8.1   # 依赖注入生成器
  freezed: ^3.2.0                # 不可变对象生成器
  freezed_annotation: ^3.1.0     # Freezed注解
  # envied_generator: ^0.5.4+1     # 环境变量代码生成

flutter:
  uses-material-design: true
  
  # 国际化支持
  generate: true