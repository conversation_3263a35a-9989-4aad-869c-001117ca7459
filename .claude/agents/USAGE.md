# MasteryOS Sub Agents 使用指南

## 🚀 快速开始

### 激活Agent
使用特定关键词激活对应的专业agent：

```
@flutter 请帮我优化移动端列表性能
@backend 需要添加用户认证API接口  
@devops 数据库连接出现问题
@project 制定下一阶段开发计划
@ai 实现智能文档问答功能
```

### Agent能力概览

| Agent | 关键词 | 专长领域 | 主要工具 |
|-------|--------|----------|----------|
| **Flutter Agent** | `@flutter` | 移动端+Web前端开发 | Context7, Zen分析 |
| **Backend Agent** | `@backend` | NestJS API开发 | Context7, Zen安全审计 |
| **DevOps Agent** | `@devops` | 基础设施和部署 | Context7, Zen调试 |
| **Project Agent** | `@project` | 项目管理和流程 | TodoWrite, Zen规划 |
| **AI Agent** | `@ai` | AI功能集成 | Context7, Zen深度思考 |

## 📱 Flutter Agent - 前端开发专家

### 专业领域
- **移动端应用** (`apps/mobile/`)
  - BLoC状态管理
  - 响应式UI设计
  - 性能优化
  - 跨平台兼容

- **Web管理后台** (`apps/admin-web/`)
  - Flutter Web优化
  - 数据表格和图表
  - 管理界面设计
  - 浏览器兼容性

### 典型使用场景
```
@flutter 移动端登录页面的BLoC状态管理实现
@flutter 管理后台数据表格组件性能优化  
@flutter 如何实现Flutter Web的文件上传功能
@flutter 响应式布局在不同屏幕尺寸的适配
```

### 工作流程
1. 使用Context7查找最新Flutter文档
2. 使用Zen MCP分析复杂UI问题
3. 严格遵循Flutter代码规范
4. 与Backend Agent协作全栈开发

## ⚡ Backend Agent - API开发专家

### 专业领域
- **统一BFF架构** (`apps/unified-bff/`)
  - NestJS + TypeScript
  - JWT认证和权限
  - API设计和实现
  - 性能监控

- **数据库集成**
  - PostgreSQL + pgvector
  - Redis缓存策略
  - 数据库优化
  - 事务管理

### 典型使用场景
```
@backend 设计用户技能管理的RESTful API
@backend JWT认证中间件的安全最佳实践
@backend 如何优化PostgreSQL查询性能
@backend AI文档处理API的异步队列实现
```

### 工作流程
1. 使用Context7查找NestJS最新文档
2. 使用Zen MCP进行安全审计
3. 严格TypeScript类型检查
4. 与AI Agent协作智能功能

## 🐳 DevOps Agent - 基础设施专家

### 专业领域
- **容器化环境**
  - 1w项目独立环境
  - Docker Compose编排
  - 服务网络配置
  - 资源监控

- **数据库运维**
  - PostgreSQL管理
  - Redis集群配置
  - 备份恢复策略
  - 性能调优

### 典型使用场景
```
@devops 1w项目数据库环境启动失败排查
@devops 配置生产环境的监控告警系统
@devops PostgreSQL连接池优化配置
@devops Docker容器资源使用率分析
```

### 工作流程
1. 使用Context7查找Docker/数据库文档
2. 使用Zen MCP进行系统分析
3. 监控服务健康状态
4. 与Backend Agent协作优化

## 📋 Project Agent - 项目管理专家

### 专业领域
- **项目规划**
  - 开发任务分解
  - 里程碑管理
  - 风险评估
  - 进度跟踪

- **质量管理**
  - 代码审查流程
  - 测试策略制定
  - 文档标准化
  - 发布流程

### 典型使用场景
```
@project 制定用户认证模块的开发计划
@project 建立代码审查和质量检查流程
@project 分析当前技术债务和优化建议
@project 规划下一个Sprint的任务分配
```

### 工作流程
1. 使用TodoWrite进行任务管理
2. 使用Zen MCP进行项目规划
3. 协调其他Agent协作
4. 确保质量标准执行

## 🤖 AI Agent - AI集成专家

### 专业领域
- **文档智能处理**
  - PDF解析和向量化
  - 文本分块和嵌入
  - 向量搜索优化
  - 内容摘要生成

- **智能问答系统**
  - RAG架构实现
  - 上下文管理
  - 多轮对话支持
  - 答案质量评估

### 典型使用场景
```
@ai 优化文档向量搜索的准确性和速度
@ai 实现基于上下文的智能问答系统
@ai 生成个性化学习计划的算法设计
@ai AI功能的性能监控和成本控制
```

### 工作流程
1. 使用Context7查找AI/ML最新文档
2. 使用Zen MCP深度思考算法
3. 监控AI性能和成本
4. 与Backend Agent协作API集成

## 🔄 Agent协作模式

### 全栈功能开发
```mermaid
sequenceDiagram
    participant P as Project Agent
    participant B as Backend Agent  
    participant F as Flutter Agent
    participant T as Testing
    
    P->>B: API需求分析
    B->>B: 实现API接口
    B->>F: API文档和接口
    F->>F: 前端UI集成
    F->>T: 集成测试
    T->>P: 功能验收
```

### AI功能实现
```mermaid
sequenceDiagram
    participant A as AI Agent
    participant B as Backend Agent
    participant F as Flutter Agent
    participant D as DevOps Agent
    
    A->>A: AI算法设计
    A->>B: AI服务集成
    B->>F: API接口提供
    F->>F: AI功能UI
    D->>D: 性能监控
```

### 问题解决流程
```mermaid
flowchart TD
    A[问题识别] --> B{问题分类}
    B -->|前端问题| C[@flutter]
    B -->|后端问题| D[@backend] 
    B -->|基础设施| E[@devops]
    B -->|AI功能| F[@ai]
    B -->|项目管理| G[@project]
    
    C --> H[专业分析]
    D --> H
    E --> H  
    F --> H
    G --> H
    
    H --> I[解决方案]
    I --> J[@project 验证]
```

## 🎯 最佳实践

### 1. 选择合适的Agent
```
# ✅ 正确示例
@flutter 移动端页面性能优化问题
@backend API接口设计和实现
@devops 数据库连接配置问题

# ❌ 错误示例  
@flutter 数据库设计问题 (应该用@backend)
@backend UI界面设计问题 (应该用@flutter)
```

### 2. 提供充分上下文
```
# ✅ 详细描述
@backend 需要为用户技能管理实现CRUD API，包括技能创建、编辑、删除和进度追踪功能，要求支持分页查询和权限控制

# ❌ 模糊描述
@backend 做个API
```

### 3. 利用Agent协作
```
# 复杂任务可以请求多Agent协作
@project 请协调@backend和@flutter完成用户认证功能的全栈开发

# 或者分步骤处理
@backend 先实现用户认证API
# API完成后
@flutter 基于认证API实现登录界面
```

### 4. 遵循项目规范
所有Agent都遵循以下原则：
- 🇨🇳 使用中文交流
- 📚 优先Context7获取文档
- 🧠 复杂问题用Zen MCP分析
- ✅ 严格代码质量检查
- 🐳 使用1w独立环境

## 📞 技术支持

### 常见问题
1. **Agent响应不准确？**
   - 提供更详细的上下文信息
   - 明确指定需要解决的具体问题
   - 必要时请求Agent协作

2. **不知道用哪个Agent？**
   - 查看Agent能力概览表
   - 从问题领域判断专业方向
   - 可以先问@project进行任务分析

3. **需要跨领域协作？**
   - 使用@project协调其他Agent
   - 明确说明需要多Agent协作
   - 按工作流程顺序执行

### 反馈和改进
如果遇到Agent使用问题或有改进建议，请：
1. 记录具体使用场景和问题
2. 提供Agent响应的不足之处
3. 建议期望的改进方向

---

**版本**: 1.0.0  
**最后更新**: 2025年8月5日  
**适用项目**: MasteryOS (万时通)