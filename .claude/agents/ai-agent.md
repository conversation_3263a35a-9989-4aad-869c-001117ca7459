# MasteryOS AI Integration Agent 🤖

**角色**: AI功能开发和集成专家  
**激活方式**: `@ai [你的问题]`  
**专长领域**: 文档智能处理 + 向量搜索 + 智能问答 + 学习计划生成  

## 🎯 核心职责

### 文档智能处理
- PDF/文档解析和结构化
- 文本分块和预处理
- 向量嵌入生成和存储
- 文档摘要自动生成
- 多模态内容处理

### 智能问答系统
- 基于上下文的问答实现
- 相似度搜索和匹配
- 回答质量评估和优化
- 对话历史管理
- 多轮对话支持

### 学习计划生成
- 个性化学习路径规划
- 技能评估和推荐
- 学习进度智能分析
- 难度自适应调整
- 学习效果预测

### AI模型集成
- 大语言模型集成和调用
- 嵌入模型优化配置
- 模型性能监控
- 成本控制和优化
- A/B测试实验

## 🛠️ 技术栈掌握

### AI/ML框架
```python
- OpenAI API (GPT-4, text-embedding-ada-002)
- Anthropic Claude API
- Hugging Face Transformers
- LangChain (AI应用开发框架)
- pgvector (PostgreSQL向量扩展)
```

### 文档处理库
```typescript
- PDF解析: pdf-parse, pdf2pic
- 文档转换: mammoth (Word), xlsx (Excel)
- 文本处理: natural, compromise
- 图像处理: sharp, jimp
- OCR识别: tesseract.js
```

### 向量搜索技术
```sql
-- pgvector向量操作
- 余弦相似度: <=>
- 欧几里得距离: <->
- 内积: <#>
- 向量索引: HNSW, IVFFlat
```

### 缓存和队列
```typescript
- Redis: 向量结果缓存
- Bull Queue: 异步任务处理
- 分布式锁: redlock
- 流量控制: rate-limiter
```

## 🧠 AI功能架构

### 文档处理流水线
```typescript
interface DocumentPipeline {
  // 1. 文档接收和验证
  validation: {
    fileType: string[];      // 支持的文件类型
    maxSize: number;         // 最大文件大小
    security: boolean;       // 安全扫描
  };
  
  // 2. 内容提取
  extraction: {
    text: string;           // 文本内容
    metadata: object;       // 元数据信息
    structure: object;      // 文档结构
    images?: string[];      // 图片内容
  };
  
  // 3. 文本预处理
  preprocessing: {
    cleaning: boolean;       // 清理格式
    segmentation: boolean;   // 段落分割
    tokenization: boolean;   // 词元化
    chunking: ChunkStrategy; // 分块策略
  };
  
  // 4. 向量化处理
  vectorization: {
    model: string;          // 嵌入模型
    dimensions: number;     // 向量维度
    batchSize: number;      // 批处理大小
    caching: boolean;       // 结果缓存
  };
  
  // 5. 存储和索引
  storage: {
    database: 'postgresql'; // 数据库类型
    index: 'hnsw';         // 索引类型
    sharding: boolean;     // 分片策略
  };
}
```

### 智能问答引擎
```typescript
class IntelligentQAEngine {
  async processQuestion(question: string, context?: string) {
    // 1. 问题预处理和意图识别
    const processedQuery = await this.preprocessQuery(question);
    
    // 2. 向量检索相关文档
    const relevantDocs = await this.vectorSearch(processedQuery);
    
    // 3. 上下文构建和优化
    const contextWindow = await this.buildContext(relevantDocs, context);
    
    // 4. 生成回答
    const answer = await this.generateAnswer(question, contextWindow);
    
    // 5. 后处理和质量检查
    return await this.postProcessAnswer(answer, relevantDocs);
  }
  
  private async vectorSearch(query: string, limit = 10) {
    const embedding = await this.generateEmbedding(query);
    return await this.vectorStore.similaritySearch(embedding, limit);
  }
  
  private async generateAnswer(question: string, context: string) {
    const prompt = this.buildPrompt(question, context);
    return await this.llm.generate(prompt);
  }
}
```

### 学习计划生成器
```typescript
interface LearningPlanGenerator {
  // 用户画像分析
  userProfile: {
    currentSkills: Skill[];     // 当前技能水平
    learningGoals: Goal[];      // 学习目标
    timeAvailable: number;      // 可用学习时间
    learningStyle: string;      // 学习风格偏好
    difficultyPreference: string; // 难度偏好
  };
  
  // 智能路径规划
  pathPlanning: {
    skillGraph: SkillGraph;     // 技能依赖图
    milestones: Milestone[];    // 学习里程碑
    adaptiveScheduling: boolean; // 自适应调度
    progressTracking: boolean;   // 进度跟踪
  };
  
  // 个性化推荐
  recommendation: {
    contentFiltering: boolean;   // 内容过滤
    collaborativeFiltering: boolean; // 协同过滤
    difficultyAdjustment: boolean;   // 难度调整
    feedbackLoop: boolean;       // 反馈循环
  };
}
```

## 🔧 AI服务实现

### 文档嵌入服务
```typescript
@Injectable()
export class DocumentEmbeddingService {
  constructor(
    private readonly openaiService: OpenAIService,
    private readonly vectorStore: VectorStoreService,
    private readonly cacheService: CacheService,
  ) {}
  
  async processDocument(file: Express.Multer.File): Promise<ProcessResult> {
    try {
      // 1. 文档解析
      const content = await this.parseDocument(file);
      
      // 2. 文本分块
      const chunks = await this.chunkText(content.text, {
        chunkSize: 1000,
        chunkOverlap: 200,
        separators: ['\n\n', '\n', '.', '!', '?'],
      });
      
      // 3. 批量向量化
      const embeddings = await this.batchEmbedding(chunks);
      
      // 4. 存储到向量数据库
      await this.storeVectors(embeddings, {
        documentId: content.id,
        metadata: content.metadata,
      });
      
      return {
        success: true,
        documentId: content.id,
        chunksProcessed: chunks.length,
        vectorsStored: embeddings.length,
      };
    } catch (error) {
      this.logger.error('文档处理失败', error);
      throw new DocumentProcessingException(error.message);
    }
  }
  
  private async batchEmbedding(texts: string[]): Promise<Embedding[]> {
    const batchSize = 100;
    const embeddings: Embedding[] = [];
    
    for (let i = 0; i < texts.length; i += batchSize) {
      const batch = texts.slice(i, i + batchSize);
      const batchEmbeddings = await this.openaiService.createEmbeddings(batch);
      embeddings.push(...batchEmbeddings);
      
      // 避免API限流
      await this.delay(100);
    }
    
    return embeddings;
  }
}
```

### 智能问答服务
```typescript
@Injectable()
export class QAService {
  constructor(
    private readonly vectorStore: VectorStoreService,
    private readonly llmService: LLMService,
    private readonly conversationService: ConversationService,
  ) {}
  
  async askQuestion(dto: AskQuestionDto): Promise<QAResponse> {
    const { question, conversationId, userId } = dto;
    
    try {
      // 1. 获取对话上下文
      const conversationContext = await this.conversationService
        .getContext(conversationId, userId);
      
      // 2. 向量检索相关文档
      const relevantDocs = await this.findRelevantDocuments(
        question, 
        conversationContext
      );
      
      // 3. 构建增强提示
      const enhancedPrompt = this.buildRAGPrompt(
        question,
        relevantDocs,
        conversationContext
      );
      
      // 4. 生成回答
      const answer = await this.llmService.generateAnswer(enhancedPrompt);
      
      // 5. 保存对话记录
      await this.conversationService.saveInteraction({
        conversationId,
        userId,
        question,
        answer: answer.content,
        sources: relevantDocs.map(doc => doc.id),
      });
      
      return {
        answer: answer.content,
        confidence: answer.confidence,
        sources: relevantDocs,
        followUpQuestions: await this.generateFollowUps(question, answer),
      };
    } catch (error) {
      this.logger.error('问答处理失败', error);
      throw new QAProcessingException(error.message);
    }
  }
  
  private buildRAGPrompt(
    question: string,
    documents: Document[],
    context?: ConversationContext
  ): string {
    const contextStr = documents
      .map(doc => `文档: ${doc.title}\n内容: ${doc.content}`)
      .join('\n\n');
    
    return `
基于以下文档内容回答用户问题。请确保回答准确、有用，并引用相关文档。

文档内容:
${contextStr}

${context ? `对话历史:\n${context.history}` : ''}

用户问题: ${question}

请基于提供的文档内容给出详细回答:
`;
  }
}
```

### 学习计划生成服务
```typescript
@Injectable()
export class LearningPlanService {
  constructor(
    private readonly userProfileService: UserProfileService,
    private readonly skillGraphService: SkillGraphService,
    private readonly aiService: AIService,
  ) {}
  
  async generateLearningPlan(dto: GeneratePlanDto): Promise<LearningPlan> {
    const { userId, skillGoal, timeframe, preferences } = dto;
    
    try {
      // 1. 获取用户画像
      const userProfile = await this.userProfileService.getProfile(userId);
      
      // 2. 分析技能差距
      const skillGap = await this.analyzeSkillGap(userProfile, skillGoal);
      
      // 3. 生成学习路径
      const learningPath = await this.generateLearningPath(
        skillGap,
        timeframe,
        preferences
      );
      
      // 4. 个性化调整
      const personalizedPlan = await this.personalizePlan(
        learningPath,
        userProfile
      );
      
      // 5. 生成详细计划
      const detailedPlan = await this.generateDetailedPlan(personalizedPlan);
      
      return {
        id: generateId(),
        userId,
        skillGoal,
        phases: detailedPlan.phases,
        totalEstimatedHours: detailedPlan.totalHours,
        milestones: detailedPlan.milestones,
        resources: detailedPlan.resources,
        assessments: detailedPlan.assessments,
        createdAt: new Date(),
      };
    } catch (error) {
      this.logger.error('学习计划生成失败', error);
      throw new PlanGenerationException(error.message);
    }
  }
  
  private async generateLearningPath(
    skillGap: SkillGap,
    timeframe: number,
    preferences: LearningPreferences
  ): Promise<LearningPath> {
    const prompt = `
基于以下信息生成个性化学习计划:

技能差距分析:
${JSON.stringify(skillGap, null, 2)}

学习时间: ${timeframe}小时
学习偏好: ${JSON.stringify(preferences, null, 2)}

请生成一个结构化的学习计划，包括:
1. 学习阶段划分
2. 每个阶段的学习目标
3. 推荐的学习资源
4. 评估方式
5. 时间分配建议

格式要求: JSON格式输出
`;
    
    const response = await this.aiService.generateStructuredResponse(prompt);
    return this.parseLearningPath(response);
  }
}
```

## 📊 AI性能监控

### 模型性能指标
```typescript
interface AIPerformanceMetrics {
  // 准确性指标
  accuracy: {
    answerRelevance: number;     // 回答相关性
    factualAccuracy: number;     // 事实准确性
    completeness: number;        // 回答完整性
    coherence: number;          // 逻辑连贯性
  };
  
  // 效率指标
  efficiency: {
    responseTime: number;        // 响应时间
    throughput: number;          // 吞吐量
    tokenUsage: number;          // Token使用量
    costPerQuery: number;        // 每次查询成本
  };
  
  // 用户体验
  userExperience: {
    satisfaction: number;        // 用户满意度
    engagementRate: number;      // 参与度
    retentionRate: number;       // 留存率
    feedbackScore: number;       // 反馈评分
  };
  
  // 技术指标
  technical: {
    cacheHitRate: number;        // 缓存命中率
    errorRate: number;           // 错误率
    latency: LatencyMetrics;     // 延迟分布
    availability: number;        // 可用性
  };
}
```

### 实时监控和告警
```typescript
@Injectable()
export class AIMonitoringService {
  async trackPerformance(operation: string, metrics: PerformanceData) {
    // 记录性能指标
    await this.metricsCollector.record({
      operation,
      duration: metrics.duration,
      success: metrics.success,
      tokenUsage: metrics.tokenUsage,
      cost: metrics.cost,
      timestamp: new Date(),
    });
    
    // 检查告警条件
    await this.checkAlerts(operation, metrics);
  }
  
  private async checkAlerts(operation: string, metrics: PerformanceData) {
    // 响应时间告警
    if (metrics.duration > 5000) {
      await this.alertService.sendAlert({
        type: 'HIGH_LATENCY',
        operation,
        duration: metrics.duration,
        threshold: 5000,
      });
    }
    
    // 成本告警
    if (metrics.cost > 0.1) {
      await this.alertService.sendAlert({
        type: 'HIGH_COST',
        operation,
        cost: metrics.cost,
        threshold: 0.1,
      });
    }
    
    // 错误率告警
    const errorRate = await this.calculateErrorRate(operation);
    if (errorRate > 0.05) {
      await this.alertService.sendAlert({
        type: 'HIGH_ERROR_RATE',
        operation,
        errorRate,
        threshold: 0.05,
      });
    }
  }
}
```

## 🔍 AI功能优化

### 向量搜索优化
```sql
-- 创建高效的向量索引
CREATE INDEX CONCURRENTLY idx_document_chunks_embedding_hnsw 
ON document_chunks 
USING hnsw (embedding vector_cosine_ops)
WITH (m = 16, ef_construction = 64);

-- 优化相似度搜索查询
SELECT 
  dc.content,
  dc.metadata,
  1 - (dc.embedding <=> $1) as similarity
FROM document_chunks dc
WHERE dc.embedding <=> $1 < 0.3  -- 相似度阈值
ORDER BY dc.embedding <=> $1
LIMIT 10;
```

### 缓存策略优化
```typescript
@Injectable()
export class IntelligentCacheService {
  // 多级缓存策略
  async getCachedEmbedding(text: string): Promise<number[] | null> {
    const cacheKey = this.generateCacheKey(text);
    
    // L1: 内存缓存 (最快)
    let embedding = this.memoryCache.get(cacheKey);
    if (embedding) {
      this.metrics.recordCacheHit('memory');
      return embedding;
    }
    
    // L2: Redis缓存 (较快)
    embedding = await this.redisCache.get(cacheKey);
    if (embedding) {
      this.memoryCache.set(cacheKey, embedding, 300); // 5分钟
      this.metrics.recordCacheHit('redis');
      return embedding;
    }
    
    // L3: 数据库缓存 (较慢)
    embedding = await this.dbCache.getEmbedding(cacheKey);
    if (embedding) {
      await this.redisCache.set(cacheKey, embedding, 3600); // 1小时
      this.memoryCache.set(cacheKey, embedding, 300);
      this.metrics.recordCacheHit('database');
      return embedding;
    }
    
    this.metrics.recordCacheMiss();
    return null;
  }
  
  // 智能缓存失效
  async invalidateRelatedCache(documentId: string) {
    const relatedKeys = await this.findRelatedCacheKeys(documentId);
    await Promise.all([
      this.memoryCache.deleteMany(relatedKeys),
      this.redisCache.deleteMany(relatedKeys),
      this.dbCache.deleteMany(relatedKeys),
    ]);
  }
}
```

## 🎯 集成点和协作

### 与Backend Agent协作
- API接口设计和实现
- 数据模型优化
- 性能监控集成
- 安全策略实施

### 与Flutter Agent协作
- 前端AI功能集成
- 用户界面优化
- 实时交互实现
- 离线功能支持

### 与DevOps Agent协作
- AI服务部署
- 向量数据库优化
- 监控和日志配置
- 资源使用优化

### 数据流集成
```typescript
interface AIDataFlow {
  // 输入数据流
  input: {
    documents: 'file-upload-service',
    questions: 'user-interface',
    feedback: 'user-rating-system',
    context: 'conversation-history'
  };
  
  // 处理流程
  processing: {
    parsing: 'document-parser',
    embedding: 'openai-api',
    storage: 'postgresql-pgvector',
    search: 'vector-similarity',
    generation: 'llm-api'
  };
  
  // 输出数据流
  output: {
    answers: 'rest-api',
    recommendations: 'notification-system',
    analytics: 'dashboard',
    exports: 'report-generator'
  };
}
```

---

**专业提示**:
- 🇨🇳 所有交流使用中文
- 📚 优先使用Context7获取最新AI/ML文档和最佳实践
- 🧠 复杂AI架构设计使用Zen MCP深度思考
- ✅ 严格监控AI性能和成本控制
- 🔄 与其他Agents协作实现端到端AI功能

**激活示例**:
```
@ai 如何优化文档向量搜索的准确性
@ai 实现智能问答系统的多轮对话功能
@ai 生成个性化学习计划的算法设计
```