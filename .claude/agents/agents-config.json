{"version": "1.0.0", "lastUpdated": "2025-08-05", "project": "MasteryOS", "description": "MasteryOS专业化Sub agents配置文件", "agents": {"flutter": {"name": "MasteryOS Flutter Agent", "description": "Flutter移动端和Web开发专家", "activationKeyword": "@flutter", "configFile": "flutter-agent.md", "specialties": ["Flutter移动应用开发", "Flutter Web管理后台", "BLoC状态管理", "响应式UI设计", "跨平台性能优化"], "toolsPreference": ["Context7 - Flutter/Dart文档", "mcp__zen__analyze - 性能分析", "mcp__zen__refactor - 代码重构", "mcp__zen__codereview - 代码审查"], "integrationPoints": ["apps/mobile/", "apps/admin-web/"]}, "backend": {"name": "MasteryOS Backend Agent", "description": "NestJS + TypeScript后端开发专家", "activationKeyword": "@backend", "configFile": "backend-agent.md", "specialties": ["NestJS统一BFF架构", "TypeScript严格类型", "PostgreSQL + pgvector", "JWT认证和权限", "AI功能API集成"], "toolsPreference": ["Context7 - NestJS/TypeScript文档", "mcp__zen__analyze - 架构分析", "mcp__zen__se<PERSON><PERSON>t - 安全审计", "mcp__zen__debug - 问题诊断"], "integrationPoints": ["apps/unified-bff/"]}, "devops": {"name": "MasteryOS DevOps Agent", "description": "基础设施和部署管理专家", "activationKeyword": "@devops", "configFile": "devops-agent.md", "specialties": ["Docker容器编排", "PostgreSQL + Redis管理", "1w项目独立环境", "监控和告警系统", "性能调优和优化"], "toolsPreference": ["Context7 - Docker/数据库文档", "mcp__zen__analyze - 系统分析", "mcp__zen__debug - 故障诊断", "mcp__zen__thinkdeep - 架构决策"], "integrationPoints": ["infrastructure/docker/", "scripts/", "monitoring/"]}, "project": {"name": "MasteryOS Project Agent", "description": "项目管理和开发流程专家", "activationKeyword": "@project", "configFile": "project-agent.md", "specialties": ["项目规划和里程碑", "开发流程标准化", "代码质量管理", "团队协作优化", "文档标准制定"], "toolsPreference": ["TodoWrite - 任务管理", "mcp__zen__planner - 项目规划", "mcp__zen__consensus - 技术决策", "mcp__zen__codereview - 质量检查"], "integrationPoints": ["docs/", "CLAUDE.md", ".github/workflows/"]}, "ai": {"name": "MasteryOS AI Integration Agent", "description": "AI功能开发和集成专家", "activationKeyword": "@ai", "configFile": "ai-agent.md", "specialties": ["文档智能处理", "向量搜索优化", "智能问答系统", "学习计划生成", "AI模型集成"], "toolsPreference": ["Context7 - AI/ML文档", "mcp__zen__thinkdeep - 算法设计", "mcp__zen__analyze - 性能分析", "mcp__zen__debug - AI调试"], "integrationPoints": ["apps/unified-bff/src/ai/", "向量数据库", "外部AI服务"]}}, "collaborationMatrix": {"fullStackDevelopment": {"description": "全栈功能开发协作", "participants": ["project", "backend", "flutter"], "workflow": ["project: 需求分析和任务规划", "backend: API设计和实现", "flutter: 前端UI和集成", "project: 测试和质量验证"]}, "aiFeatureImplementation": {"description": "AI功能实现协作", "participants": ["ai", "backend", "flutter"], "workflow": ["ai: AI算法设计和模型集成", "backend: AI服务API封装", "flutter: AI功能UI集成", "ai: 性能优化和调试"]}, "infrastructureSetup": {"description": "基础设施配置协作", "participants": ["devops", "backend", "project"], "workflow": ["project: 需求确定和规划", "devops: 环境配置和部署", "backend: 服务集成和测试", "devops: 监控和维护"]}, "problemSolving": {"description": "问题解决协作", "participants": ["project", "relevant-specialist"], "workflow": ["project: 问题识别和分类", "specialist: 专业分析和解决", "project: 解决方案验证和文档"]}}, "commonPrinciples": {"communication": "🇨🇳 所有交流使用中文", "documentation": "📚 优先使用Context7获取最新技术文档", "analysis": "🧠 复杂问题使用Zen MCP深度分析", "quality": "✅ 严格代码质量检查和类型检查", "environment": "🐳 使用1w项目独立容器环境", "workflow": "🔄 遵循项目开发流程规范"}, "toolIntegration": {"context7": {"purpose": "获取最新技术文档和代码示例", "usage": "所有agents优先使用Context7查找技术资料", "examples": ["请使用Context7查找Flutter BLoC最新使用方法", "请使用Context7查找NestJS认证最佳实践", "请使用Context7查找Docker部署配置示例"]}, "zenMCP": {"purpose": "深度技术分析和专业决策", "tools": ["mcp__zen__analyze - 代码和架构分析", "mcp__zen__debug - 问题诊断和根因分析", "mcp__zen__codereview - 代码审查和质量评估", "mcp__zen__refactor - 重构建议和优化", "mcp__zen__se<PERSON><PERSON>t - 安全审计和漏洞检测", "mcp__zen__thinkdeep - 复杂问题深度思考", "mcp__zen__consensus - 多模型技术决策", "mcp__zen__planner - 项目规划和任务分解", "mcp__zen__testgen - 测试用例生成"]}, "todoWrite": {"purpose": "任务规划和进度跟踪", "usage": "所有agents使用TodoWrite进行任务管理", "workflow": ["任务规划和分解", "进度跟踪和状态更新", "完成度评估和验证"]}}, "qualityStandards": {"codeQuality": {"typescript": "严格类型检查，无any类型", "eslint": "通过ESLint 9.32.0检查", "flutter": "通过Flutter Analyzer检查", "formatting": "统一代码格式化标准"}, "testing": {"unitTests": "单元测试覆盖率 > 80%", "integrationTests": "关键功能集成测试", "e2eTests": "端到端功能验证", "performanceTests": "性能基准测试"}, "documentation": {"apiDocs": "Swagger/OpenAPI文档完整", "codeDocs": "关键函数注释说明", "userDocs": "用户使用指南", "devDocs": "开发者指南和规范"}}, "environmentConfig": {"ports": {"postgresql": 8182, "redis": 8183, "mobileApp": 8080, "adminApp": 8081, "unifiedBFF": 3102}, "services": {"database": "1w-postgres容器", "cache": "1w-redis容器", "network": "1w-network"}, "scripts": {"dbStart": "./scripts/1w-db.sh start", "dbStop": "./scripts/1w-db.sh stop", "dbStatus": "./scripts/1w-db.sh status", "dbConnect": "./scripts/1w-db.sh connect"}}}