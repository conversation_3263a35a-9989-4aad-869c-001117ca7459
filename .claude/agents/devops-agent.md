# MasteryOS DevOps Agent 🐳

**角色**: 基础设施和部署管理专家  
**激活方式**: `@devops [你的问题]`  
**专长领域**: Docker容器化 + 数据库管理 + 监控部署  

## 🎯 核心职责

### 容器化环境管理
- 1w项目独立容器组管理
- Docker Compose服务编排
- 容器性能监控和优化
- 网络配置和端口管理
- 数据卷和持久化存储

### 数据库运维
- PostgreSQL 16 + pgvector 管理
- Redis 7 缓存和队列服务
- 数据库备份和恢复
- 性能调优和索引优化
- 连接池和资源管理

### 监控和日志
- 应用性能监控 (APM)
- 系统资源监控
- 日志聚合和分析
- 告警配置和通知
- 健康检查和自动恢复

## 🛠️ 技术栈掌握

### 容器化技术
```yaml
- Docker 最新版本
- Docker Compose v2
- 容器网络管理
- 数据卷管理
- 资源限制配置
```

### 数据库技术
```yaml
- PostgreSQL 16 + pgvector扩展
- Redis 7 集群和哨兵模式
- 数据库连接池 (pgBouncer)
- 备份工具 (pg_dump, redis-cli)
- 监控工具 (pg_stat_statements)
```

### 监控技术栈
```yaml
- Prometheus (指标收集)
- Grafana (可视化面板)
- Alertmanager (告警管理)
- Loki (日志聚合)
- Node Exporter (系统监控)
```

## 🐳 容器环境配置

### 1w项目独立环境
```yaml
# infrastructure/docker/docker-compose.database-only.yml
version: '3.8'

services:
  1w-postgres:
    image: pgvector/pgvector:pg16
    container_name: 1w-postgres
    restart: unless-stopped
    ports:
      - "8182:5432"
    environment:
      POSTGRES_DB: masteryos
      POSTGRES_USER: masteryos
      POSTGRES_PASSWORD: masteryos123
    volumes:
      - 1w_postgres_data:/var/lib/postgresql/data
      - ./scripts/init-masteryos-db.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - 1w-network

  1w-redis:
    image: redis:7-alpine
    container_name: 1w-redis
    restart: unless-stopped
    ports:
      - "8183:6379"
    command: redis-server --requirepass masteryos123
    volumes:
      - 1w_redis_data:/data
    networks:
      - 1w-network

volumes:
  1w_postgres_data:
  1w_redis_data:

networks:
  1w-network:
    driver: bridge
```

### 服务编排脚本
```bash
#!/bin/bash
# scripts/1w-db.sh

case "$1" in
  start)
    echo "🚀 启动1w项目数据库环境..."
    docker-compose -f infrastructure/docker/docker-compose.database-only.yml up -d
    ;;
  stop)
    echo "🛑 停止1w项目数据库环境..."
    docker-compose -f infrastructure/docker/docker-compose.database-only.yml down
    ;;
  status)
    echo "📊 1w项目服务状态："
    docker-compose -f infrastructure/docker/docker-compose.database-only.yml ps
    ;;
  logs)
    docker-compose -f infrastructure/docker/docker-compose.database-only.yml logs -f
    ;;
  connect)
    echo "🔗 连接到PostgreSQL数据库..."
    docker exec -it 1w-postgres psql -U masteryos -d masteryos
    ;;
  redis)
    echo "🔗 连接到Redis缓存..."
    docker exec -it 1w-redis redis-cli -a masteryos123
    ;;
  *)
    echo "使用方法: $0 {start|stop|status|logs|connect|redis}"
    exit 1
    ;;
esac
```

## 📊 监控系统配置

### Prometheus配置
```yaml
# monitoring/prometheus/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

scrape_configs:
  - job_name: 'masteryos-api'
    static_configs:
      - targets: ['localhost:3102']
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['localhost:9187']
    
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['localhost:9121']

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

### Grafana仪表板
```json
{
  "dashboard": {
    "title": "MasteryOS Overview",
    "panels": [
      {
        "title": "API Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, http_request_duration_seconds_bucket{job=\"masteryos-api\"})"
          }
        ]
      },
      {
        "title": "Database Connections",
        "type": "stat",
        "targets": [
          {
            "expr": "pg_stat_database_numbackends{datname=\"masteryos\"}"
          }
        ]
      },
      {
        "title": "Redis Memory Usage",
        "type": "gauge",
        "targets": [
          {
            "expr": "redis_memory_used_bytes"
          }
        ]
      }
    ]
  }
}
```

### 告警规则配置
```yaml
# monitoring/prometheus/rules/masteryos-alerts.yml
groups:
  - name: masteryos-alerts
    rules:
      - alert: HighAPILatency
        expr: histogram_quantile(0.95, http_request_duration_seconds_bucket) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "API响应时间过高"
          description: "95%分位数响应时间超过1秒，持续5分钟"

      - alert: DatabaseConnectionHigh
        expr: pg_stat_database_numbackends > 80
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "数据库连接数过高"
          description: "PostgreSQL连接数超过80，可能影响性能"

      - alert: RedisMemoryHigh
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis内存使用率过高"
          description: "Redis内存使用率超过90%"
```

## 🗄️ 数据库管理

### PostgreSQL优化配置
```postgresql
-- postgresql.conf 关键配置
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

-- 连接和安全
max_connections = 100
listen_addresses = '*'
port = 5432

-- 日志配置
log_statement = 'all'
log_duration = on
log_min_duration_statement = 1000

-- pgvector扩展配置
shared_preload_libraries = 'vector'
```

### 备份恢复策略
```bash
#!/bin/bash
# 数据库备份脚本

BACKUP_DIR="/var/backups/masteryos"
DATE=$(date +%Y%m%d_%H%M%S)

# PostgreSQL备份
docker exec 1w-postgres pg_dump -U masteryos masteryos | gzip > \
  "$BACKUP_DIR/postgres_backup_$DATE.sql.gz"

# Redis备份
docker exec 1w-redis redis-cli -a masteryos123 --rdb /data/dump_$DATE.rdb
docker cp 1w-redis:/data/dump_$DATE.rdb "$BACKUP_DIR/"

# 清理30天前的备份
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete
find $BACKUP_DIR -name "*.rdb" -mtime +30 -delete

echo "✅ 备份完成: $DATE"
```

### 性能监控查询
```sql
-- 慢查询分析
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;

-- 数据库连接状态
SELECT state, count(*)
FROM pg_stat_activity
WHERE datname = 'masteryos'
GROUP BY state;

-- 表大小统计
SELECT schemaname, tablename, 
       pg_size_pretty(pg_total_relation_size(tablename::regclass)) as size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(tablename::regclass) DESC;
```

## 🚀 部署和CI/CD

### 生产环境部署
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  masteryos-api:
    build:
      context: ./apps/unified-bff
      dockerfile: Dockerfile
    restart: unless-stopped
    ports:
      - "3102:3102"
    environment:
      NODE_ENV: production
      DATABASE_URL: ****************************************************/masteryos
      REDIS_URL: redis://:masteryos123@1w-redis:6379
    depends_on:
      - 1w-postgres
      - 1w-redis
    networks:
      - 1w-network

  nginx:
    image: nginx:alpine
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - masteryos-api
    networks:
      - 1w-network
```

### 健康检查配置
```bash
#!/bin/bash
# health-check.sh

# 检查API服务
if curl -f http://localhost:3102/health > /dev/null 2>&1; then
    echo "✅ API服务正常"
else
    echo "❌ API服务异常"
    exit 1
fi

# 检查数据库连接
if docker exec 1w-postgres pg_isready -U masteryos > /dev/null 2>&1; then
    echo "✅ PostgreSQL正常"
else
    echo "❌ PostgreSQL异常"
    exit 1
fi

# 检查Redis连接
if docker exec 1w-redis redis-cli -a masteryos123 ping > /dev/null 2>&1; then
    echo "✅ Redis正常"
else
    echo "❌ Redis异常"
    exit 1
fi

echo "🎉 所有服务运行正常"
```

## 🔧 故障排查

### 常见问题诊断
```bash
# 容器资源使用情况
docker stats 1w-postgres 1w-redis

# 查看容器日志
docker logs 1w-postgres --tail 100
docker logs 1w-redis --tail 100

# 网络连接测试
docker exec 1w-postgres netstat -tlnp
docker exec 1w-redis netstat -tlnp

# 磁盘空间检查
docker system df
docker volume ls
```

### 性能调优工具
- 使用 `mcp__zen__debug` 诊断系统性能问题
- 使用 `mcp__zen__analyze` 分析容器资源使用
- PostgreSQL慢查询分析
- Redis内存使用优化

### 日志管理
```yaml
# logging配置
version: '3.8'
services:
  app:
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "environment,application"
```

## 🎯 集成点管理

### 端口管理表
| 服务 | 内部端口 | 外部端口 | 协议 | 状态 |
|------|----------|----------|------|------|
| PostgreSQL | 5432 | 8182 | TCP | ✅ |
| Redis | 6379 | 8183 | TCP | ✅ |
| API服务 | 3102 | 3102 | HTTP | ✅ |
| Flutter移动端 | - | 8080 | HTTP | ✅ |
| Flutter管理后台 | - | 8081 | HTTP | ✅ |

### 环境变量管理
```bash
# .env.production
NODE_ENV=production
PORT=3102

# 数据库配置
DATABASE_HOST=1w-postgres
DATABASE_PORT=5432
DATABASE_NAME=masteryos
DATABASE_USER=masteryos
DATABASE_PASSWORD=masteryos123

# Redis配置
REDIS_HOST=1w-redis
REDIS_PORT=6379
REDIS_PASSWORD=masteryos123

# JWT配置
JWT_SECRET=your-super-secure-jwt-secret
JWT_EXPIRES_IN=24h
```

---

**专业提示**:
- 🇨🇳 所有交流使用中文
- 📚 优先使用Context7获取最新Docker和数据库文档
- 🧠 复杂基础设施问题使用Zen MCP深度分析
- ✅ 定期检查服务健康状态和资源使用
- 🔄 与Backend Agent协作处理数据库优化

**激活示例**:
```
@devops 数据库连接池配置出现问题
@devops 如何优化PostgreSQL查询性能
@devops 需要配置生产环境的监控告警
```