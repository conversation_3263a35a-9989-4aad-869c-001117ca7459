# CLAUDE.md

**重要说明**: 
- 🇨🇳 **请务必用中文与我交流** - 项目团队为中文母语者，所有技术讨论使用中文
- 📚 **优先使用Context7** - 获取最新的技术文档和代码示例
- 🧠 **善用Zen MCP** - 寻找最佳实践和深度技术分析
- 🕐 所有生成的文档和报告都使用当前系统时间 (2025-08-06)
- 本文件为 Claude Code (claude.ai/code) 提供项目工作指南

**最后更新**: 2025年8月6日

## 项目概述

MasteryOS (万时通) 是基于"一万小时定律"的智能技能发展和追踪系统。这是一个综合学习平台，结合AI辅助、社交学习和游戏化机制，帮助用户在任何技能领域实现精通。

## 当前项目状态 🚀

### ✅ 已完成的核心功能 (2025-08-06)

**1. 完整的Monorepo架构**
- 统一的 monorepo 架构，清晰的 `apps/` 目录组织
- Flutter移动应用 + Flutter Web管理后台 + 统一BFF API
- 标准化的开发工具配置和脚本
- pnpm 工作空间管理和依赖优化

**2. 移动端完整应用 (apps/mobile)**
- Flutter 3.32.1 完整移动应用框架
- 清晰的特性驱动架构：core/ 和 features/ 分离
- 五个核心页面：首页、认证、文档、学习、个人资料
- BLoC状态管理和依赖注入 (get_it + injectable)
- 完整的HTTP客户端 (dio + retrofit)
- 响应式 UI 设计，支持 Web 开发

**3. 统一BFF API服务 (apps/unified-bff)**
- NestJS + TypeScript 5.8.3 企业级后端架构
- 完整的认证和权限系统 (JWT + 角色权限)
- 全面的AI集成模块（LangChain + 向量存储）
- 高级缓存策略 (Redis多级缓存)
- 完整的监控和性能优化系统
- Prometheus指标收集和Grafana可视化
- 结构化日志系统 (Winston)

**4. Flutter Web管理后台 (apps/admin-web)**
- Flutter Web 框架构建的完整管理系统
- BLoC状态管理和现代化UI组件
- 用户管理、文档管理、分析统计功能
- 数据表格和图表可视化 (fl_chart)
- JWT认证和权限控制

**5. 生产级开发环境**
- Docker独立项目环境 (1w项目组)
- PostgreSQL 16 + pgvector + Redis 7 数据层
- 完整的开发工具链配置和脚本
- 自动化健康检查和服务监控

**6. 企业级工具链**
- pnpm 10.14.0 工作空间管理
- ESLint 9.32.0 现代化平面配置
- TypeScript 严格类型检查 (noImplicitAny + 所有严格选项)
- Prettier 统一代码格式化
- 完整的开发脚本生态系统

### 📁 当前目录结构

```
1w/
├── apps/                           # ✅ 应用程序目录
│   ├── mobile/                     # ✅ Flutter 移动应用
│   ├── admin-web/                  # ✅ Flutter Web 管理后台
│   └── unified-bff/                # ✅ 统一 BFF API (NestJS)
├── infrastructure/docker/          # ✅ Docker 配置
├── scripts/                        # ✅ 开发脚本
├── docs/                          # ✅ 项目文档
├── CLAUDE.md                      # ✅ 项目指导文档
└── README.md                      # ✅ 项目说明
```

## ✅ 最新改进成果 (2025-08-06)

### 代码质量大幅提升
**从1345个ESLint错误降至238个问题（96错误+142警告）**
- ✅ 修复所有未使用变量问题（61个→0）
- ✅ 替换149个`any`类型为具体类型定义
- ✅ 修复4个require导入，统一使用ES6模块
- ✅ 修复API端口不匹配问题 (3100→3102)
- ✅ 清理TypeScript配置中的无效目录引用
- ⏳ 剩余问题主要是Entity初始化和复杂类型推断

### 配置文件已对齐
- ✅ **API端口统一**: 移动端已更新为3102端口
- ✅ **TypeScript配置清理**: 移除已删除的应用目录引用
- ⏳ **Flutter依赖更新**: 待处理25个包更新

### Claude Code Sub Agents配置完成
- ✅ 成功配置5个专业化子代理
- ✅ Flutter、Backend、DevOps、Project、AI代理就绪
- ✅ 可通过`/agents`命令调用专业代理协助

## 🛠️ MCP工具使用指南

### Context7 - 获取最新技术文档

Context7提供最新的技术文档和代码示例。**优先使用Context7查找技术资料**：

```bash
# Context7已配置为SSE传输，自动连接
# 可直接在对话中请求最新文档
```

**使用场景**：
- 查找最新的Flutter/Dart文档
- 获取NestJS/TypeScript最佳实践
- Flutter BLoC状态管理最佳实践
- PostgreSQL/Redis配置示例
- Docker配置参考

**使用方法**：
```
请使用Context7查找[技术栈]的最新文档
例如：请使用Context7查找Flutter BLoC状态管理的最新使用方法
```

### Zen MCP - 深度技术分析

Zen MCP提供多种专业分析工具，适合复杂技术决策：

**可用工具**：
- `mcp__zen__analyze` - 代码分析和架构评估
- `mcp__zen__debug` - 问题诊断和根因分析
- `mcp__zen__codereview` - 代码审查和质量评估
- `mcp__zen__refactor` - 重构建议和优化方案
- `mcp__zen__secaudit` - 安全审计和漏洞检测
- `mcp__zen__thinkdeep` - 复杂问题深度思考
- `mcp__zen__consensus` - 多模型技术决策
- `mcp__zen__testgen` - 测试用例生成

**使用场景**：
- 代码架构设计决策
- 性能优化和问题排查
- 安全性评估和改进
- 复杂功能的实现方案
- 技术选型和最佳实践

## 开发环境指南

### 🐳 推荐开发方式 - 1w 项目独立环境

**健康状态**: ✅ 所有服务正常运行，数据库连接稳定

当前已启动服务状态：
- PostgreSQL: ✅ 运行中 (端口 8182)
- Redis: ✅ 运行中 (端口 8183)
- 服务健康检查: ✅ 正常

```bash
# 启动 1w 项目数据库环境
./scripts/1w-db.sh start

# 查看服务状态
./scripts/1w-db.sh status

# 连接到数据库
./scripts/1w-db.sh connect

# 查看服务日志
./scripts/1w-db.sh logs

# 停止服务
./scripts/1w-db.sh stop
```

### 📱 应用开发启动

```bash
# Flutter 移动端开发
cd apps/mobile
fvm flutter run -d web-server --web-port=8080

# Flutter 管理后台开发
cd apps/admin-web
flutter run -d web-server --web-port=8081

# 统一BFF API开发  
cd apps/unified-bff
pnpm run start:dev    # 开发模式，支持热重载
# 或者
pnpm run start:debug  # 调试模式
```

### 🔧 必要的开发工具

**前置要求**:
- Node.js 22.18.0 LTS (通过 NVM 管理)
- pnpm 10.14.0 (最新版本)
- Docker & Docker Compose (项目容器化)
- Flutter SDK (通过 FVM 管理)
- Git (版本控制)

## 开发命令

### 基础项目管理
```bash
# 项目依赖管理
pnpm install                    # 安装所有依赖
pnpm run lint                   # 代码检查
pnpm run lint:fix              # 修复代码问题  
pnpm run format                 # 格式化代码
pnpm run typecheck             # TypeScript 类型检查

# 数据库管理
./scripts/1w-db.sh start        # 启动数据库服务
./scripts/1w-db.sh stop         # 停止数据库服务
./scripts/1w-db.sh status       # 查看服务状态
./scripts/1w-db.sh connect      # 连接到 PostgreSQL
./scripts/1w-db.sh redis        # 连接到 Redis
```

### Flutter 开发命令
```bash
cd apps/mobile  # 或 apps/admin-web

# 开发相关
flutter pub get                 # 获取依赖
flutter analyze                 # 代码分析
flutter build web              # 构建 Web 版本
flutter test                   # 运行测试

# 开发服务器
flutter run -d web-server --web-port=8080  # 启动 Web 开发
```

### 统一BFF开发命令
```bash
cd apps/unified-bff

# 开发相关
pnpm install                    # 安装依赖
pnpm run dev                    # 启动开发服务器
pnpm run build                  # 构建生产版本
pnpm run test                   # 运行测试
pnpm run test:e2e              # 端到端测试
```

## 架构和技术栈

### 当前技术架构
- **数据库**: PostgreSQL 16 + pgvector (主数据库) + Redis 7 (缓存/队列)
- **移动端**: Flutter 3.32.1 + 响应式设计
- **管理后台**: Flutter Web + BLoC状态管理
- **后端 API**: NestJS + TypeScript 5.9.2 + 统一BFF架构
- **容器化**: Docker + Docker Compose (1w 项目组)
- **工具链**: pnpm 工作空间 + ESLint 9.32.0 + Node.js 22.18.0 LTS

### 服务端口分配 (1w 项目独立环境)
| 服务 | 端口 | 用途 | 访问地址 | 容器名 | 状态 |
|------|------|------|----------|---------|------|
| **PostgreSQL** | 8182 | 主数据库 | localhost:8182 | 1w-postgres | ✅ |
| **Redis** | 8183 | 缓存/队列 | localhost:8183 | 1w-redis | ✅ |
| **移动端** | 8080 | Flutter移动应用 | http://localhost:8080 | - | 🟡 |
| **管理后台** | 8081 | Flutter管理后台 | http://localhost:8081 | - | 🟡 |
| **统一BFF** | 3102 | API服务 | http://localhost:3102/api | - | 🟡 |

📊 **API端点**:
- Swagger文档: http://localhost:3102/api/docs
- 健康检查: http://localhost:3102/api/health
- Prometheus指标: http://localhost:3102/metrics

### 数据库连接信息
```bash
# PostgreSQL (1w-postgres)
主机: localhost
端口: 8182
用户: masteryos
密码: masteryos123
数据库: masteryos

# Redis (1w-redis)
主机: localhost
端口: 8183
密码: masteryos123
```

## 🎯 当前开发优先级 (2025-08-06)

**阶段1: 基础功能完善 (当前)**
1. **用户认证系统集成** - 移动端和管理后台的完整登录流程
2. **核心API接口测试** - 统一BFF的关键接口验证和优化
3. **基础UI交互完善** - 页面导航、状态管理、错误处理
4. **代码质量改进** - 修复1345个lint错误，提升代码标准

**阶段2: 核心业务功能 (2-3周)**
1. **技能追踪系统** - 技能创建、进度记录、目标设定
2. **文档管理功能** - 上传、解析、存储、搜索
3. **用户个人资料** - 完整的用户信息管理
4. **基础分析统计** - 学习时间、进度统计

**阶段3: AI功能集成 (3-5周)**
1. **智能文档解析** - 基于现有AI模块的文档处理
2. **学习计划生成** - AI辅助的个性化学习路径
3. **智能问答系统** - 基于文档内容的Q&A
4. **进度洞察分析** - AI驱动的学习建议

### 🔧 技术增强
1. **状态管理** - Flutter BLoC 集成
2. **API文档** - Swagger/OpenAPI 文档
3. **测试框架** - 单元测试 + 集成测试
4. **缓存策略** - Redis 缓存优化

### 🎨 用户体验优化
1. **页面动画** - Flutter 转场动画
2. **错误处理** - 统一错误处理和用户反馈
3. **响应式设计** - 适配各种屏幕尺寸
4. **离线支持** - 本地数据缓存

## 重要注意事项和当前问题

### ⚠️ 当前待处理问题

**1. 剩余代码质量问题**
- **238个问题** (96错误 + 142警告)：
  - TypeORM Entity属性初始化警告
  - 部分复杂类型推断问题
  - 非空断言警告
  
**2. Flutter依赖更新**
- 25个包需要更新到最新版本
- 建议定期执行 `flutter pub upgrade`

**3. 测试覆盖改进**
- 需要增加单元测试覆盖率
- 缺少集成测试和端到端测试
- 建议实施TDD开发流程

### 开发原则
- 🇨🇳 **优先使用中文** - 所有交流和文档都使用中文
- 📚 **优先使用Context7** - 获取最新技术文档和示例
- 🧠 **善用Zen MCP** - 进行深度技术分析和决策
- 🕐 **时间标准** - 使用当前系统时间生成文档 (2025-08-06)
- 🎯 **渐进开发** - 从核心功能开始，逐步增加复杂性
- ✅ **代码质量优先** - 严格的类型检查和代码规范
- 🐳 **环境隔离** - 使用1w项目独立容器环境

### MCP工具使用原则
1. **技术查询优先Context7** - 获取最新文档和示例
2. **复杂分析优先Zen MCP** - 使用专业分析工具
3. **问题解决流程**：
   - 先用Context7查找相关文档
   - 再用Zen MCP进行深度分析
   - 结合两者制定解决方案

### 开发流程
1. 使用 TodoWrite 工具规划任务
2. 使用 Context7 查找最新技术文档
3. 使用 Zen MCP 进行复杂技术决策
4. 启动数据库环境: `./scripts/1w-db.sh start`
5. 优先编辑现有文件，避免创建新文件
6. 完成功能后运行 lint 和 typecheck
7. 只有明确要求时才进行 git commit

### 快速启动指南
```bash
# 1. 启动数据库环境 (✅ 已运行)
./scripts/1w-db.sh start
./scripts/1w-db.sh status  # 检查服务状态

# 2. 安装项目依赖 (✅ 已完成)
pnpm install

# 3. 检查代码质量 (✅ 大部分已修复)
pnpm run lint      # 查看剩余238个问题
pnpm run lint:fix  # 自动修复格式化问题

# 4. 启动应用开发 (选择一个)
# 移动端：
cd apps/mobile && flutter run -d web-server --web-port=8080
# 管理后台：  
cd apps/admin-web && flutter run -d web-server --web-port=8081
# API服务：
cd apps/unified-bff && pnpm run start:dev
```

### 🔧 开发工具状态检查
```bash
# Flutter开发环境状态
✅ Flutter 3.32.1 (stable)
✅ Xcode 16.4 (macOS/iOS支持)
✅ Chrome (Web开发支持)
✅ VS Code 1.102.3
⚠️ Android许可证需要接受: flutter doctor --android-licenses

# Node.js环境状态  
✅ Node.js 22.18.0 LTS
✅ pnpm 10.14.0
✅ TypeScript 5.8.3
```

---

**项目状态**: 🎯 基础架构完成，开始核心功能开发  
**技术栈状态**: ✅ Flutter + NestJS + PostgreSQL + Redis 全栈架构完整  
**MCP工具状态**: ✅ Context7 (最新文档) + Zen MCP (深度分析) 已配置  
**最后更新**: 2025年8月6日  
**下次里程碑**: 完善核心业务功能和用户体验