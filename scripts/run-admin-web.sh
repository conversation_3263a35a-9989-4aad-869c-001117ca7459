#!/bin/bash

# Flutter Admin Web 优化启动脚本
# 解决registerExtension和性能警告

echo "🚀 启动 MasteryOS Admin Web..."
echo "📝 使用优化的渲染器和配置..."

cd apps/admin-web

# 清理缓存（如果需要）
if [ "$1" == "--clean" ]; then
    echo "🧹 清理Flutter缓存..."
    flutter clean
    flutter pub get
fi

# 使用CanvasKit渲染器以获得更好的性能
# 添加 --web-renderer canvaskit 使用更好的渲染器
# 添加 --profile 进行性能分析
echo "🎯 启动开发服务器..."
flutter run -d web-server \
    --web-port=8081 \
    --web-renderer auto \
    --dart-define=FLUTTER_WEB_USE_SKIA=true \
    --dart-define=FLUTTER_WEB_CANVASKIT_URL=https://www.gstatic.com/flutter-canvaskit/a56e0e9e5dc59eb9abcba9e3e4b3e69421e4a9f2/

# 备选命令：
# 使用HTML渲染器（更快的首次加载）：
# flutter run -d web-server --web-port=8081 --web-renderer html

# 性能分析模式：
# flutter run -d web-server --web-port=8081 --profile