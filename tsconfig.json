{"compilerOptions": {"target": "ES2022", "module": "commonjs", "lib": ["ES2022", "DOM", "DOM.Iterable"], "jsx": "react-jsx", "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./", "composite": true, "removeComments": true, "noEmit": true, "importHelpers": true, "downlevelIteration": true, "strict": false, "noImplicitAny": true, "strictNullChecks": false, "strictFunctionTypes": false, "strictBindCallApply": false, "strictPropertyInitialization": false, "noImplicitThis": false, "alwaysStrict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": false, "noImplicitOverride": false, "noPropertyAccessFromIndexSignature": false, "moduleResolution": "node", "baseUrl": "./", "paths": {"@apps/*": ["apps/*"], "@infrastructure/*": ["infrastructure/*"], "@docs/*": ["docs/*"], "@/*": ["apps/unified-bff/src/*"]}, "types": ["node"], "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "resolveJsonModule": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true}, "include": ["apps/**/*", "infrastructure/**/*", "scripts/**/*", "*.js", "*.ts"], "exclude": ["node_modules", "**/node_modules", "**/dist", "**/build", "**/.next", "**/coverage", "**/*.spec.ts", "**/*.test.ts", "apps/mobile/**/*", "apps/admin-web/**/*"]}