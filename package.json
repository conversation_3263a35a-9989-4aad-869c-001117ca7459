{"name": "masteryos", "version": "1.0.0", "description": "基于10,000小时法则的智能化技能培养与跟踪系统", "type": "module", "private": true, "scripts": {"dev": "./scripts/start-all-services.sh", "dev:stop": "./scripts/stop-all-services.sh", "dev:status": "./scripts/1w-db.sh status && echo '\n🌐 服务访问地址:' && echo 'Flutter Web: http://localhost:8080' && echo 'Flutter Admin Web: http://localhost:3200' && echo '统一BFF: http://localhost:3102'", "build": "echo '请先创建应用代码后再构建'", "test": "echo '请先创建应用代码后再运行测试'", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write \"**/*.{ts,js,json,md}\"", "typecheck": "tsc --noEmit", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "db:connect": "psql -h localhost -p 8182 -U masteryos -d masteryos", "db:start": "./scripts/1w-db.sh start", "db:stop": "./scripts/1w-db.sh stop", "db:status": "./scripts/1w-db.sh status"}, "devDependencies": {"@eslint/js": "^9.32.0", "@types/node": "^20.19.9", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.32.0", "eslint-config-prettier": "^9.1.2", "eslint-plugin-prettier": "^5.5.3", "globals": "^16.3.0", "prettier": "^3.6.2", "tslib": "^2.8.1", "typescript": "5.8.3"}, "engines": {"node": ">=22.18.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@10.14.0", "keywords": ["skill-tracking", "learning", "10000-hours", "typescript"], "author": "MasteryOS Team", "license": "MIT"}