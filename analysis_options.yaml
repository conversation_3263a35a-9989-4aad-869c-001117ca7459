# 项目级别的代码分析配置
# Monorepo级别配置，不依赖Flutter包
# 各个子项目(apps/*, packages/*)有自己的analysis_options.yaml

# include: package:flutter_lints/flutter.yaml  # 在monorepo根目录不可用

analyzer:
  language:
    strict-casts: true
    strict-inference: true
    strict-raw-types: true
  
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "**/*.config.dart"
    - "**/*.env.dart"
    - "**/generated/**"
    - "build/**"
    - ".dart_tool/**"
    - "pubspec.yaml"
    
  errors:
    # 将某些警告提升为错误
    missing_required_param: error
    missing_return: error
    todo: warning
    deprecated_member_use_from_same_package: warning

linter:
  rules:
    # 额外的严格规则
    always_declare_return_types: true
    always_put_control_body_on_new_line: true
    always_put_required_named_parameters_first: true
    always_require_non_null_named_parameters: true
    annotate_overrides: true
    avoid_bool_literals_in_conditional_expressions: true
    avoid_empty_else: true
    avoid_init_to_null: true
    avoid_null_checks_in_equality_operators: true
    avoid_relative_lib_imports: true
    avoid_return_types_on_setters: true
    avoid_shadowing_type_parameters: true
    avoid_types_as_parameter_names: true
    avoid_unnecessary_containers: true
    await_only_futures: true
    camel_case_extensions: true
    camel_case_types: true
    constant_identifier_names: true
    curly_braces_in_flow_control_structures: true
    directives_ordering: true
    empty_catches: true
    empty_constructor_bodies: true
    file_names: true
    implementation_imports: true
    library_names: true
    library_prefixes: true
    no_duplicate_case_values: true
    null_closures: true
    prefer_adjacent_string_concatenation: true
    prefer_collection_literals: true
    prefer_conditional_assignment: true
    prefer_contains: true
    prefer_equal_for_default_values: true
    prefer_final_fields: true
    prefer_for_elements_to_map_fromIterable: true
    prefer_generic_function_type_aliases: true
    prefer_if_null_operators: true
    prefer_is_empty: true
    prefer_is_not_empty: true
    prefer_iterable_whereType: true
    prefer_single_quotes: true
    prefer_spread_collections: true
    recursive_getters: true
    slash_for_doc_comments: true
    sort_child_properties_last: true
    type_init_formals: true
    unawaited_futures: true
    unnecessary_brace_in_string_interps: true
    unnecessary_const: true
    unnecessary_getters_setters: true
    unnecessary_new: true
    unnecessary_null_in_if_null_operators: true
    unnecessary_this: true
    unrelated_type_equality_checks: true
    use_function_type_syntax_for_parameters: true
    use_rethrow_when_possible: true
    valid_regexps: true