import 'dart:async';

import 'package:dio/dio.dart';
import 'package:logger/logger.dart';

import 'auth_models.dart';

/// JWT令牌管理器
class TokenManager {
  final Dio _dio;
  final Logger _logger;

  JwtTokens? _currentTokens;
  Timer? _refreshTimer;

  final StreamController<JwtTokens?> _tokensController =
      StreamController<JwtTokens?>.broadcast();

  TokenManager({required Dio dio, Logger? logger})
    : _dio = dio,
      _logger = logger ?? Logger() {
    _setupInterceptor();
  }

  /// 当前令牌流
  Stream<JwtTokens?> get tokensStream => _tokensController.stream;

  /// 当前令牌
  JwtTokens? get currentTokens => _currentTokens;

  /// 是否有有效令牌
  bool get hasValidTokens =>
      _currentTokens != null && !_currentTokens!.isExpired;

  /// 设置令牌
  void setTokens(JwtTokens tokens) {
    _currentTokens = tokens;
    _tokensController.add(tokens);
    _scheduleTokenRefresh();
    _logger.d('令牌已设置，将在${tokens.expiresAt}过期');
  }

  /// 清除令牌
  void clearTokens() {
    _currentTokens = null;
    _tokensController.add(null);
    _cancelTokenRefresh();
    _logger.d('令牌已清除');
  }

  /// 获取访问令牌
  String? getAccessToken() {
    if (_currentTokens == null || _currentTokens!.isExpired) {
      return null;
    }
    return _currentTokens!.accessToken;
  }

  /// 获取刷新令牌
  String? getRefreshToken() {
    return _currentTokens?.refreshToken;
  }

  /// 检查令牌是否即将过期
  bool willTokenExpireSoon() {
    return _currentTokens?.willExpireSoon ?? true;
  }

  /// 设置Dio请求拦截器
  void _setupInterceptor() {
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          // 自动添加Authorization头
          final token = getAccessToken();
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }
          handler.next(options);
        },
        onError: (error, handler) async {
          // 处理401错误，尝试刷新令牌
          if (error.response?.statusCode == 401 && _currentTokens != null) {
            try {
              _logger.d('检测到401错误，尝试刷新令牌');
              final success = await _refreshTokens();
              if (success) {
                // 重试原始请求
                final requestOptions = error.requestOptions;
                final newToken = getAccessToken();
                if (newToken != null) {
                  requestOptions.headers['Authorization'] = 'Bearer $newToken';
                  final response = await _dio.fetch<dynamic>(requestOptions);
                  handler.resolve(response);
                  return;
                }
              }
            } catch (e) {
              _logger.e('令牌刷新失败', error: e);
            }
          }
          handler.next(error);
        },
      ),
    );
  }

  /// 调度令牌刷新
  void _scheduleTokenRefresh() {
    _cancelTokenRefresh();

    if (_currentTokens == null) {
      return;
    }

    // 在令牌过期前5分钟刷新
    final refreshTime = _currentTokens!.expiresAt.subtract(
      const Duration(minutes: 5),
    );

    final now = DateTime.now();
    if (refreshTime.isAfter(now)) {
      final delay = refreshTime.difference(now);
      _refreshTimer = Timer(delay, () async {
        _logger.d('定时刷新令牌');
        await _refreshTokens();
      });
    } else {
      // 令牌很快就要过期，立即刷新
      Timer.run(() async {
        _logger.d('令牌即将过期，立即刷新');
        await _refreshTokens();
      });
    }
  }

  /// 取消令牌刷新
  void _cancelTokenRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  /// 刷新令牌
  Future<bool> _refreshTokens() async {
    if (_currentTokens == null) {
      _logger.w('无可用的刷新令牌');
      return false;
    }

    try {
      final response = await _dio.post<Map<String, dynamic>>(
        '/auth/refresh',
        data: {'refreshToken': _currentTokens!.refreshToken},
        options: Options(
          headers: {'Authorization': null}, // 移除Authorization头
        ),
      );

      final newTokens = JwtTokens.fromJson(
        (response.data as Map<String, dynamic>)['tokens']
            as Map<String, dynamic>,
      );
      setTokens(newTokens);

      _logger.i('令牌刷新成功');
      return true;
    } on DioException catch (e) {
      _logger.e('令牌刷新失败', error: e);
      if (e.response?.statusCode == 401 || e.response?.statusCode == 403) {
        // 刷新令牌无效，清除所有令牌
        clearTokens();
      }
      return false;
    } catch (e) {
      _logger.e('令牌刷新过程中发生意外错误', error: e);
      return false;
    }
  }

  /// 手动刷新令牌
  Future<bool> refreshTokens() async {
    return await _refreshTokens();
  }

  /// 检查令牌状态
  TokenStatus getTokenStatus() {
    if (_currentTokens == null) {
      return TokenStatus.missing;
    }

    if (_currentTokens!.isExpired) {
      return TokenStatus.expired;
    }

    if (_currentTokens!.willExpireSoon) {
      return TokenStatus.expiringSoon;
    }

    return TokenStatus.valid;
  }

  /// 获取令牌剩余时间
  Duration? getTokenRemainingTime() {
    if (_currentTokens == null || _currentTokens!.isExpired) {
      return null;
    }

    return _currentTokens!.expiresAt.difference(DateTime.now());
  }

  /// 释放资源
  void dispose() {
    _cancelTokenRefresh();
    _tokensController.close();
  }
}

/// 令牌状态枚举
enum TokenStatus { missing, expired, expiringSoon, valid }

/// 令牌状态扩展
extension TokenStatusExtension on TokenStatus {
  bool get isValid => this == TokenStatus.valid;
  bool get needsRefresh =>
      this == TokenStatus.expired || this == TokenStatus.expiringSoon;
  bool get isMissing => this == TokenStatus.missing;

  String get description {
    switch (this) {
      case TokenStatus.missing:
        return '令牌缺失';
      case TokenStatus.expired:
        return '令牌已过期';
      case TokenStatus.expiringSoon:
        return '令牌即将过期';
      case TokenStatus.valid:
        return '令牌有效';
    }
  }
}
