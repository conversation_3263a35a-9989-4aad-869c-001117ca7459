import 'package:intl/intl.dart';

/// 格式化工具类
class FormatUtils {
  FormatUtils._();

  /// 格式化文件大小
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    }
    if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    }
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// 格式化数字（添加千分位分隔符）
  static String formatNumber(num number, {String locale = 'zh_CN'}) {
    return NumberFormat('#,##0', locale).format(number);
  }

  /// 格式化货币
  static String formatCurrency(
    num amount, {
    String symbol = '¥',
    int decimalDigits = 2,
  }) {
    return '$symbol${NumberFormat('#,##0.${'0' * decimalDigits}').format(amount)}';
  }

  /// 格式化百分比
  static String formatPercentage(double value, {int decimalDigits = 1}) {
    return '${(value * 100).toStringAsFixed(decimalDigits)}%';
  }

  /// 格式化手机号（添加空格）
  static String formatPhoneNumber(String phone) {
    if (phone.length != 11) {
      return phone;
    }
    return '${phone.substring(0, 3)} ${phone.substring(3, 7)} ${phone.substring(7)}';
  }

  /// 格式化身份证号（隐藏中间部分）
  static String formatIdCard(String idCard, {String mask = '*'}) {
    if (idCard.length != 18) {
      return idCard;
    }
    return '${idCard.substring(0, 6)}${mask * 8}${idCard.substring(14)}';
  }

  /// 格式化银行卡号（添加空格）
  static String formatBankCard(String cardNumber) {
    final cleaned = cardNumber.replaceAll(RegExp(r'[\s-]'), '');
    final formatted = cleaned.replaceAllMapped(
      RegExp(r'(.{4})'),
      (match) => '${match.group(1)} ',
    );
    return formatted.trim();
  }

  /// 格式化银行卡号（隐藏中间部分）
  static String formatBankCardMasked(String cardNumber, {String mask = '*'}) {
    final cleaned = cardNumber.replaceAll(RegExp(r'[\s-]'), '');
    if (cleaned.length < 8) {
      return cardNumber;
    }

    final first = cleaned.substring(0, 4);
    final last = cleaned.substring(cleaned.length - 4);
    final middle = mask * 8;

    return '$first $middle $last';
  }

  /// 格式化邮箱（隐藏用户名部分）
  static String formatEmailMasked(String email, {String mask = '*'}) {
    final parts = email.split('@');
    if (parts.length != 2) {
      return email;
    }

    final username = parts[0];
    final domain = parts[1];

    if (username.length <= 2) {
      return email;
    }

    final visibleStart = username.substring(0, 1);
    final visibleEnd = username.substring(username.length - 1);
    final maskedMiddle = mask * (username.length - 2);

    return '$visibleStart$maskedMiddle$visibleEnd@$domain';
  }

  /// 格式化时长（秒转为时分秒）
  static String formatDuration(int seconds) {
    final hours = seconds ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    final remainingSeconds = seconds % 60;

    if (hours > 0) {
      return '$hours:${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
    } else {
      return '$minutes:${remainingSeconds.toString().padLeft(2, '0')}';
    }
  }

  /// 格式化时长（毫秒转为可读格式）
  static String formatDurationFromMillis(int milliseconds) {
    final seconds = milliseconds ~/ 1000;
    return formatDuration(seconds);
  }

  /// 格式化距离
  static String formatDistance(double meters) {
    if (meters < 1000) {
      return '${meters.toStringAsFixed(0)}米';
    } else {
      return '${(meters / 1000).toStringAsFixed(1)}公里';
    }
  }

  /// 格式化评分（星级）
  static String formatRating(double rating, {int maxStars = 5}) {
    final fullStars = rating.floor();
    final hasHalfStar = rating - fullStars >= 0.5;
    final emptyStars = maxStars - fullStars - (hasHalfStar ? 1 : 0);

    return '★' * fullStars + (hasHalfStar ? '☆' : '') + '☆' * emptyStars;
  }

  /// 格式化字节速度
  static String formatSpeed(double bytesPerSecond) {
    if (bytesPerSecond < 1024) {
      return '${bytesPerSecond.toStringAsFixed(0)} B/s';
    } else if (bytesPerSecond < 1024 * 1024) {
      return '${(bytesPerSecond / 1024).toStringAsFixed(1)} KB/s';
    } else {
      return '${(bytesPerSecond / (1024 * 1024)).toStringAsFixed(1)} MB/s';
    }
  }

  /// 格式化温度
  static String formatTemperature(
    double celsius, {
    bool includeFahrenheit = false,
  }) {
    if (includeFahrenheit) {
      final fahrenheit = celsius * 9 / 5 + 32;
      return '${celsius.toStringAsFixed(1)}°C (${fahrenheit.toStringAsFixed(1)}°F)';
    }
    return '${celsius.toStringAsFixed(1)}°C';
  }

  /// 格式化数字为中文大写
  static String formatNumberToChinese(int number) {
    const digits = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
    const units = ['', '十', '百', '千', '万', '十万', '百万', '千万', '亿'];

    if (number == 0) {
      return '零';
    }
    if (number < 0) {
      return '负${formatNumberToChinese(-number)}';
    }

    final str = number.toString();
    final result = StringBuffer();

    for (int i = 0; i < str.length; i++) {
      final digit = int.parse(str[i]);
      final unitIndex = str.length - i - 1;

      if (digit != 0) {
        result.write(digits[digit]);
        if (unitIndex > 0) {
          result.write(units[unitIndex]);
        }
      } else if (result.isNotEmpty &&
          i < str.length - 1 &&
          int.parse(str[i + 1]) != 0) {
        result.write('零');
      }
    }

    return result.toString();
  }

  /// 格式化IP地址
  static String formatIpAddress(String ip) {
    final parts = ip.split('.');
    if (parts.length != 4) {
      return ip;
    }

    return parts
        .map((part) => int.tryParse(part)?.toString() ?? part)
        .join('.');
  }

  /// 格式化版本号
  static String formatVersion(String version) {
    final parts = version.split('.');
    return parts
        .map((part) {
          final num = int.tryParse(part);
          return num?.toString() ?? part;
        })
        .join('.');
  }

  /// 格式化MAC地址
  static String formatMacAddress(String mac, {String separator = ':'}) {
    final cleaned = mac.replaceAll(RegExp(r'[:-]'), '').toUpperCase();
    if (cleaned.length != 12) {
      return mac;
    }

    final parts = <String>[];
    for (int i = 0; i < cleaned.length; i += 2) {
      parts.add(cleaned.substring(i, i + 2));
    }

    return parts.join(separator);
  }
}
