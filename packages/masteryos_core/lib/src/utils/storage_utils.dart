import 'dart:convert';

import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 存储工具类
class StorageUtils {
  StorageUtils._();

  static SharedPreferences? _prefs;
  static final Logger _logger = Logger();

  /// 初始化SharedPreferences
  static Future<void> initialize() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// 获取SharedPreferences实例
  static Future<SharedPreferences> _getPrefs() async {
    _prefs ??= await SharedPreferences.getInstance();
    return _prefs!;
  }

  /// 保存字符串
  static Future<bool> setString(String key, String value) async {
    try {
      final prefs = await _getPrefs();
      return await prefs.setString(key, value);
    } catch (e) {
      _logger.e('保存字符串失败: $key', error: e);
      return false;
    }
  }

  /// 获取字符串
  static Future<String?> getString(String key, {String? defaultValue}) async {
    try {
      final prefs = await _getPrefs();
      return prefs.getString(key) ?? defaultValue;
    } catch (e) {
      _logger.e('获取字符串失败: $key', error: e);
      return defaultValue;
    }
  }

  /// 保存整数
  static Future<bool> setInt(String key, int value) async {
    try {
      final prefs = await _getPrefs();
      return await prefs.setInt(key, value);
    } catch (e) {
      _logger.e('保存整数失败: $key', error: e);
      return false;
    }
  }

  /// 获取整数
  static Future<int?> getInt(String key, {int? defaultValue}) async {
    try {
      final prefs = await _getPrefs();
      return prefs.getInt(key) ?? defaultValue;
    } catch (e) {
      _logger.e('获取整数失败: $key', error: e);
      return defaultValue;
    }
  }

  /// 保存双精度浮点数
  static Future<bool> setDouble(String key, double value) async {
    try {
      final prefs = await _getPrefs();
      return await prefs.setDouble(key, value);
    } catch (e) {
      _logger.e('保存双精度浮点数失败: $key', error: e);
      return false;
    }
  }

  /// 获取双精度浮点数
  static Future<double?> getDouble(String key, {double? defaultValue}) async {
    try {
      final prefs = await _getPrefs();
      return prefs.getDouble(key) ?? defaultValue;
    } catch (e) {
      _logger.e('获取双精度浮点数失败: $key', error: e);
      return defaultValue;
    }
  }

  /// 保存布尔值
  static Future<bool> setBool(String key, bool value) async {
    try {
      final prefs = await _getPrefs();
      return await prefs.setBool(key, value);
    } catch (e) {
      _logger.e('保存布尔值失败: $key', error: e);
      return false;
    }
  }

  /// 获取布尔值
  static Future<bool?> getBool(String key, {bool? defaultValue}) async {
    try {
      final prefs = await _getPrefs();
      return prefs.getBool(key) ?? defaultValue;
    } catch (e) {
      _logger.e('获取布尔值失败: $key', error: e);
      return defaultValue;
    }
  }

  /// 保存字符串列表
  static Future<bool> setStringList(String key, List<String> value) async {
    try {
      final prefs = await _getPrefs();
      return await prefs.setStringList(key, value);
    } catch (e) {
      _logger.e('保存字符串列表失败: $key', error: e);
      return false;
    }
  }

  /// 获取字符串列表
  static Future<List<String>?> getStringList(
    String key, {
    List<String>? defaultValue,
  }) async {
    try {
      final prefs = await _getPrefs();
      return prefs.getStringList(key) ?? defaultValue;
    } catch (e) {
      _logger.e('获取字符串列表失败: $key', error: e);
      return defaultValue;
    }
  }

  /// 保存JSON对象
  static Future<bool> setJson(String key, Map<String, dynamic> value) async {
    try {
      final jsonString = jsonEncode(value);
      return await setString(key, jsonString);
    } catch (e) {
      _logger.e('保存JSON对象失败: $key', error: e);
      return false;
    }
  }

  /// 获取JSON对象
  static Future<Map<String, dynamic>?> getJson(
    String key, {
    Map<String, dynamic>? defaultValue,
  }) async {
    try {
      final jsonString = await getString(key);
      if (jsonString == null) {
        return defaultValue;
      }
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      _logger.e('获取JSON对象失败: $key', error: e);
      return defaultValue;
    }
  }

  /// 保存JSON列表
  static Future<bool> setJsonList(
    String key,
    List<Map<String, dynamic>> value,
  ) async {
    try {
      final jsonString = jsonEncode(value);
      return await setString(key, jsonString);
    } catch (e) {
      _logger.e('保存JSON列表失败: $key', error: e);
      return false;
    }
  }

  /// 获取JSON列表
  static Future<List<Map<String, dynamic>>?> getJsonList(
    String key, {
    List<Map<String, dynamic>>? defaultValue,
  }) async {
    try {
      final jsonString = await getString(key);
      if (jsonString == null) {
        return defaultValue;
      }
      final decoded = jsonDecode(jsonString) as List;
      return decoded.cast<Map<String, dynamic>>();
    } catch (e) {
      _logger.e('获取JSON列表失败: $key', error: e);
      return defaultValue;
    }
  }

  /// 检查键是否存在
  static Future<bool> containsKey(String key) async {
    try {
      final prefs = await _getPrefs();
      return prefs.containsKey(key);
    } catch (e) {
      _logger.e('检查键存在性失败: $key', error: e);
      return false;
    }
  }

  /// 移除指定键
  static Future<bool> remove(String key) async {
    try {
      final prefs = await _getPrefs();
      return await prefs.remove(key);
    } catch (e) {
      _logger.e('移除键失败: $key', error: e);
      return false;
    }
  }

  /// 清除所有数据
  static Future<bool> clear() async {
    try {
      final prefs = await _getPrefs();
      return await prefs.clear();
    } catch (e) {
      _logger.e('清除所有数据失败', error: e);
      return false;
    }
  }

  /// 获取所有键
  static Future<Set<String>> getKeys() async {
    try {
      final prefs = await _getPrefs();
      return prefs.getKeys();
    } catch (e) {
      _logger.e('获取所有键失败', error: e);
      return <String>{};
    }
  }

  /// 批量保存数据
  static Future<bool> setBatch(Map<String, dynamic> data) async {
    try {
      final prefs = await _getPrefs();
      bool allSuccess = true;

      for (final entry in data.entries) {
        final key = entry.key;
        final value = entry.value;

        bool success = false;
        if (value is String) {
          success = await prefs.setString(key, value);
        } else if (value is int) {
          success = await prefs.setInt(key, value);
        } else if (value is double) {
          success = await prefs.setDouble(key, value);
        } else if (value is bool) {
          success = await prefs.setBool(key, value);
        } else if (value is List<String>) {
          success = await prefs.setStringList(key, value);
        } else {
          // 其他类型转为JSON字符串
          success = await prefs.setString(key, jsonEncode(value));
        }

        if (!success) {
          allSuccess = false;
          _logger.w('批量保存失败: $key');
        }
      }

      return allSuccess;
    } catch (e) {
      _logger.e('批量保存数据失败', error: e);
      return false;
    }
  }

  /// 批量获取数据
  static Future<Map<String, dynamic>> getBatch(List<String> keys) async {
    try {
      final prefs = await _getPrefs();
      final result = <String, dynamic>{};

      for (final key in keys) {
        if (prefs.containsKey(key)) {
          final value = prefs.get(key);
          result[key] = value;
        }
      }

      return result;
    } catch (e) {
      _logger.e('批量获取数据失败', error: e);
      return {};
    }
  }

  /// 批量移除数据
  static Future<bool> removeBatch(List<String> keys) async {
    try {
      final prefs = await _getPrefs();
      bool allSuccess = true;

      for (final key in keys) {
        final success = await prefs.remove(key);
        if (!success) {
          allSuccess = false;
          _logger.w('批量移除失败: $key');
        }
      }

      return allSuccess;
    } catch (e) {
      _logger.e('批量移除数据失败', error: e);
      return false;
    }
  }

  /// 获取存储使用情况统计
  static Future<StorageStats> getStorageStats() async {
    try {
      final prefs = await _getPrefs();
      final keys = prefs.getKeys();
      int totalSize = 0;
      final typeCounts = <String, int>{};

      for (final key in keys) {
        final value = prefs.get(key);

        if (value is String) {
          totalSize += value.length;
          typeCounts['String'] = (typeCounts['String'] ?? 0) + 1;
        } else if (value is int) {
          totalSize += 8; // 假设int占8字节
          typeCounts['int'] = (typeCounts['int'] ?? 0) + 1;
        } else if (value is double) {
          totalSize += 8; // 假设double占8字节
          typeCounts['double'] = (typeCounts['double'] ?? 0) + 1;
        } else if (value is bool) {
          totalSize += 1; // 假设bool占1字节
          typeCounts['bool'] = (typeCounts['bool'] ?? 0) + 1;
        } else if (value is List<String>) {
          totalSize += value.fold(0, (sum, item) => sum + item.length);
          typeCounts['List<String>'] = (typeCounts['List<String>'] ?? 0) + 1;
        }
      }

      return StorageStats(
        totalKeys: keys.length,
        estimatedSize: totalSize,
        typeCounts: typeCounts,
      );
    } catch (e) {
      _logger.e('获取存储统计失败', error: e);
      return StorageStats(totalKeys: 0, estimatedSize: 0, typeCounts: {});
    }
  }

  /// 导出所有数据
  static Future<Map<String, dynamic>> exportAll() async {
    try {
      final prefs = await _getPrefs();
      final keys = prefs.getKeys();
      final data = <String, dynamic>{};

      for (final key in keys) {
        data[key] = prefs.get(key);
      }

      return data;
    } catch (e) {
      _logger.e('导出所有数据失败', error: e);
      return {};
    }
  }

  /// 导入数据
  static Future<bool> importData(Map<String, dynamic> data) async {
    try {
      return await setBatch(data);
    } catch (e) {
      _logger.e('导入数据失败', error: e);
      return false;
    }
  }
}

/// 存储统计信息
class StorageStats {
  final int totalKeys;
  final int estimatedSize;
  final Map<String, int> typeCounts;

  StorageStats({
    required this.totalKeys,
    required this.estimatedSize,
    required this.typeCounts,
  });

  @override
  String toString() {
    return 'StorageStats(keys: $totalKeys, size: ${estimatedSize}B, types: $typeCounts)';
  }
}
