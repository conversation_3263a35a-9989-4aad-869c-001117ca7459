import 'dart:convert';

import 'package:crypto/crypto.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class StorageService {
  static StorageService? _instance;
  static SharedPreferences? _preferences;

  StorageService._internal();

  factory StorageService() {
    _instance ??= StorageService._internal();
    return _instance!;
  }

  // 初始化
  static Future<void> initialize() async {
    _preferences = await SharedPreferences.getInstance();
  }

  SharedPreferences get _prefs {
    if (_preferences == null) {
      throw Exception(
        'StorageService not initialized. Call StorageService.initialize() first.',
      );
    }
    return _preferences!;
  }

  // 存储键常量
  static const String _keyAccessToken = 'access_token';
  static const String _keyRefreshToken = 'refresh_token';
  static const String _keyUserData = 'user_data';
  static const String _keyDeviceId = 'device_id';
  static const String _keyFirstLaunch = 'first_launch';
  static const String _keyAppVersion = 'app_version';
  static const String _keyLanguage = 'language';
  static const String _keyThemeMode = 'theme_mode';
  static const String _keyNotificationSettings = 'notification_settings';
  static const String _keyUserPreferences = 'user_preferences';
  static const String _keyLastSyncTime = 'last_sync_time';
  static const String _keyCachedData = 'cached_data_';

  // 认证相关
  Future<void> setAccessToken(String token) async {
    await _prefs.setString(_keyAccessToken, token);
  }

  Future<String?> getAccessToken() async {
    return _prefs.getString(_keyAccessToken);
  }

  Future<void> setRefreshToken(String token) async {
    await _prefs.setString(_keyRefreshToken, token);
  }

  Future<String?> getRefreshToken() async {
    return _prefs.getString(_keyRefreshToken);
  }

  Future<void> setUserData(Map<String, dynamic> userData) async {
    await _prefs.setString(_keyUserData, jsonEncode(userData));
  }

  Future<Map<String, dynamic>?> getUserData() async {
    final data = _prefs.getString(_keyUserData);
    if (data == null) {
      return null;
    }
    return jsonDecode(data) as Map<String, dynamic>;
  }

  Future<void> clearAuthData() async {
    await Future.wait([
      _prefs.remove(_keyAccessToken),
      _prefs.remove(_keyRefreshToken),
      _prefs.remove(_keyUserData),
    ]);
  }

  // 设备相关
  Future<String> getDeviceId() async {
    String? deviceId = _prefs.getString(_keyDeviceId);

    if (deviceId == null) {
      deviceId = await _generateDeviceId();
      await _prefs.setString(_keyDeviceId, deviceId);
    }

    return deviceId;
  }

  Future<String> _generateDeviceId() async {
    final deviceInfo = DeviceInfoPlugin();
    String deviceId;

    try {
      if (defaultTargetPlatform == TargetPlatform.android) {
        final androidInfo = await deviceInfo.androidInfo;
        deviceId = androidInfo.id;
      } else if (defaultTargetPlatform == TargetPlatform.iOS) {
        final iosInfo = await deviceInfo.iosInfo;
        deviceId = iosInfo.identifierForVendor ?? 'unknown_ios';
      } else if (kIsWeb) {
        final webInfo = await deviceInfo.webBrowserInfo;
        deviceId = '${webInfo.userAgent}_${webInfo.vendor}_${webInfo.platform}';
      } else {
        deviceId = 'unknown_platform_${DateTime.now().millisecondsSinceEpoch}';
      }
    } catch (e) {
      // 如果获取设备信息失败，生成一个随机ID
      deviceId = 'generated_${DateTime.now().millisecondsSinceEpoch}';
    }

    // 对设备ID进行哈希处理以保护隐私
    final bytes = utf8.encode(deviceId);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // 应用状态
  Future<bool> isFirstLaunch() async {
    return _prefs.getBool(_keyFirstLaunch) ?? true;
  }

  Future<void> setFirstLaunchCompleted() async {
    await _prefs.setBool(_keyFirstLaunch, false);
  }

  Future<void> setAppVersion(String version) async {
    await _prefs.setString(_keyAppVersion, version);
  }

  Future<String?> getLastAppVersion() async {
    return _prefs.getString(_keyAppVersion);
  }

  // 用户偏好设置
  Future<void> setLanguage(String languageCode) async {
    await _prefs.setString(_keyLanguage, languageCode);
  }

  Future<String?> getLanguage() async {
    return _prefs.getString(_keyLanguage);
  }

  Future<void> setThemeMode(String themeMode) async {
    await _prefs.setString(_keyThemeMode, themeMode);
  }

  Future<String?> getThemeMode() async {
    return _prefs.getString(_keyThemeMode);
  }

  Future<void> setNotificationSettings(Map<String, bool> settings) async {
    await _prefs.setString(_keyNotificationSettings, jsonEncode(settings));
  }

  Future<Map<String, bool>> getNotificationSettings() async {
    final data = _prefs.getString(_keyNotificationSettings);
    if (data == null) {
      return {
        'push_notifications': true,
        'email_notifications': true,
        'marketing_notifications': false,
      };
    }
    final Map<String, dynamic> decoded =
        jsonDecode(data) as Map<String, dynamic>;
    return decoded.map((key, value) => MapEntry(key, value as bool));
  }

  Future<void> setUserPreferences(Map<String, dynamic> preferences) async {
    await _prefs.setString(_keyUserPreferences, jsonEncode(preferences));
  }

  Future<Map<String, dynamic>> getUserPreferences() async {
    final data = _prefs.getString(_keyUserPreferences);
    if (data == null) {
      return {};
    }
    return jsonDecode(data) as Map<String, dynamic>;
  }

  // 缓存相关
  Future<void> setCachedData(
    String key,
    Map<String, dynamic> data, {
    Duration? expiration,
  }) async {
    final cacheData = {
      'data': data,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'expiration': expiration?.inMilliseconds,
    };
    await _prefs.setString('$_keyCachedData$key', jsonEncode(cacheData));
  }

  Future<Map<String, dynamic>?> getCachedData(String key) async {
    final data = _prefs.getString('$_keyCachedData$key');
    if (data == null) {
      return null;
    }

    final Map<String, dynamic> cacheData =
        jsonDecode(data) as Map<String, dynamic>;
    final timestamp = cacheData['timestamp'] as int;
    final expiration = cacheData['expiration'] as int?;

    // 检查是否过期
    if (expiration != null) {
      final expirationTime = DateTime.fromMillisecondsSinceEpoch(
        timestamp,
      ).add(Duration(milliseconds: expiration));
      if (DateTime.now().isAfter(expirationTime)) {
        await removeCachedData(key);
        return null;
      }
    }

    return cacheData['data'] as Map<String, dynamic>;
  }

  Future<void> removeCachedData(String key) async {
    await _prefs.remove('$_keyCachedData$key');
  }

  Future<void> clearAllCachedData() async {
    final keys = _prefs.getKeys().where(
      (key) => key.startsWith(_keyCachedData),
    );
    await Future.wait(keys.map((key) => _prefs.remove(key)));
  }

  // 同步相关
  Future<void> setLastSyncTime(DateTime time) async {
    await _prefs.setInt(_keyLastSyncTime, time.millisecondsSinceEpoch);
  }

  Future<DateTime?> getLastSyncTime() async {
    final timestamp = _prefs.getInt(_keyLastSyncTime);
    if (timestamp == null) {
      return null;
    }
    return DateTime.fromMillisecondsSinceEpoch(timestamp);
  }

  // 通用存储方法
  Future<void> setString(String key, String value) async {
    await _prefs.setString(key, value);
  }

  String? getString(String key) {
    return _prefs.getString(key);
  }

  Future<void> setInt(String key, int value) async {
    await _prefs.setInt(key, value);
  }

  int? getInt(String key) {
    return _prefs.getInt(key);
  }

  Future<void> setBool(String key, bool value) async {
    await _prefs.setBool(key, value);
  }

  bool? getBool(String key) {
    return _prefs.getBool(key);
  }

  Future<void> setDouble(String key, double value) async {
    await _prefs.setDouble(key, value);
  }

  double? getDouble(String key) {
    return _prefs.getDouble(key);
  }

  Future<void> setStringList(String key, List<String> value) async {
    await _prefs.setStringList(key, value);
  }

  List<String>? getStringList(String key) {
    return _prefs.getStringList(key);
  }

  Future<void> remove(String key) async {
    await _prefs.remove(key);
  }

  bool containsKey(String key) {
    return _prefs.containsKey(key);
  }

  Set<String> getKeys() {
    return _prefs.getKeys();
  }

  Future<void> clear() async {
    await _prefs.clear();
  }

  // 获取存储使用情况
  Map<String, dynamic> getStorageInfo() {
    final keys = _prefs.getKeys();
    final authKeys = keys
        .where(
          (key) =>
              key == _keyAccessToken ||
              key == _keyRefreshToken ||
              key == _keyUserData,
        )
        .length;
    final cacheKeys = keys
        .where((key) => key.startsWith(_keyCachedData))
        .length;
    final preferenceKeys = keys.length - authKeys - cacheKeys;

    return {
      'totalKeys': keys.length,
      'authKeys': authKeys,
      'cacheKeys': cacheKeys,
      'preferenceKeys': preferenceKeys,
      'allKeys': keys.toList(),
    };
  }
}
