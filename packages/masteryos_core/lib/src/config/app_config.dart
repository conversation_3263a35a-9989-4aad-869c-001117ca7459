import 'package:flutter/foundation.dart';
import 'package:package_info_plus/package_info_plus.dart';

enum Environment { development, staging, production }

class AppConfig {
  static late PackageInfo _packageInfo;
  static Environment _environment = Environment.development;

  // 应用信息
  static String get appName => _packageInfo.appName;
  static String get packageName => _packageInfo.packageName;
  static String get version => _packageInfo.version;
  static String get buildNumber => _packageInfo.buildNumber;

  // 环境配置
  static Environment get environment => _environment;
  static bool get isDevelopment => _environment == Environment.development;
  static bool get isStaging => _environment == Environment.staging;
  static bool get isProduction => _environment == Environment.production;
  static bool get isDebug => kDebugMode;
  static bool get isRelease => kReleaseMode;

  // API配置
  static String get baseUrl {
    switch (_environment) {
      case Environment.development:
        return 'http://localhost:3001';
      case Environment.staging:
        return 'https://staging-api.masteryos.com';
      case Environment.production:
        return 'https://api.masteryos.com';
    }
  }

  static String get webSocketUrl {
    switch (_environment) {
      case Environment.development:
        return 'ws://localhost:3001';
      case Environment.staging:
        return 'wss://staging-api.masteryos.com';
      case Environment.production:
        return 'wss://api.masteryos.com';
    }
  }

  // 功能开关
  static bool get enableAnalytics => !isDevelopment;
  static bool get enableCrashReporting => isProduction;
  static bool get enableLogging => isDevelopment || isStaging;
  static bool get enableDebugTools => isDevelopment;

  // 超时配置
  static const Duration defaultTimeout = Duration(seconds: 30);
  static const Duration uploadTimeout = Duration(minutes: 5);
  static const Duration downloadTimeout = Duration(minutes: 10);

  // 分页配置
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // 缓存配置
  static const Duration cacheExpiration = Duration(hours: 24);
  static const int maxCacheSize = 50 * 1024 * 1024; // 50MB

  // 文件上传配置
  static const int maxFileSize = 100 * 1024 * 1024; // 100MB
  static const List<String> allowedImageTypes = [
    'jpg',
    'jpeg',
    'png',
    'gif',
    'webp',
    'bmp',
  ];
  static const List<String> allowedDocumentTypes = [
    'pdf',
    'doc',
    'docx',
    'txt',
    'md',
    'rtf',
  ];
  static const List<String> allowedVideoTypes = [
    'mp4',
    'avi',
    'mkv',
    'mov',
    'wmv',
    'flv',
  ];
  static const List<String> allowedAudioTypes = [
    'mp3',
    'wav',
    'flac',
    'aac',
    'ogg',
    'm4a',
  ];

  // 安全配置
  static const int maxLoginAttempts = 5;
  static const Duration lockoutDuration = Duration(minutes: 15);
  static const Duration tokenExpiration = Duration(hours: 24);
  static const Duration refreshTokenExpiration = Duration(days: 30);

  // UI配置
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration splashDuration = Duration(seconds: 2);
  static const double defaultRadius = 8.0;
  static const double defaultPadding = 16.0;

  // 初始化配置
  static Future<void> initialize({Environment? environment}) async {
    _packageInfo = await PackageInfo.fromPlatform();

    if (environment != null) {
      _environment = environment;
    } else {
      // 根据构建模式自动确定环境
      if (kDebugMode) {
        _environment = Environment.development;
      } else if (kProfileMode) {
        _environment = Environment.staging;
      } else {
        _environment = Environment.production;
      }
    }
  }

  // 设置环境
  static void setEnvironment(Environment environment) {
    _environment = environment;
  }

  // 获取环境名称
  static String get environmentName {
    switch (_environment) {
      case Environment.development:
        return 'Development';
      case Environment.staging:
        return 'Staging';
      case Environment.production:
        return 'Production';
    }
  }

  // 获取完整的用户代理字符串
  static String get userAgent {
    return '$appName/$version ($packageName; ${defaultTargetPlatform.name})';
  }

  // 获取应用标识符
  static String get appId {
    return '${packageName}_${version}_$buildNumber';
  }

  // 验证文件类型
  static bool isAllowedFileType(String extension, String category) {
    final ext = extension.toLowerCase();
    switch (category.toLowerCase()) {
      case 'image':
        return allowedImageTypes.contains(ext);
      case 'document':
        return allowedDocumentTypes.contains(ext);
      case 'video':
        return allowedVideoTypes.contains(ext);
      case 'audio':
        return allowedAudioTypes.contains(ext);
      default:
        return false;
    }
  }

  // 获取所有允许的文件类型
  static List<String> get allAllowedFileTypes {
    return [
      ...allowedImageTypes,
      ...allowedDocumentTypes,
      ...allowedVideoTypes,
      ...allowedAudioTypes,
    ];
  }

  // 调试信息
  static Map<String, dynamic> get debugInfo {
    return {
      'appName': appName,
      'packageName': packageName,
      'version': version,
      'buildNumber': buildNumber,
      'environment': environmentName,
      'platform': defaultTargetPlatform.name,
      'isDebug': isDebug,
      'baseUrl': baseUrl,
      'features': {
        'analytics': enableAnalytics,
        'crashReporting': enableCrashReporting,
        'logging': enableLogging,
        'debugTools': enableDebugTools,
      },
    };
  }
}
