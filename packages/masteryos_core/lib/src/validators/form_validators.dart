/// 表单验证器
class FormValidators {
  FormValidators._();

  /// 必填验证
  static String? required(dynamic value, {String? message}) {
    if (value == null ||
        (value is String && value.trim().isEmpty) ||
        (value is List && value.isEmpty) ||
        (value is Map && value.isEmpty)) {
      return message ?? '此字段为必填项';
    }
    return null;
  }

  /// 邮箱验证
  static String? email(String? value, {String? message}) {
    if (value == null || value.isEmpty) {
      return null;
    }

    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    if (!emailRegex.hasMatch(value)) {
      return message ?? '请输入有效的邮箱地址';
    }
    return null;
  }

  /// 手机号验证（中国大陆）
  static String? phone(String? value, {String? message}) {
    if (value == null || value.isEmpty) {
      return null;
    }

    final phoneRegex = RegExp(r'^1[3-9]\d{9}$');
    if (!phoneRegex.hasMatch(value)) {
      return message ?? '请输入有效的手机号码';
    }
    return null;
  }

  /// 密码强度验证
  static String? password(
    String? value, {
    int minLength = 6,
    int maxLength = 20,
    bool requireUppercase = false,
    bool requireLowercase = false,
    bool requireNumbers = false,
    bool requireSpecialChars = false,
    String? message,
  }) {
    if (value == null || value.isEmpty) {
      return null;
    }

    if (value.length < minLength) {
      return message ?? '密码长度至少为$minLength位';
    }

    if (value.length > maxLength) {
      return message ?? '密码长度不能超过$maxLength位';
    }

    if (requireUppercase && !RegExp(r'[A-Z]').hasMatch(value)) {
      return message ?? '密码必须包含大写字母';
    }

    if (requireLowercase && !RegExp(r'[a-z]').hasMatch(value)) {
      return message ?? '密码必须包含小写字母';
    }

    if (requireNumbers && !RegExp(r'\d').hasMatch(value)) {
      return message ?? '密码必须包含数字';
    }

    if (requireSpecialChars &&
        !RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(value)) {
      return message ?? '密码必须包含特殊字符';
    }

    return null;
  }

  /// 确认密码验证
  static String? confirmPassword(
    String? value,
    String? originalPassword, {
    String? message,
  }) {
    if (value == null || value.isEmpty) {
      return null;
    }

    if (value != originalPassword) {
      return message ?? '两次输入的密码不一致';
    }
    return null;
  }

  /// 长度验证
  static String? length(
    String? value, {
    int? min,
    int? max,
    int? exact,
    String? message,
  }) {
    if (value == null || value.isEmpty) {
      return null;
    }

    if (exact != null && value.length != exact) {
      return message ?? '长度必须为$exact位';
    }

    if (min != null && value.length < min) {
      return message ?? '长度不能少于$min位';
    }

    if (max != null && value.length > max) {
      return message ?? '长度不能超过$max位';
    }

    return null;
  }

  /// 数字范围验证
  static String? numberRange(
    String? value, {
    num? min,
    num? max,
    String? message,
  }) {
    if (value == null || value.isEmpty) {
      return null;
    }

    final number = num.tryParse(value);
    if (number == null) {
      return message ?? '请输入有效的数字';
    }

    if (min != null && number < min) {
      return message ?? '数值不能小于$min';
    }

    if (max != null && number > max) {
      return message ?? '数值不能大于$max';
    }

    return null;
  }

  /// 正则表达式验证
  static String? pattern(String? value, RegExp pattern, {String? message}) {
    if (value == null || value.isEmpty) {
      return null;
    }

    if (!pattern.hasMatch(value)) {
      return message ?? '格式不正确';
    }
    return null;
  }

  /// URL验证
  static String? url(String? value, {String? message}) {
    if (value == null || value.isEmpty) {
      return null;
    }

    final urlRegex = RegExp(
      r'^https?://[^\s/$.?#].[^\s]*$',
      caseSensitive: false,
    );
    if (!urlRegex.hasMatch(value)) {
      return message ?? '请输入有效的URL地址';
    }
    return null;
  }

  /// 身份证号验证（中国大陆）
  static String? idCard(String? value, {String? message}) {
    if (value == null || value.isEmpty) {
      return null;
    }

    if (value.length != 18) {
      return message ?? '身份证号码必须为18位';
    }

    // 检查前17位是否为数字
    final prefix = value.substring(0, 17);
    if (!RegExp(r'^\d{17}$').hasMatch(prefix)) {
      return message ?? '身份证号码格式不正确';
    }

    // 检查最后一位校验码
    const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];

    int sum = 0;
    for (int i = 0; i < 17; i++) {
      sum += int.parse(prefix[i]) * weights[i];
    }

    final expectedCheckCode = checkCodes[sum % 11];
    if (value[17].toUpperCase() != expectedCheckCode) {
      return message ?? '身份证号码校验失败';
    }

    return null;
  }

  /// 银行卡号验证
  static String? bankCard(String? value, {String? message}) {
    if (value == null || value.isEmpty) {
      return null;
    }

    // 移除空格和连字符
    final cleaned = value.replaceAll(RegExp(r'[\s-]'), '');

    // 检查长度和数字
    if (cleaned.length < 16 || cleaned.length > 19) {
      return message ?? '银行卡号长度应为16-19位';
    }

    if (!RegExp(r'^\d+$').hasMatch(cleaned)) {
      return message ?? '银行卡号只能包含数字';
    }

    // Luhn算法验证
    if (!_luhnCheck(cleaned)) {
      return message ?? '银行卡号格式不正确';
    }

    return null;
  }

  /// Luhn算法校验
  static bool _luhnCheck(String cardNumber) {
    int sum = 0;
    bool isEven = false;

    for (int i = cardNumber.length - 1; i >= 0; i--) {
      int digit = int.parse(cardNumber[i]);

      if (isEven) {
        digit *= 2;
        if (digit > 9) {
          digit = digit ~/ 10 + digit % 10;
        }
      }

      sum += digit;
      isEven = !isEven;
    }

    return sum % 10 == 0;
  }

  /// 用户名验证
  static String? username(String? value, {String? message}) {
    if (value == null || value.isEmpty) {
      return null;
    }

    // 4-20位，字母、数字、下划线，以字母开头
    final usernameRegex = RegExp(r'^[a-zA-Z][a-zA-Z0-9_]{3,19}$');
    if (!usernameRegex.hasMatch(value)) {
      return message ?? '用户名格式不正确';
    }
    return null;
  }

  /// 中文姓名验证
  static String? chineseName(String? value, {String? message}) {
    if (value == null || value.isEmpty) {
      return null;
    }

    final nameRegex = RegExp(r'^[\u4e00-\u9fa5]{2,8}$');
    if (!nameRegex.hasMatch(value)) {
      return message ?? '请输入有效的中文姓名';
    }
    return null;
  }

  /// 英文姓名验证
  static String? englishName(String? value, {String? message}) {
    if (value == null || value.isEmpty) {
      return null;
    }

    final nameRegex = RegExp(r'^[a-zA-Z\s]{2,50}$');
    if (!nameRegex.hasMatch(value)) {
      return message ?? '请输入有效的英文姓名';
    }
    return null;
  }

  /// 纯数字验证
  static String? numeric(String? value, {String? message}) {
    if (value == null || value.isEmpty) {
      return null;
    }

    if (!RegExp(r'^\d+$').hasMatch(value)) {
      return message ?? '只能输入数字';
    }
    return null;
  }

  /// 纯字母验证
  static String? alpha(String? value, {String? message}) {
    if (value == null || value.isEmpty) {
      return null;
    }

    if (!RegExp(r'^[a-zA-Z]+$').hasMatch(value)) {
      return message ?? '只能输入字母';
    }
    return null;
  }

  /// 字母数字验证
  static String? alphanumeric(String? value, {String? message}) {
    if (value == null || value.isEmpty) {
      return null;
    }

    if (!RegExp(r'^[a-zA-Z0-9]+$').hasMatch(value)) {
      return message ?? '只能输入字母和数字';
    }
    return null;
  }

  /// 组合验证器
  static String? Function(String?) combine(
    List<String? Function(String?)> validators,
  ) {
    return (String? value) {
      for (final validator in validators) {
        final result = validator(value);
        if (result != null) {
          return result;
        }
      }
      return null;
    };
  }

  /// 条件验证器
  static String? Function(String?) when(
    bool Function() condition,
    String? Function(String?) validator,
  ) {
    return (String? value) {
      if (condition()) {
        return validator(value);
      }
      return null;
    };
  }
}
