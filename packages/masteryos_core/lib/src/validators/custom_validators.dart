/// 自定义验证器
class CustomValidators {
  CustomValidators._();

  /// 创建自定义验证器
  static String? Function(String?) custom(
    bool Function(String?) validator,
    String errorMessage,
  ) {
    return (String? value) {
      if (!validator(value)) {
        return errorMessage;
      }
      return null;
    };
  }

  /// 异步验证器基类
  static Future<String?> Function(String?) asyncCustom(
    Future<bool> Function(String?) validator,
    String errorMessage,
  ) {
    return (String? value) async {
      if (!(await validator(value))) {
        return errorMessage;
      }
      return null;
    };
  }

  /// 日期验证器
  static String? date(
    String? value, {
    DateTime? minDate,
    DateTime? maxDate,
    String format = 'yyyy-MM-dd',
    String? message,
  }) {
    if (value == null || value.isEmpty) {
      return null;
    }

    DateTime? date;
    try {
      // 简单的日期解析，实际项目中应该使用更完善的日期解析库
      if (format == 'yyyy-MM-dd') {
        final parts = value.split('-');
        if (parts.length == 3) {
          date = DateTime(
            int.parse(parts[0]),
            int.parse(parts[1]),
            int.parse(parts[2]),
          );
        }
      }
    } catch (e) {
      date = null;
    }

    if (date == null) {
      return message ?? '请输入有效的日期';
    }

    if (minDate != null && date.isBefore(minDate)) {
      return message ?? '日期不能早于${_formatDate(minDate)}';
    }

    if (maxDate != null && date.isAfter(maxDate)) {
      return message ?? '日期不能晚于${_formatDate(maxDate)}';
    }

    return null;
  }

  /// 时间验证器
  static String? time(
    String? value, {
    String format = 'HH:mm',
    String? message,
  }) {
    if (value == null || value.isEmpty) {
      return null;
    }

    if (format == 'HH:mm') {
      if (!RegExp(r'^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$').hasMatch(value)) {
        return message ?? '请输入有效的时间格式（HH:mm）';
      }
    } else if (format == 'HH:mm:ss') {
      if (!RegExp(
        r'^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$',
      ).hasMatch(value)) {
        return message ?? '请输入有效的时间格式（HH:mm:ss）';
      }
    }

    return null;
  }

  /// 年龄验证器
  static String? age(
    String? value, {
    int? minAge,
    int? maxAge,
    String? message,
  }) {
    if (value == null || value.isEmpty) {
      return null;
    }

    final age = int.tryParse(value);
    if (age == null) {
      return message ?? '请输入有效的年龄';
    }

    if (minAge != null && age < minAge) {
      return message ?? '年龄不能小于$minAge岁';
    }

    if (maxAge != null && age > maxAge) {
      return message ?? '年龄不能大于$maxAge岁';
    }

    return null;
  }

  /// 文件大小验证器
  static String? fileSize(
    int? sizeInBytes, {
    int? maxSizeInBytes,
    int? minSizeInBytes,
    String? message,
  }) {
    if (sizeInBytes == null) {
      return null;
    }

    if (minSizeInBytes != null && sizeInBytes < minSizeInBytes) {
      return message ?? '文件大小不能小于${_formatFileSize(minSizeInBytes)}';
    }

    if (maxSizeInBytes != null && sizeInBytes > maxSizeInBytes) {
      return message ?? '文件大小不能超过${_formatFileSize(maxSizeInBytes)}';
    }

    return null;
  }

  /// 文件类型验证器
  static String? fileType(
    String? fileName, {
    List<String>? allowedExtensions,
    List<String>? allowedMimeTypes,
    String? message,
  }) {
    if (fileName == null || fileName.isEmpty) {
      return null;
    }

    if (allowedExtensions != null) {
      final extension = fileName.split('.').last.toLowerCase();
      if (!allowedExtensions.map((e) => e.toLowerCase()).contains(extension)) {
        return message ?? '只允许上传${allowedExtensions.join('、')}格式的文件';
      }
    }

    // MIME类型验证需要额外的文件检测逻辑
    if (allowedMimeTypes != null) {
      // 这里只是示例，实际需要根据文件内容检测MIME类型
      // 可以使用mime包或其他工具
    }

    return null;
  }

  /// 密码复杂度验证器
  static String? passwordComplexity(
    String? value, {
    int minScore = 3,
    String? message,
  }) {
    if (value == null || value.isEmpty) {
      return null;
    }

    int score = 0;

    // 长度评分
    if (value.length >= 8) {
      score += 1;
    }
    if (value.length >= 12) {
      score += 1;
    }

    // 字符类型评分
    if (RegExp(r'[a-z]').hasMatch(value)) {
      score += 1;
    }
    if (RegExp(r'[A-Z]').hasMatch(value)) {
      score += 1;
    }
    if (RegExp(r'\d').hasMatch(value)) {
      score += 1;
    }
    if (RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(value)) {
      score += 1;
    }

    if (score < minScore) {
      return message ?? '密码复杂度太低，请使用更复杂的密码';
    }

    return null;
  }

  /// 黑名单验证器
  static String? blacklist(
    String? value, {
    required List<String> blacklistedValues,
    bool caseSensitive = true,
    String? message,
  }) {
    if (value == null || value.isEmpty) {
      return null;
    }

    final checkValue = caseSensitive ? value : value.toLowerCase();
    final blacklist = caseSensitive
        ? blacklistedValues
        : blacklistedValues.map((v) => v.toLowerCase()).toList();

    if (blacklist.contains(checkValue)) {
      return message ?? '该值不被允许';
    }

    return null;
  }

  /// 白名单验证器
  static String? whitelist(
    String? value, {
    required List<String> allowedValues,
    bool caseSensitive = true,
    String? message,
  }) {
    if (value == null || value.isEmpty) {
      return null;
    }

    final checkValue = caseSensitive ? value : value.toLowerCase();
    final allowlist = caseSensitive
        ? allowedValues
        : allowedValues.map((v) => v.toLowerCase()).toList();

    if (!allowlist.contains(checkValue)) {
      return message ?? '请选择有效的选项';
    }

    return null;
  }

  /// 依赖验证器（基于其他字段的值）
  static String? Function(String?) dependsOn(
    String? otherFieldValue,
    String? Function(String?, String?) validator,
  ) {
    return (String? value) {
      return validator(value, otherFieldValue);
    };
  }

  /// 条件验证器（基于条件的验证）
  static String? Function(String?) conditional(
    bool Function() condition,
    String? Function(String?) validator,
  ) {
    return (String? value) {
      if (condition()) {
        return validator(value);
      }
      return null;
    };
  }

  /// 正则表达式列表验证器
  static String? multiPattern(
    String? value, {
    required List<RegExp> patterns,
    required List<String> messages,
    String? defaultMessage,
  }) {
    if (value == null || value.isEmpty) {
      return null;
    }

    for (int i = 0; i < patterns.length; i++) {
      if (!patterns[i].hasMatch(value)) {
        return i < messages.length ? messages[i] : defaultMessage ?? '格式不正确';
      }
    }

    return null;
  }

  /// 唯一性验证器（需要外部数据源）
  static Future<String?> Function(String?) uniqueness(
    Future<bool> Function(String?) checkUnique,
    String? message,
  ) {
    return (String? value) async {
      if (value == null || value.isEmpty) {
        return null;
      }

      if (!(await checkUnique(value))) {
        return message ?? '该值已存在';
      }

      return null;
    };
  }

  /// 格式化日期显示
  static String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// 格式化文件大小显示
  static String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '${bytes}B';
    }
    if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)}KB';
    }
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }
}

/// 验证结果
class ValidationResult {
  final bool isValid;
  final String? error;
  final Map<String, String>? fieldErrors;

  ValidationResult({required this.isValid, this.error, this.fieldErrors});

  factory ValidationResult.success() {
    return ValidationResult(isValid: true);
  }

  factory ValidationResult.failure(String error) {
    return ValidationResult(isValid: false, error: error);
  }

  factory ValidationResult.fieldErrors(Map<String, String> errors) {
    return ValidationResult(isValid: false, fieldErrors: errors);
  }
}

/// 表单验证器
class FormValidator {
  final Map<String, List<String? Function(String?)>> _validators = {};

  /// 为字段添加验证器
  FormValidator field(
    String fieldName,
    List<String? Function(String?)> validators,
  ) {
    _validators[fieldName] = validators;
    return this;
  }

  /// 验证表单数据
  ValidationResult validate(Map<String, String?> data) {
    final errors = <String, String>{};

    for (final entry in _validators.entries) {
      final fieldName = entry.key;
      final validators = entry.value;
      final value = data[fieldName];

      for (final validator in validators) {
        final error = validator(value);
        if (error != null) {
          errors[fieldName] = error;
          break; // 只报告第一个错误
        }
      }
    }

    if (errors.isEmpty) {
      return ValidationResult.success();
    } else {
      return ValidationResult.fieldErrors(errors);
    }
  }
}
