class ApiEndpoints {
  // 基础配置
  static const String apiVersion = 'v1';

  // 基础路径
  static const String auth = '/auth';
  static const String users = '/users';
  static const String admin = '/admin';
  static const String mobile = '/mobile';
  static const String documents = '/documents';
  static const String analytics = '/analytics';
  static const String health = '/health';
  static const String metrics = '/metrics';

  // 认证相关
  static const String login = '$auth/login';
  static const String logout = '$auth/logout';
  static const String refresh = '$auth/refresh';
  static const String register = '$auth/register';
  static const String forgotPassword = '$auth/forgot-password';
  static const String resetPassword = '$auth/reset-password';
  static const String verifyEmail = '$auth/verify-email';
  static const String profile = '$auth/profile';

  // 管理员用户管理
  static const String adminUsers = '$admin/users';
  static String adminUser(String id) => '$adminUsers/$id';
  static String adminUserStatus(String id) => '$adminUsers/$id/status';
  static String adminUserRole(String id) => '$adminUsers/$id/role';
  static String adminUserPermissions(String id) =>
      '$adminUsers/$id/permissions';

  // 管理员文档管理
  static const String adminDocuments = '$admin/documents';
  static String adminDocument(String id) => '$adminDocuments/$id';
  static String adminDocumentStatus(String id) => '$adminDocuments/$id/status';
  static const String adminDocumentUpload = '$adminDocuments/upload';
  static String adminDocumentDownload(String id) =>
      '$adminDocuments/$id/download';

  // 管理员分析
  static const String adminAnalytics = '$admin/analytics';
  static const String adminAnalyticsOverview = '$adminAnalytics/overview';
  static const String adminAnalyticsUsers = '$adminAnalytics/users';
  static const String adminAnalyticsDocuments = '$adminAnalytics/documents';
  static const String adminAnalyticsEvents = '$adminAnalytics/events';

  // 管理员系统监控
  static const String adminMonitoring = '$admin/monitoring';
  static const String adminHealth = '$adminMonitoring/health';
  static const String adminSystemMetrics = '$adminMonitoring/metrics/system';
  static const String adminAppMetrics = '$adminMonitoring/metrics/application';
  static const String adminDiagnostics = '$adminMonitoring/diagnostics';
  static const String adminLogs = '$adminMonitoring/logs';
  static const String adminAlerts = '$adminMonitoring/alerts';

  // 管理员缓存管理
  static const String adminCache = '$admin/cache';
  static const String adminCacheStats = '$adminCache/stats';
  static const String adminCacheClear = '$adminCache/clear';
  static const String adminCacheKeys = '$adminCache/keys';

  // 管理员性能管理
  static const String adminPerformance = '$admin/performance';
  static const String adminPerformanceDatabase = '$adminPerformance/database';
  static const String adminPerformanceQueries = '$adminPerformance/queries';
  static const String adminPerformanceIndexes = '$adminPerformance/indexes';

  // 移动端用户相关
  static const String mobileProfile = '$mobile/profile';
  static const String mobileDocuments = '$mobile/documents';
  static String mobileDocument(String id) => '$mobileDocuments/$id';
  static String mobileDocumentDownload(String id) =>
      '$mobileDocuments/$id/download';

  // 移动端学习相关
  static const String mobileLearning = '$mobile/learning';
  static const String mobileProgress = '$mobileLearning/progress';
  static const String mobileCourses = '$mobileLearning/courses';
  static String mobileCourse(String id) => '$mobileCourses/$id';

  // 公共健康检查
  static const String healthCheck = '$health';
  static const String healthDatabase = '$health/database';
  static const String healthRedis = '$health/redis';
  static const String healthMonitoring = '$health/monitoring';
  static const String healthMetrics = '$health/metrics';

  // 公共指标
  static const String metricsPrometheus = '$metrics';
  static const String metricsJson = '$metrics/json';
  static const String metricsHealth = '$metrics/health';
  static const String metricsStats = '$metrics/stats';

  // 文件上传下载
  static const String upload = '/upload';
  static const String download = '/download';
  static String fileDownload(String id) => '$download/$id';

  // 构建完整URL
  static String buildUrl(String baseUrl, String endpoint) {
    final base = baseUrl.endsWith('/')
        ? baseUrl.substring(0, baseUrl.length - 1)
        : baseUrl;
    final path = endpoint.startsWith('/') ? endpoint : '/$endpoint';
    return '$base/api/$apiVersion$path';
  }

  // 构建查询参数
  static String buildQuery(String endpoint, Map<String, dynamic>? params) {
    if (params == null || params.isEmpty) {
      return endpoint;
    }

    final query = params.entries
        .where((e) => e.value != null)
        .map(
          (e) =>
              '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value.toString())}',
        )
        .join('&');

    return query.isEmpty ? endpoint : '$endpoint?$query';
  }

  // 构建分页查询参数
  static Map<String, dynamic> buildPaginationParams({
    int page = 1,
    int limit = 20,
    String? sortBy,
    String? sortOrder = 'desc',
    String? search,
    Map<String, dynamic>? filters,
  }) {
    final params = <String, dynamic>{'page': page, 'limit': limit};

    if (sortBy != null) {
      params['sortBy'] = sortBy;
    }
    if (sortOrder != null) {
      params['sortOrder'] = sortOrder;
    }
    if (search != null && search.isNotEmpty) {
      params['search'] = search;
    }

    if (filters != null) {
      filters.forEach((key, value) {
        if (value != null) {
          params['filter[$key]'] = value;
        }
      });
    }

    return params;
  }
}
