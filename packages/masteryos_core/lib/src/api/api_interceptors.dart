import 'package:dio/dio.dart';
import 'package:logger/logger.dart';

/// API请求拦截器
class ApiInterceptors {
  ApiInterceptors._();

  /// 创建日志拦截器
  static Interceptor createLogInterceptor({Logger? logger}) {
    final log = logger ?? Logger();

    return InterceptorsWrapper(
      onRequest: (options, handler) {
        log.d('发送请求: ${options.method} ${options.uri}');
        if (options.data != null) {
          log.d('请求数据: ${options.data}');
        }
        handler.next(options);
      },
      onResponse: (response, handler) {
        log.d('收到响应: ${response.statusCode} ${response.requestOptions.uri}');
        handler.next(response);
      },
      onError: (error, handler) {
        log.e('请求错误: ${error.message}', error: error.error);
        handler.next(error);
      },
    );
  }

  /// 创建认证拦截器
  static Interceptor createAuthInterceptor({
    required String Function() getToken,
    required Future<void> Function() onTokenExpired,
  }) {
    return InterceptorsWrapper(
      onRequest: (options, handler) {
        final token = getToken();
        if (token.isNotEmpty) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        handler.next(options);
      },
      onError: (error, handler) async {
        if (error.response?.statusCode == 401) {
          await onTokenExpired();
        }
        handler.next(error);
      },
    );
  }

  /// 创建错误处理拦截器
  static Interceptor createErrorInterceptor({Logger? logger}) {
    final log = logger ?? Logger();

    return InterceptorsWrapper(
      onError: (error, handler) {
        final response = error.response;
        if (response != null) {
          log.e('HTTP错误: ${response.statusCode} ${response.statusMessage}');
          if (response.data != null) {
            log.e('错误响应: ${response.data}');
          }
        } else {
          log.e('网络错误: ${error.message}');
        }
        handler.next(error);
      },
    );
  }

  /// 创建重试拦截器
  static Interceptor createRetryInterceptor({
    int maxRetries = 3,
    Duration delay = const Duration(seconds: 1),
    List<int> retryStatusCodes = const [500, 502, 503, 504],
    Logger? logger,
  }) {
    final log = logger ?? Logger();

    return InterceptorsWrapper(
      onError: (error, handler) async {
        final response = error.response;
        final requestOptions = error.requestOptions;

        // 检查是否应该重试
        if (response != null &&
            retryStatusCodes.contains(response.statusCode) &&
            requestOptions.extra['retryCount'] != null &&
            (requestOptions.extra['retryCount'] as int) < maxRetries) {
          final retryCount = requestOptions.extra['retryCount'] as int;
          requestOptions.extra['retryCount'] = retryCount + 1;

          log.w('重试请求 (${retryCount + 1}/$maxRetries): ${requestOptions.uri}');

          // 延迟后重试
          await Future<void>.delayed(delay * (retryCount + 1));

          try {
            final dio = Dio();
            final retryResponse = await dio.fetch<dynamic>(requestOptions);
            handler.resolve(retryResponse);
            return;
          } catch (e) {
            // 重试失败，继续处理原始错误
          }
        }

        handler.next(error);
      },
      onRequest: (options, handler) {
        // 初始化重试计数
        options.extra['retryCount'] = 0;
        handler.next(options);
      },
    );
  }

  /// 创建缓存拦截器
  static Interceptor createCacheInterceptor({
    Duration cacheDuration = const Duration(minutes: 5),
    Logger? logger,
  }) {
    final log = logger ?? Logger();
    final cache = <String, _CacheItem>{};

    return InterceptorsWrapper(
      onRequest: (options, handler) {
        // 只缓存GET请求
        if (options.method.toUpperCase() != 'GET') {
          handler.next(options);
          return;
        }

        final cacheKey = '${options.uri}${options.queryParameters}';
        final cachedItem = cache[cacheKey];

        if (cachedItem != null && !cachedItem.isExpired) {
          log.d('使用缓存响应: ${options.uri}');
          handler.resolve(cachedItem.response);
          return;
        }

        handler.next(options);
      },
      onResponse: (response, handler) {
        // 缓存GET请求的响应
        if (response.requestOptions.method.toUpperCase() == 'GET') {
          final cacheKey =
              '${response.requestOptions.uri}${response.requestOptions.queryParameters}';
          cache[cacheKey] = _CacheItem(
            response: response,
            expiredAt: DateTime.now().add(cacheDuration),
          );
          log.d('缓存响应: ${response.requestOptions.uri}');
        }

        handler.next(response);
      },
    );
  }

  /// 创建请求ID拦截器
  static Interceptor createRequestIdInterceptor() {
    return InterceptorsWrapper(
      onRequest: (options, handler) {
        final requestId = DateTime.now().millisecondsSinceEpoch.toString();
        options.headers['X-Request-ID'] = requestId;
        handler.next(options);
      },
    );
  }

  /// 创建用户代理拦截器
  static Interceptor createUserAgentInterceptor(String userAgent) {
    return InterceptorsWrapper(
      onRequest: (options, handler) {
        options.headers['User-Agent'] = userAgent;
        handler.next(options);
      },
    );
  }
}

/// 缓存项
class _CacheItem {
  final Response<dynamic> response;
  final DateTime expiredAt;

  _CacheItem({required this.response, required this.expiredAt});

  bool get isExpired => DateTime.now().isAfter(expiredAt);
}
