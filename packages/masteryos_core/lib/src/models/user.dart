import 'package:json_annotation/json_annotation.dart';

part 'user.g.dart';

enum UserRole {
  @JsonValue('admin')
  admin,
  @<PERSON>sonValue('manager')
  manager,
  @JsonValue('user')
  user,
  @JsonValue('guest')
  guest,
}

enum UserStatus {
  @JsonValue('active')
  active,
  @JsonValue('inactive')
  inactive,
  @JsonValue('suspended')
  suspended,
  @JsonValue('pending')
  pending,
}

@JsonSerializable()
class User {
  final String id;
  final String email;
  final String? name;
  final String? avatar;
  final UserRole role;
  final UserStatus status;
  final bool emailVerified;
  final DateTime? lastLoginAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  // 扩展用户信息
  final String? phone;
  final String? department;
  final String? jobTitle;
  final List<String> permissions;
  final Map<String, dynamic>? metadata;

  const User({
    required this.id,
    required this.email,
    required this.role,
    required this.status,
    required this.emailVerified,
    required this.createdAt,
    required this.updatedAt,
    this.name,
    this.avatar,
    this.lastLoginAt,
    this.phone,
    this.department,
    this.jobTitle,
    this.permissions = const [],
    this.metadata,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);

  // 便利方法
  bool get isActive => status == UserStatus.active;
  bool get isAdmin => role == UserRole.admin;
  bool get isManager => role == UserRole.manager || role == UserRole.admin;

  String get displayName => name ?? email.split('@').first;

  bool hasPermission(String permission) {
    return permissions.contains(permission) || isAdmin;
  }

  User copyWith({
    String? id,
    String? email,
    String? name,
    String? avatar,
    UserRole? role,
    UserStatus? status,
    bool? emailVerified,
    DateTime? lastLoginAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? phone,
    String? department,
    String? jobTitle,
    List<String>? permissions,
    Map<String, dynamic>? metadata,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      avatar: avatar ?? this.avatar,
      role: role ?? this.role,
      status: status ?? this.status,
      emailVerified: emailVerified ?? this.emailVerified,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      phone: phone ?? this.phone,
      department: department ?? this.department,
      jobTitle: jobTitle ?? this.jobTitle,
      permissions: permissions ?? this.permissions,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is User && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'User{id: $id, email: $email, name: $name, role: $role, status: $status}';
  }
}
