import 'package:json_annotation/json_annotation.dart';

part 'pagination.g.dart';

@JsonSerializable(genericArgumentFactories: true)
class PaginatedResponse<T> {
  final List<T> data;
  final PaginationMeta meta;

  const PaginatedResponse({required this.data, required this.meta});

  factory PaginatedResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) => _$PaginatedResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$PaginatedResponseToJson(this, toJsonT);

  bool get hasNextPage => meta.hasNextPage;
  bool get hasPreviousPage => meta.hasPreviousPage;
  int get totalPages => meta.totalPages;
  int get currentPage => meta.currentPage;
  int get totalItems => meta.total;
}

@JsonSerializable()
class PaginationMeta {
  final int total;
  final int page;
  final int limit;
  final int totalPages;
  final bool hasNextPage;
  final bool hasPreviousPage;
  final int? nextPage;
  final int? previousPage;

  const PaginationMeta({
    required this.total,
    required this.page,
    required this.limit,
    required this.totalPages,
    required this.hasNextPage,
    required this.hasPreviousPage,
    this.nextPage,
    this.previousPage,
  });

  factory PaginationMeta.fromJson(Map<String, dynamic> json) =>
      _$PaginationMetaFromJson(json);

  Map<String, dynamic> toJson() => _$PaginationMetaToJson(this);

  // 便利构造器
  factory PaginationMeta.create({
    required int total,
    required int page,
    required int limit,
  }) {
    final totalPages = (total / limit).ceil();
    final hasNextPage = page < totalPages;
    final hasPreviousPage = page > 1;
    final nextPage = hasNextPage ? page + 1 : null;
    final previousPage = hasPreviousPage ? page - 1 : null;

    return PaginationMeta(
      total: total,
      page: page,
      limit: limit,
      totalPages: totalPages,
      hasNextPage: hasNextPage,
      hasPreviousPage: hasPreviousPage,
      nextPage: nextPage,
      previousPage: previousPage,
    );
  }

  int get currentPage => page;
  int get offset => (page - 1) * limit;

  String get pageInfo => '$page / $totalPages';
  String get itemsInfo {
    final start = offset + 1;
    final end = (offset + limit > total) ? total : offset + limit;
    return '$start-$end of $total';
  }
}

// 分页查询参数
@JsonSerializable()
class PaginationQuery {
  final int page;
  final int limit;
  final String? sortBy;
  final String? sortOrder;
  final String? search;
  final Map<String, dynamic>? filters;

  const PaginationQuery({
    this.page = 1,
    this.limit = 20,
    this.sortBy,
    this.sortOrder = 'desc',
    this.search,
    this.filters,
  });

  factory PaginationQuery.fromJson(Map<String, dynamic> json) =>
      _$PaginationQueryFromJson(json);

  Map<String, dynamic> toJson() => _$PaginationQueryToJson(this);

  int get offset => (page - 1) * limit;

  bool get hasSearch => search != null && search!.isNotEmpty;
  bool get hasFilters => filters != null && filters!.isNotEmpty;
  bool get hasSorting => sortBy != null && sortBy!.isNotEmpty;

  PaginationQuery copyWith({
    int? page,
    int? limit,
    String? sortBy,
    String? sortOrder,
    String? search,
    Map<String, dynamic>? filters,
  }) {
    return PaginationQuery(
      page: page ?? this.page,
      limit: limit ?? this.limit,
      sortBy: sortBy ?? this.sortBy,
      sortOrder: sortOrder ?? this.sortOrder,
      search: search ?? this.search,
      filters: filters ?? this.filters,
    );
  }

  // 转换为URL查询参数
  Map<String, String> toQueryParams() {
    final params = <String, String>{
      'page': page.toString(),
      'limit': limit.toString(),
    };

    if (sortBy != null) {
      params['sortBy'] = sortBy!;
    }
    if (sortOrder != null) {
      params['sortOrder'] = sortOrder!;
    }
    if (search != null) {
      params['search'] = search!;
    }

    if (filters != null) {
      filters!.forEach((key, value) {
        if (value != null) {
          params['filter[$key]'] = value.toString();
        }
      });
    }

    return params;
  }
}
