import 'package:json_annotation/json_annotation.dart';

import '../user.dart';

part 'user_models.g.dart';

/// 用户列表响应模型
@JsonSerializable()
class UserListResponse {
  final List<User> users;
  final PaginationMeta pagination;

  const UserListResponse({required this.users, required this.pagination});

  factory UserListResponse.fromJson(Map<String, dynamic> json) =>
      _$UserListResponseFromJson(json);

  Map<String, dynamic> toJson() => _$UserListResponseToJson(this);
}

/// 分页元数据
@JsonSerializable()
class PaginationMeta {
  final int currentPage;
  final int totalPages;
  final int totalItems;
  final int itemsPerPage;
  final bool hasNextPage;
  final bool hasPreviousPage;

  const PaginationMeta({
    required this.currentPage,
    required this.totalPages,
    required this.totalItems,
    required this.itemsPerPage,
    required this.hasNextPage,
    required this.hasPreviousPage,
  });

  factory PaginationMeta.fromJson(Map<String, dynamic> json) =>
      _$PaginationMetaFromJson(json);

  Map<String, dynamic> toJson() => _$PaginationMetaToJson(this);
}

/// 用户统计数据
@JsonSerializable()
class UserStats {
  final int totalUsers;
  final int activeUsers;
  final int inactiveUsers;
  final int suspendedUsers;
  final int adminUsers;
  final int regularUsers;
  final Map<UserRole, int> usersByRole;
  final Map<UserStatus, int> usersByStatus;

  const UserStats({
    required this.totalUsers,
    required this.activeUsers,
    required this.inactiveUsers,
    required this.suspendedUsers,
    required this.adminUsers,
    required this.regularUsers,
    required this.usersByRole,
    required this.usersByStatus,
  });

  factory UserStats.fromJson(Map<String, dynamic> json) =>
      _$UserStatsFromJson(json);

  Map<String, dynamic> toJson() => _$UserStatsToJson(this);

  /// 计算用户增长率
  double calculateGrowthRate(UserStats previousStats) {
    if (previousStats.totalUsers == 0) {
      return 0.0;
    }
    return ((totalUsers - previousStats.totalUsers) /
            previousStats.totalUsers) *
        100;
  }
}

/// 用户查询参数
@JsonSerializable()
class UserQueryParams {
  final int? page;
  final int? limit;
  final String? search;
  final UserRole? role;
  final UserStatus? status;
  final String? sortBy;
  final String? sortOrder;
  final DateTime? createdAfter;
  final DateTime? createdBefore;

  const UserQueryParams({
    this.page,
    this.limit,
    this.search,
    this.role,
    this.status,
    this.sortBy,
    this.sortOrder,
    this.createdAfter,
    this.createdBefore,
  });

  factory UserQueryParams.fromJson(Map<String, dynamic> json) =>
      _$UserQueryParamsFromJson(json);

  Map<String, dynamic> toJson() => _$UserQueryParamsToJson(this);

  /// 创建查询字符串
  Map<String, String> toQueryString() {
    final Map<String, String> params = {};

    if (page != null) {
      params['page'] = page.toString();
    }
    if (limit != null) {
      params['limit'] = limit.toString();
    }
    if (search != null && search!.isNotEmpty) {
      params['search'] = search!;
    }
    if (role != null) {
      params['role'] = role!.name;
    }
    if (status != null) {
      params['status'] = status!.name;
    }
    if (sortBy != null) {
      params['sortBy'] = sortBy!;
    }
    if (sortOrder != null) {
      params['sortOrder'] = sortOrder!;
    }
    if (createdAfter != null) {
      params['createdAfter'] = createdAfter!.toIso8601String();
    }
    if (createdBefore != null) {
      params['createdBefore'] = createdBefore!.toIso8601String();
    }

    return params;
  }
}

/// 用户创建请求
@JsonSerializable()
class CreateUserRequest {
  final String email;
  final String name;
  final String? password;
  final UserRole role;
  final UserStatus status;
  final String? avatar;
  final Map<String, dynamic>? preferences;

  const CreateUserRequest({
    required this.email,
    required this.name,
    this.password,
    this.role = UserRole.user,
    this.status = UserStatus.active,
    this.avatar,
    this.preferences,
  });

  factory CreateUserRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateUserRequestFromJson(json);

  Map<String, dynamic> toJson() => _$CreateUserRequestToJson(this);
}

/// 用户更新请求
@JsonSerializable()
class UpdateUserRequest {
  final String? email;
  final String? name;
  final String? password;
  final UserRole? role;
  final UserStatus? status;
  final String? avatar;
  final Map<String, dynamic>? preferences;

  const UpdateUserRequest({
    this.email,
    this.name,
    this.password,
    this.role,
    this.status,
    this.avatar,
    this.preferences,
  });

  factory UpdateUserRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateUserRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UpdateUserRequestToJson(this);
}

/// 批量用户操作请求
@JsonSerializable()
class BulkUserOperationRequest {
  final List<String> userIds;
  final BulkUserOperation operation;
  final Map<String, dynamic>? params;

  const BulkUserOperationRequest({
    required this.userIds,
    required this.operation,
    this.params,
  });

  factory BulkUserOperationRequest.fromJson(Map<String, dynamic> json) =>
      _$BulkUserOperationRequestFromJson(json);

  Map<String, dynamic> toJson() => _$BulkUserOperationRequestToJson(this);
}

/// 批量用户操作类型
enum BulkUserOperation {
  delete,
  activate,
  deactivate,
  suspend,
  changeRole,
  export,
}

/// 用户活动统计
@JsonSerializable()
class UserActivityStats {
  final String userId;
  final int loginCount;
  final DateTime? lastLoginAt;
  final int sessionCount;
  final Duration averageSessionDuration;
  final int actionsCount;
  final Map<String, int> actionsByType;

  const UserActivityStats({
    required this.userId,
    required this.loginCount,
    required this.sessionCount,
    required this.averageSessionDuration,
    required this.actionsCount,
    required this.actionsByType,
    this.lastLoginAt,
  });

  factory UserActivityStats.fromJson(Map<String, dynamic> json) =>
      _$UserActivityStatsFromJson(json);

  Map<String, dynamic> toJson() => _$UserActivityStatsToJson(this);
}

/// 用户权限信息
@JsonSerializable()
class UserPermissions {
  final String userId;
  final UserRole role;
  final List<String> permissions;
  final List<String> roles;
  final Map<String, dynamic> customPermissions;

  const UserPermissions({
    required this.userId,
    required this.role,
    required this.permissions,
    required this.roles,
    required this.customPermissions,
  });

  factory UserPermissions.fromJson(Map<String, dynamic> json) =>
      _$UserPermissionsFromJson(json);

  Map<String, dynamic> toJson() => _$UserPermissionsToJson(this);

  /// 检查是否有特定权限
  bool hasPermission(String permission) {
    return permissions.contains(permission) || role == UserRole.admin;
  }

  /// 检查是否有任意一个权限
  bool hasAnyPermission(List<String> requiredPermissions) {
    return requiredPermissions.any((permission) => hasPermission(permission));
  }

  /// 检查是否有所有权限
  bool hasAllPermissions(List<String> requiredPermissions) {
    return requiredPermissions.every((permission) => hasPermission(permission));
  }
}
