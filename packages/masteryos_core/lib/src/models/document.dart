import 'package:json_annotation/json_annotation.dart';

part 'document.g.dart';

enum DocumentType {
  @JsonValue('pdf')
  pdf,
  @JsonValue('doc')
  doc,
  @JsonValue('docx')
  docx,
  @JsonValue('txt')
  txt,
  @JsonValue('md')
  md,
  @JsonValue('image')
  image,
  @JsonValue('video')
  video,
  @JsonValue('audio')
  audio,
  @JsonValue('other')
  other,
}

enum DocumentStatus {
  @JsonValue('draft')
  draft,
  @JsonValue('published')
  published,
  @JsonValue('archived')
  archived,
  @JsonValue('deleted')
  deleted,
}

enum AccessLevel {
  @JsonValue('public')
  public,
  @JsonValue('internal')
  internal,
  @JsonValue('private')
  private,
  @JsonValue('restricted')
  restricted,
}

@JsonSerializable()
class Document {
  final String id;
  final String title;
  final String? description;
  final String fileName;
  final String filePath;
  final DocumentType type;
  final DocumentStatus status;
  final AccessLevel accessLevel;
  final int fileSize;
  final String? mimeType;
  final String uploadedBy;
  final DateTime uploadedAt;
  final DateTime updatedAt;

  // 扩展信息
  final List<String> tags;
  final String? category;
  final Map<String, dynamic>? metadata;
  final int downloadCount;
  final int viewCount;
  final DateTime? lastAccessedAt;

  const Document({
    required this.id,
    required this.title,
    required this.fileName,
    required this.filePath,
    required this.type,
    required this.status,
    required this.accessLevel,
    required this.fileSize,
    required this.uploadedBy,
    required this.uploadedAt,
    required this.updatedAt,
    this.description,
    this.mimeType,
    this.tags = const [],
    this.category,
    this.metadata,
    this.downloadCount = 0,
    this.viewCount = 0,
    this.lastAccessedAt,
  });

  factory Document.fromJson(Map<String, dynamic> json) =>
      _$DocumentFromJson(json);
  Map<String, dynamic> toJson() => _$DocumentToJson(this);

  // 便利方法
  bool get isPublished => status == DocumentStatus.published;
  bool get isPublic => accessLevel == AccessLevel.public;
  bool get isImage => type == DocumentType.image;
  bool get isPdf => type == DocumentType.pdf;

  String get fileExtension {
    return fileName.split('.').last.toLowerCase();
  }

  String get formattedFileSize {
    if (fileSize < 1024) {
      return '${fileSize}B';
    }
    if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)}KB';
    }
    if (fileSize < 1024 * 1024 * 1024) {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
    return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  bool hasTag(String tag) {
    return tags.contains(tag);
  }

  Document copyWith({
    String? id,
    String? title,
    String? description,
    String? fileName,
    String? filePath,
    DocumentType? type,
    DocumentStatus? status,
    AccessLevel? accessLevel,
    int? fileSize,
    String? mimeType,
    String? uploadedBy,
    DateTime? uploadedAt,
    DateTime? updatedAt,
    List<String>? tags,
    String? category,
    Map<String, dynamic>? metadata,
    int? downloadCount,
    int? viewCount,
    DateTime? lastAccessedAt,
  }) {
    return Document(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      fileName: fileName ?? this.fileName,
      filePath: filePath ?? this.filePath,
      type: type ?? this.type,
      status: status ?? this.status,
      accessLevel: accessLevel ?? this.accessLevel,
      fileSize: fileSize ?? this.fileSize,
      mimeType: mimeType ?? this.mimeType,
      uploadedBy: uploadedBy ?? this.uploadedBy,
      uploadedAt: uploadedAt ?? this.uploadedAt,
      updatedAt: updatedAt ?? this.updatedAt,
      tags: tags ?? this.tags,
      category: category ?? this.category,
      metadata: metadata ?? this.metadata,
      downloadCount: downloadCount ?? this.downloadCount,
      viewCount: viewCount ?? this.viewCount,
      lastAccessedAt: lastAccessedAt ?? this.lastAccessedAt,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Document && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Document{id: $id, title: $title, fileName: $fileName, type: $type, status: $status}';
  }
}
