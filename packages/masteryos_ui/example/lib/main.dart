import 'package:flutter/material.dart';
import 'package:masteryos_ui/masteryos_ui.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'MasteryOS UI Demo',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      home: const DemoHomePage(),
    );
  }
}

class DemoHomePage extends StatefulWidget {
  const DemoHomePage({super.key});

  @override
  State<DemoHomePage> createState() => _DemoHomePageState();
}

class _DemoHomePageState extends State<DemoHomePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('MasteryOS UI Demo'),
        actions: [
          IconButton(
            icon: const Icon(Icons.brightness_6),
            onPressed: () {
              // 主题切换逻辑
            },
          ),
        ],
      ),
      body: ResponsiveLayout(
        mobile: _buildMobileLayout(),
        tablet: _buildTabletLayout(),
        desktop: _buildDesktopLayout(),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return const SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          _ComponentShowcase(),
        ],
      ),
    );
  }

  Widget _buildTabletLayout() {
    return const SingleChildScrollView(
      padding: EdgeInsets.all(24),
      child: Column(
        children: [
          _ComponentShowcase(),
        ],
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return const SingleChildScrollView(
      padding: EdgeInsets.all(32),
      child: Column(
        children: [
          _ComponentShowcase(),
        ],
      ),
    );
  }
}

class _ComponentShowcase extends StatefulWidget {
  const _ComponentShowcase();

  @override
  State<_ComponentShowcase> createState() => _ComponentShowcaseState();
}

class _ComponentShowcaseState extends State<_ComponentShowcase> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  String? _selectedRole = 'user';
  bool _isLoading = false;
  int _currentPage = 1;
  final int _totalPages = 5;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 统计卡片展示
        _buildStatsSection(),
        const SizedBox(height: 32),

        // 表单组件展示
        _buildFormSection(),
        const SizedBox(height: 32),

        // 数据展示组件
        _buildDataSection(),
        const SizedBox(height: 32),

        // 分页组件展示
        _buildPaginationSection(),
        const SizedBox(height: 32),

        // 空状态展示
        _buildEmptyStateSection(),
      ],
    );
  }

  Widget _buildStatsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '统计卡片',
          style: context.headlineMedium,
        ),
        const SizedBox(height: 16),
        StatCardGrid(
          cards: [
            const TrendingStatCard(
              title: '总用户数',
              value: '1,234',
              changePercentage: 12.5,
              icon: Icons.people,
            ),
            const TrendingStatCard(
              title: '活跃用户',
              value: '987',
              changePercentage: -3.2,
              icon: Icons.trending_up,
            ),
            const SimpleStatCard(
              title: '新注册',
              value: '56',
              icon: Icons.person_add,
              color: Colors.green,
            ),
            const SimpleStatCard(
              title: '文档数量',
              value: '2,468',
              icon: Icons.description,
              color: Colors.blue,
            ),
          ],
          crossAxisCount: context.isMobile ? 2 : 4,
        ),
      ],
    );
  }

  Widget _buildFormSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '表单组件',
          style: context.headlineMedium,
        ),
        const SizedBox(height: 16),
        Form(
          key: _formKey,
          child: Column(
            children: [
              EmailTextField(
                controller: _emailController,
                isRequired: true,
              ),
              const SizedBox(height: 16),
              PasswordTextField(
                controller: _passwordController,
                isRequired: true,
              ),
              const SizedBox(height: 16),
              SimpleDropdown<String>(
                items: const ['admin', 'user', 'guest'],
                itemLabelBuilder: (value) => value.toUpperCase(),
                value: _selectedRole,
                onChanged: (value) {
                  setState(() {
                    _selectedRole = value;
                  });
                },
                labelText: '用户角色',
                isRequired: true,
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: AppButton(
                      onPressed: _isLoading ? null : _handleSubmit,
                      isLoading: _isLoading,
                      child: const Text('提交'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: AppButton(
                      onPressed: _handleReset,
                      variant: AppButtonVariant.outlined,
                      child: const Text('重置'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDataSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '数据表格',
          style: context.headlineMedium,
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: context.colorScheme.outline),
            borderRadius: BorderRadius.circular(8),
          ),
          child: AppDataTable<Map<String, dynamic>>(
            columns: const [
              AppDataColumn(label: '姓名', sortable: true),
              AppDataColumn(label: '邮箱', sortable: true),
              AppDataColumn(label: '角色'),
              AppDataColumn(label: '状态'),
              AppDataColumn(label: '操作'),
            ],
            rows: [
              AppDataRow(
                data: {'id': '1', 'name': '张三'},
                cells: [
                  const AppDataCell(child: Text('张三')),
                  const AppDataCell(child: Text('<EMAIL>')),
                  const AppDataCell(child: Text('管理员')),
                  AppDataCell(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: const Text('活跃',
                          style: TextStyle(color: Colors.green)),
                    ),
                  ),
                  AppDataCell(
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.edit, size: 16),
                          onPressed: () {},
                        ),
                        IconButton(
                          icon: const Icon(Icons.delete, size: 16),
                          onPressed: () {},
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              AppDataRow(
                data: {'id': '2', 'name': '李四'},
                cells: [
                  const AppDataCell(child: Text('李四')),
                  const AppDataCell(child: Text('<EMAIL>')),
                  const AppDataCell(child: Text('用户')),
                  AppDataCell(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.orange.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: const Text('未激活',
                          style: TextStyle(color: Colors.orange)),
                    ),
                  ),
                  AppDataCell(
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.edit, size: 16),
                          onPressed: () {},
                        ),
                        IconButton(
                          icon: const Icon(Icons.delete, size: 16),
                          onPressed: () {},
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPaginationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '分页组件',
          style: context.headlineMedium,
        ),
        const SizedBox(height: 16),
        Pagination(
          currentPage: _currentPage,
          totalPages: _totalPages,
          totalItems: 100,
          itemsPerPage: 10,
          onPageChanged: (page) {
            setState(() {
              _currentPage = page;
            });
          },
        ),
      ],
    );
  }

  Widget _buildEmptyStateSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '空状态组件',
          style: context.headlineMedium,
        ),
        const SizedBox(height: 16),
        Container(
          height: 200,
          decoration: BoxDecoration(
            border: Border.all(color: context.colorScheme.outline),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const NoDataState(),
        ),
      ],
    );
  }

  void _handleSubmit() async {
    if (_formKey.currentState?.validate() ?? false) {
      setState(() {
        _isLoading = true;
      });

      // 模拟API调用
      await Future<void>.delayed(const Duration(seconds: 2));

      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        context.showSuccessSnackBar('提交成功！');
      }
    }
  }

  void _handleReset() {
    _emailController.clear();
    _passwordController.clear();
    setState(() {
      _selectedRole = 'user';
    });
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }
}
