import 'package:flutter/material.dart';

import '../../theme/theme_constants.dart';

/// 应用通用下拉选择框组件
class AppDropdown<T> extends StatelessWidget {
  const AppDropdown({
    required this.items,
    super.key,
    this.value,
    this.onChanged,
    this.validator,
    this.decoration,
    this.hint,
    this.labelText,
    this.hintText,
    this.helperText,
    this.errorText,
    this.prefixIcon,
    this.suffixIcon,
    this.isRequired = false,
    this.enabled = true,
    this.borderRadius,
    this.fillColor,
    this.size = AppDropdownSize.medium,
    this.itemBuilder,
    this.displayStringForOption,
  });

  /// 下拉选项
  final List<DropdownMenuItem<T>> items;

  /// 当前值
  final T? value;

  /// 值变化回调
  final ValueChanged<T?>? onChanged;

  /// 验证器
  final FormFieldValidator<T>? validator;

  /// 输入装饰
  final InputDecoration? decoration;

  /// 提示组件
  final Widget? hint;

  /// 标签文本
  final String? labelText;

  /// 提示文本
  final String? hintText;

  /// 帮助文本
  final String? helperText;

  /// 错误文本
  final String? errorText;

  /// 前缀图标
  final Widget? prefixIcon;

  /// 后缀图标
  final Widget? suffixIcon;

  /// 是否必填
  final bool isRequired;

  /// 是否启用
  final bool enabled;

  /// 边框圆角
  final BorderRadius? borderRadius;

  /// 填充颜色
  final Color? fillColor;

  /// 下拉框尺寸
  final AppDropdownSize size;

  /// 自定义选项构建器
  final Widget Function(BuildContext, T)? itemBuilder;

  /// 选项显示字符串转换器
  final String Function(T)? displayStringForOption;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    final decoration = _buildInputDecoration(context, colorScheme);

    return DropdownButtonFormField<T>(
      value: value,
      items: items,
      onChanged: enabled ? onChanged : null,
      validator: validator,
      decoration: decoration,
      hint: hint ?? (hintText != null ? Text(hintText!) : null),
      style: _getTextStyle(context),
      icon: suffixIcon ?? Icon(Icons.keyboard_arrow_down, size: _getIconSize()),
      iconSize: _getIconSize(),
      isExpanded: true,
      menuMaxHeight: 300,
    );
  }

  InputDecoration _buildInputDecoration(
      BuildContext context, ColorScheme colorScheme) {
    final baseDecoration = decoration ?? const InputDecoration();
    final borderRadius =
        this.borderRadius ?? BorderRadius.circular(ThemeConstants.radiusMD);

    // 构建标签文本
    String? labelText = this.labelText;
    if (isRequired && labelText != null) {
      labelText = '$labelText *';
    }

    return baseDecoration.copyWith(
      labelText: labelText,
      helperText: helperText,
      errorText: errorText,
      prefixIcon: prefixIcon,
      filled: true,
      fillColor: fillColor ?? colorScheme.surface,
      contentPadding: _getPadding(),
      border: OutlineInputBorder(
        borderRadius: borderRadius,
        borderSide: BorderSide(color: colorScheme.outline),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: borderRadius,
        borderSide: BorderSide(color: colorScheme.outline),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: borderRadius,
        borderSide: BorderSide(color: colorScheme.primary, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: borderRadius,
        borderSide: BorderSide(color: colorScheme.error),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: borderRadius,
        borderSide: BorderSide(color: colorScheme.error, width: 2),
      ),
      disabledBorder: OutlineInputBorder(
        borderRadius: borderRadius,
        borderSide:
            BorderSide(color: colorScheme.outline.withValues(alpha: 0.5)),
      ),
    );
  }

  EdgeInsets _getPadding() {
    switch (size) {
      case AppDropdownSize.small:
        return const EdgeInsets.symmetric(
          horizontal: ThemeConstants.spaceMD,
          vertical: ThemeConstants.spaceXS,
        );
      case AppDropdownSize.medium:
        return const EdgeInsets.symmetric(
          horizontal: ThemeConstants.spaceLG,
          vertical: ThemeConstants.spaceMD,
        );
      case AppDropdownSize.large:
        return const EdgeInsets.symmetric(
          horizontal: ThemeConstants.spaceXL,
          vertical: ThemeConstants.spaceLG,
        );
    }
  }

  TextStyle _getTextStyle(BuildContext context) {
    final baseStyle = Theme.of(context).textTheme.bodyMedium!;

    switch (size) {
      case AppDropdownSize.small:
        return baseStyle.copyWith(fontSize: ThemeConstants.fontSizeXS);
      case AppDropdownSize.medium:
        return baseStyle.copyWith(fontSize: ThemeConstants.fontSizeSM);
      case AppDropdownSize.large:
        return baseStyle.copyWith(fontSize: ThemeConstants.fontSizeMD);
    }
  }

  double _getIconSize() {
    switch (size) {
      case AppDropdownSize.small:
        return 16;
      case AppDropdownSize.medium:
        return 20;
      case AppDropdownSize.large:
        return 24;
    }
  }
}

/// 下拉框尺寸枚举
enum AppDropdownSize {
  small,
  medium,
  large,
}

/// 简化的下拉选择框构建器
class SimpleDropdown<T> extends StatelessWidget {
  const SimpleDropdown({
    required this.items,
    required this.itemLabelBuilder,
    super.key,
    this.value,
    this.onChanged,
    this.validator,
    this.labelText,
    this.hintText,
    this.helperText,
    this.prefixIcon,
    this.isRequired = false,
    this.enabled = true,
    this.size = AppDropdownSize.medium,
  });

  /// 选项数据
  final List<T> items;

  /// 选项标签构建器
  final String Function(T) itemLabelBuilder;

  /// 当前值
  final T? value;

  /// 值变化回调
  final ValueChanged<T?>? onChanged;

  /// 验证器
  final FormFieldValidator<T>? validator;

  /// 标签文本
  final String? labelText;

  /// 提示文本
  final String? hintText;

  /// 帮助文本
  final String? helperText;

  /// 前缀图标
  final Widget? prefixIcon;

  /// 是否必填
  final bool isRequired;

  /// 是否启用
  final bool enabled;

  /// 下拉框尺寸
  final AppDropdownSize size;

  @override
  Widget build(BuildContext context) {
    final dropdownItems = items.map<DropdownMenuItem<T>>((T item) {
      return DropdownMenuItem<T>(
        value: item,
        child: Text(itemLabelBuilder(item)),
      );
    }).toList();

    return AppDropdown<T>(
      items: dropdownItems,
      value: value,
      onChanged: onChanged,
      validator: validator,
      labelText: labelText,
      hintText: hintText,
      helperText: helperText,
      prefixIcon: prefixIcon,
      isRequired: isRequired,
      enabled: enabled,
      size: size,
    );
  }
}

/// 枚举下拉选择框
class EnumDropdown<T extends Enum> extends StatelessWidget {
  const EnumDropdown({
    required this.enumValues,
    required this.enumLabelBuilder,
    super.key,
    this.value,
    this.onChanged,
    this.validator,
    this.labelText,
    this.hintText,
    this.helperText,
    this.prefixIcon,
    this.isRequired = false,
    this.enabled = true,
    this.size = AppDropdownSize.medium,
  });

  /// 枚举值列表
  final List<T> enumValues;

  /// 枚举标签构建器
  final String Function(T) enumLabelBuilder;

  /// 当前值
  final T? value;

  /// 值变化回调
  final ValueChanged<T?>? onChanged;

  /// 验证器
  final FormFieldValidator<T>? validator;

  /// 标签文本
  final String? labelText;

  /// 提示文本
  final String? hintText;

  /// 帮助文本
  final String? helperText;

  /// 前缀图标
  final Widget? prefixIcon;

  /// 是否必填
  final bool isRequired;

  /// 是否启用
  final bool enabled;

  /// 下拉框尺寸
  final AppDropdownSize size;

  @override
  Widget build(BuildContext context) {
    return SimpleDropdown<T>(
      items: enumValues,
      itemLabelBuilder: enumLabelBuilder,
      value: value,
      onChanged: onChanged,
      validator: validator,
      labelText: labelText,
      hintText: hintText,
      helperText: helperText,
      prefixIcon: prefixIcon,
      isRequired: isRequired,
      enabled: enabled,
      size: size,
    );
  }
}

/// 可搜索的下拉选择框
class SearchableDropdown<T> extends StatefulWidget {
  const SearchableDropdown({
    required this.items,
    required this.itemLabelBuilder,
    super.key,
    this.value,
    this.onChanged,
    this.validator,
    this.labelText,
    this.hintText,
    this.searchHintText = '搜索...',
    this.helperText,
    this.prefixIcon,
    this.isRequired = false,
    this.enabled = true,
    this.size = AppDropdownSize.medium,
    this.searchFilter,
  });

  /// 选项数据
  final List<T> items;

  /// 选项标签构建器
  final String Function(T) itemLabelBuilder;

  /// 当前值
  final T? value;

  /// 值变化回调
  final ValueChanged<T?>? onChanged;

  /// 验证器
  final FormFieldValidator<T>? validator;

  /// 标签文本
  final String? labelText;

  /// 提示文本
  final String? hintText;

  /// 搜索提示文本
  final String searchHintText;

  /// 帮助文本
  final String? helperText;

  /// 前缀图标
  final Widget? prefixIcon;

  /// 是否必填
  final bool isRequired;

  /// 是否启用
  final bool enabled;

  /// 下拉框尺寸
  final AppDropdownSize size;

  /// 自定义搜索过滤器
  final bool Function(T item, String searchTerm)? searchFilter;

  @override
  State<SearchableDropdown<T>> createState() => _SearchableDropdownState<T>();
}

class _SearchableDropdownState<T> extends State<SearchableDropdown<T>> {
  final TextEditingController _searchController = TextEditingController();
  List<T> _filteredItems = [];
  final bool _isDropdownOpen = false;

  @override
  void initState() {
    super.initState();
    _filteredItems = widget.items;
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    final searchTerm = _searchController.text.toLowerCase();
    setState(() {
      if (searchTerm.isEmpty) {
        _filteredItems = widget.items;
      } else {
        _filteredItems = widget.items.where((item) {
          if (widget.searchFilter != null) {
            return widget.searchFilter!(item, searchTerm);
          }
          return widget
              .itemLabelBuilder(item)
              .toLowerCase()
              .contains(searchTerm);
        }).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.labelText != null) ...[
          Text(
            widget.isRequired ? '${widget.labelText} *' : widget.labelText!,
            style: Theme.of(context).textTheme.labelMedium,
          ),
          const SizedBox(height: 8),
        ],

        // 搜索框
        if (_isDropdownOpen) ...[
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: widget.searchHintText,
              prefixIcon: const Icon(Icons.search),
              suffixIcon: IconButton(
                icon: const Icon(Icons.clear),
                onPressed: () {
                  _searchController.clear();
                },
              ),
            ),
          ),
          const SizedBox(height: 8),
        ],

        // 下拉选择框
        SimpleDropdown<T>(
          items: _filteredItems,
          itemLabelBuilder: widget.itemLabelBuilder,
          value: widget.value,
          onChanged: widget.onChanged,
          validator: widget.validator,
          hintText: widget.hintText,
          helperText: widget.helperText,
          prefixIcon: widget.prefixIcon,
          isRequired: false, // 已在上面显示了必填标记
          enabled: widget.enabled,
          size: widget.size,
        ),
      ],
    );
  }
}
