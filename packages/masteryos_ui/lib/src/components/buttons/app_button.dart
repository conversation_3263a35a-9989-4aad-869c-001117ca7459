import 'package:flutter/material.dart';

import '../../theme/theme_constants.dart';

/// 应用通用按钮组件
class AppButton extends StatelessWidget {
  const AppButton({
    required this.onPressed,
    required this.child,
    super.key,
    this.variant = AppButtonVariant.filled,
    this.size = AppButtonSize.medium,
    this.color,
    this.backgroundColor,
    this.foregroundColor,
    this.borderColor,
    this.elevation,
    this.padding,
    this.margin,
    this.borderRadius,
    this.isLoading = false,
    this.isDisabled = false,
    this.width,
    this.height,
    this.iconSpacing = 8.0,
    this.loadingWidget,
  });

  /// 按钮点击回调
  final VoidCallback? onPressed;

  /// 按钮子组件
  final Widget child;

  /// 按钮变体
  final AppButtonVariant variant;

  /// 按钮尺寸
  final AppButtonSize size;

  /// 按钮颜色
  final Color? color;

  /// 背景颜色
  final Color? backgroundColor;

  /// 前景颜色
  final Color? foregroundColor;

  /// 边框颜色
  final Color? borderColor;

  /// 阴影高度
  final double? elevation;

  /// 内边距
  final EdgeInsets? padding;

  /// 外边距
  final EdgeInsets? margin;

  /// 圆角半径
  final BorderRadius? borderRadius;

  /// 是否加载中
  final bool isLoading;

  /// 是否禁用
  final bool isDisabled;

  /// 宽度
  final double? width;

  /// 高度
  final double? height;

  /// 图标间距
  final double iconSpacing;

  /// 自定义加载组件
  final Widget? loadingWidget;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    final buttonStyle = _getButtonStyle(context, colorScheme);
    final buttonChild = _buildChild(context);

    Widget button;

    switch (variant) {
      case AppButtonVariant.filled:
        button = ElevatedButton(
          onPressed: _getOnPressed(),
          style: buttonStyle,
          child: buttonChild,
        );
        break;
      case AppButtonVariant.outlined:
        button = OutlinedButton(
          onPressed: _getOnPressed(),
          style: buttonStyle,
          child: buttonChild,
        );
        break;
      case AppButtonVariant.text:
        button = TextButton(
          onPressed: _getOnPressed(),
          style: buttonStyle,
          child: buttonChild,
        );
        break;
      case AppButtonVariant.tonal:
        button = FilledButton.tonal(
          onPressed: _getOnPressed(),
          style: buttonStyle,
          child: buttonChild,
        );
        break;
    }

    if (width != null || height != null || margin != null) {
      button = Container(
        width: width,
        height: height,
        margin: margin,
        child: button,
      );
    }

    return button;
  }

  VoidCallback? _getOnPressed() {
    if (isDisabled || isLoading) {
      return null;
    }
    return onPressed;
  }

  Widget _buildChild(BuildContext context) {
    if (isLoading) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: _getIconSize(),
            height: _getIconSize(),
            child: loadingWidget ??
                CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    foregroundColor ?? Theme.of(context).colorScheme.onPrimary,
                  ),
                ),
          ),
          SizedBox(width: iconSpacing),
          child,
        ],
      );
    }

    return child;
  }

  ButtonStyle _getButtonStyle(BuildContext context, ColorScheme colorScheme) {
    final basePadding = _getPadding();
    final baseRadius =
        borderRadius ?? BorderRadius.circular(ThemeConstants.radiusMD);
    final baseElevation = elevation ?? _getElevation();

    return ButtonStyle(
      padding: WidgetStateProperty.all(basePadding),
      shape: WidgetStateProperty.all(
        RoundedRectangleBorder(borderRadius: baseRadius),
      ),
      elevation: WidgetStateProperty.resolveWith<double>((states) {
        if (states.contains(WidgetState.disabled)) {
          return 0;
        }
        if (states.contains(WidgetState.pressed)) {
          return baseElevation / 2;
        }
        return baseElevation;
      }),
      backgroundColor: _getBackgroundColor(colorScheme),
      foregroundColor: _getForegroundColor(colorScheme),
      side: _getBorderSide(colorScheme),
      textStyle: WidgetStateProperty.all(_getTextStyle(context)),
      minimumSize: WidgetStateProperty.all(_getMinimumSize()),
    );
  }

  EdgeInsets _getPadding() {
    if (padding != null) {
      return padding!;
    }

    switch (size) {
      case AppButtonSize.small:
        return const EdgeInsets.symmetric(
          horizontal: ThemeConstants.spaceMD,
          vertical: ThemeConstants.spaceXS,
        );
      case AppButtonSize.medium:
        return const EdgeInsets.symmetric(
          horizontal: ThemeConstants.spaceLG,
          vertical: ThemeConstants.spaceSM,
        );
      case AppButtonSize.large:
        return const EdgeInsets.symmetric(
          horizontal: ThemeConstants.spaceXL,
          vertical: ThemeConstants.spaceMD,
        );
    }
  }

  double _getElevation() {
    switch (variant) {
      case AppButtonVariant.filled:
        return 2;
      case AppButtonVariant.tonal:
        return 1;
      case AppButtonVariant.outlined:
      case AppButtonVariant.text:
        return 0;
    }
  }

  double _getIconSize() {
    switch (size) {
      case AppButtonSize.small:
        return 16;
      case AppButtonSize.medium:
        return 20;
      case AppButtonSize.large:
        return 24;
    }
  }

  Size _getMinimumSize() {
    switch (size) {
      case AppButtonSize.small:
        return const Size(64, 32);
      case AppButtonSize.medium:
        return const Size(88, 40);
      case AppButtonSize.large:
        return const Size(112, 48);
    }
  }

  TextStyle _getTextStyle(BuildContext context) {
    final baseStyle = Theme.of(context).textTheme.labelLarge!;

    switch (size) {
      case AppButtonSize.small:
        return baseStyle.copyWith(fontSize: ThemeConstants.fontSizeXS);
      case AppButtonSize.medium:
        return baseStyle.copyWith(fontSize: ThemeConstants.fontSizeSM);
      case AppButtonSize.large:
        return baseStyle.copyWith(fontSize: ThemeConstants.fontSizeMD);
    }
  }

  WidgetStateProperty<Color?> _getBackgroundColor(ColorScheme colorScheme) {
    return WidgetStateProperty.resolveWith<Color?>((states) {
      if (states.contains(WidgetState.disabled)) {
        return colorScheme.onSurface.withValues(alpha: 0.12);
      }

      final baseColor = backgroundColor ?? color ?? colorScheme.primary;

      if (states.contains(WidgetState.pressed)) {
        return baseColor.withValues(alpha: 0.8);
      }
      if (states.contains(WidgetState.hovered)) {
        return baseColor.withValues(alpha: 0.9);
      }

      switch (variant) {
        case AppButtonVariant.filled:
          return baseColor;
        case AppButtonVariant.tonal:
          return baseColor.withValues(alpha: 0.12);
        case AppButtonVariant.outlined:
        case AppButtonVariant.text:
          return Colors.transparent;
      }
    });
  }

  WidgetStateProperty<Color?> _getForegroundColor(ColorScheme colorScheme) {
    return WidgetStateProperty.resolveWith<Color?>((states) {
      if (states.contains(WidgetState.disabled)) {
        return colorScheme.onSurface.withValues(alpha: 0.38);
      }

      final baseColor = foregroundColor ?? color ?? colorScheme.primary;

      switch (variant) {
        case AppButtonVariant.filled:
          return colorScheme.onPrimary;
        case AppButtonVariant.tonal:
        case AppButtonVariant.outlined:
        case AppButtonVariant.text:
          return baseColor;
      }
    });
  }

  WidgetStateProperty<BorderSide?> _getBorderSide(ColorScheme colorScheme) {
    if (variant != AppButtonVariant.outlined) {
      return WidgetStateProperty.all(BorderSide.none);
    }

    return WidgetStateProperty.resolveWith<BorderSide?>((states) {
      if (states.contains(WidgetState.disabled)) {
        return BorderSide(color: colorScheme.onSurface.withValues(alpha: 0.12));
      }

      final baseColor = borderColor ?? color ?? colorScheme.outline;

      return BorderSide(color: baseColor);
    });
  }
}

/// 按钮变体枚举
enum AppButtonVariant {
  filled,
  outlined,
  text,
  tonal,
}

/// 按钮尺寸枚举
enum AppButtonSize {
  small,
  medium,
  large,
}

/// 图标按钮组件
class AppIconButton extends StatelessWidget {
  const AppIconButton({
    required this.onPressed,
    required this.icon,
    super.key,
    this.label,
    this.variant = AppButtonVariant.filled,
    this.size = AppButtonSize.medium,
    this.color,
    this.backgroundColor,
    this.foregroundColor,
    this.isLoading = false,
    this.isDisabled = false,
    this.tooltip,
  });

  final VoidCallback? onPressed;
  final Widget icon;
  final String? label;
  final AppButtonVariant variant;
  final AppButtonSize size;
  final Color? color;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final bool isLoading;
  final bool isDisabled;
  final String? tooltip;

  @override
  Widget build(BuildContext context) {
    Widget child;

    if (label != null) {
      child = Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          icon,
          const SizedBox(width: 8),
          Text(label!),
        ],
      );
    } else {
      child = icon;
    }

    Widget button = AppButton(
      onPressed: onPressed,
      variant: variant,
      size: size,
      color: color,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      isLoading: isLoading,
      isDisabled: isDisabled,
      child: child,
    );

    if (tooltip != null) {
      button = Tooltip(
        message: tooltip!,
        child: button,
      );
    }

    return button;
  }
}
