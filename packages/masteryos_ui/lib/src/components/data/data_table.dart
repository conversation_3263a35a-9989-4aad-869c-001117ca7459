import 'package:flutter/material.dart';

import '../feedback/empty_state.dart';
import '../feedback/loading_indicator.dart';

/// 应用数据表格组件
class AppDataTable<T> extends StatelessWidget {
  const AppDataTable({
    required this.columns,
    required this.rows,
    super.key,
    this.isLoading = false,
    this.isEmpty = false,
    this.emptyState,
    this.loadingWidget,
    this.onSort,
    this.sortColumnIndex,
    this.sortAscending = true,
    this.showCheckboxColumn = false,
    this.onSelectAll,
    this.selectedItems = const {},
    this.onItemSelected,
    this.horizontalMargin = 24.0,
    this.columnSpacing = 56.0,
    this.dataRowHeight = 52.0,
    this.headingRowHeight = 56.0,
    this.headingTextStyle,
    this.dataTextStyle,
    this.decoration,
    this.border,
    this.dividerThickness = 1.0,
  });

  /// 列定义
  final List<AppDataColumn> columns;

  /// 行数据
  final List<AppDataRow<T>> rows;

  /// 是否加载中
  final bool isLoading;

  /// 是否为空
  final bool isEmpty;

  /// 空状态组件
  final Widget? emptyState;

  /// 加载组件
  final Widget? loadingWidget;

  /// 排序回调
  final void Function(int columnIndex, bool ascending)? onSort;

  /// 当前排序列索引
  final int? sortColumnIndex;

  /// 是否升序
  final bool sortAscending;

  /// 是否显示复选框列
  final bool showCheckboxColumn;

  /// 全选回调
  final void Function(bool? selected)? onSelectAll;

  /// 已选择项目
  final Set<T> selectedItems;

  /// 项目选择回调
  final void Function(T item, bool selected)? onItemSelected;

  /// 水平边距
  final double horizontalMargin;

  /// 列间距
  final double columnSpacing;

  /// 数据行高度
  final double dataRowHeight;

  /// 标题行高度
  final double headingRowHeight;

  /// 标题文本样式
  final TextStyle? headingTextStyle;

  /// 数据文本样式
  final TextStyle? dataTextStyle;

  /// 装饰
  final Decoration? decoration;

  /// 边框
  final TableBorder? border;

  /// 分割线厚度
  final double dividerThickness;

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return Container(
        padding: const EdgeInsets.all(48),
        child: loadingWidget ??
            const LoadingIndicator(
              message: '加载中...',
            ),
      );
    }

    if (isEmpty || rows.isEmpty) {
      return emptyState ?? const NoDataState();
    }

    return Container(
      decoration: decoration,
      child: DataTable(
        columns: _buildDataColumns(context),
        rows: _buildDataRows(context),
        sortColumnIndex: sortColumnIndex,
        sortAscending: sortAscending,
        showCheckboxColumn: showCheckboxColumn,
        horizontalMargin: horizontalMargin,
        columnSpacing: columnSpacing,
        dataRowMinHeight: dataRowHeight,
        dataRowMaxHeight: dataRowHeight,
        headingRowHeight: headingRowHeight,
        border: border,
        dividerThickness: dividerThickness,
      ),
    );
  }

  List<DataColumn> _buildDataColumns(BuildContext context) {
    return columns.map((column) {
      return DataColumn(
        label: Expanded(
          child: Text(
            column.label,
            style: headingTextStyle ?? _getHeadingStyle(context),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        tooltip: column.tooltip,
        numeric: column.numeric,
        onSort: column.sortable && onSort != null
            ? (columnIndex, ascending) => onSort!(columnIndex, ascending)
            : null,
      );
    }).toList();
  }

  List<DataRow> _buildDataRows(BuildContext context) {
    return rows.map((row) {
      final isSelected = selectedItems.contains(row.data);

      return DataRow(
        cells: _buildDataCells(row, context),
        selected: isSelected,
        onSelectChanged: showCheckboxColumn && onItemSelected != null
            ? (selected) => onItemSelected!(row.data, selected ?? false)
            : null,
        color: row.color != null ? WidgetStateProperty.all(row.color) : null,
      );
    }).toList();
  }

  List<DataCell> _buildDataCells(AppDataRow<T> row, BuildContext context) {
    return row.cells.map((cell) {
      return DataCell(
        cell.child,
        showEditIcon: cell.showEditIcon,
        placeholder: cell.placeholder,
        onTap: cell.onTap,
      );
    }).toList();
  }

  TextStyle _getHeadingStyle(BuildContext context) {
    return Theme.of(context).textTheme.titleSmall!.copyWith(
          fontWeight: FontWeight.w600,
          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.8),
        );
  }
}

/// 数据列定义
class AppDataColumn {
  const AppDataColumn({
    required this.label,
    this.tooltip,
    this.numeric = false,
    this.sortable = false,
  });

  /// 列标签
  final String label;

  /// 工具提示
  final String? tooltip;

  /// 是否为数字列
  final bool numeric;

  /// 是否可排序
  final bool sortable;
}

/// 数据行定义
class AppDataRow<T> {
  const AppDataRow({
    required this.data,
    required this.cells,
    this.color,
  });

  /// 行数据
  final T data;

  /// 单元格列表
  final List<AppDataCell> cells;

  /// 行颜色
  final Color? color;
}

/// 数据单元格定义
class AppDataCell {
  const AppDataCell({
    required this.child,
    this.showEditIcon = false,
    this.placeholder = false,
    this.onTap,
  });

  /// 单元格内容
  final Widget child;

  /// 是否显示编辑图标
  final bool showEditIcon;

  /// 是否为占位符
  final bool placeholder;

  /// 点击回调
  final VoidCallback? onTap;
}

/// 分页数据表格
class PaginatedAppDataTable<T> extends StatelessWidget {
  const PaginatedAppDataTable({
    required this.columns,
    required this.source,
    super.key,
    this.header,
    this.actions,
    this.rowsPerPage = 10,
    this.availableRowsPerPage = const [10, 20, 50],
    this.onRowsPerPageChanged,
    this.initialFirstRowIndex = 0,
    this.onPageChanged,
    this.sortColumnIndex,
    this.sortAscending = true,
    this.onSort,
    this.showCheckboxColumn = false,
    this.showFirstLastButtons = true,
    this.horizontalMargin = 24.0,
    this.columnSpacing = 56.0,
    this.dataRowHeight = 52.0,
    this.headingRowHeight = 56.0,
    this.headingTextStyle,
    this.dataTextStyle,
  });

  /// 列定义
  final List<AppDataColumn> columns;

  /// 数据源
  final AppDataTableSource<T> source;

  /// 表格标题
  final Widget? header;

  /// 操作按钮
  final List<Widget>? actions;

  /// 每页行数
  final int rowsPerPage;

  /// 可选每页行数
  final List<int> availableRowsPerPage;

  /// 每页行数变化回调
  final ValueChanged<int?>? onRowsPerPageChanged;

  /// 初始第一行索引
  final int initialFirstRowIndex;

  /// 页面变化回调
  final void Function(int firstRowIndex)? onPageChanged;

  /// 排序列索引
  final int? sortColumnIndex;

  /// 是否升序
  final bool sortAscending;

  /// 排序回调
  final void Function(int columnIndex, bool ascending)? onSort;

  /// 是否显示复选框列
  final bool showCheckboxColumn;

  /// 是否显示首末页按钮
  final bool showFirstLastButtons;

  /// 水平边距
  final double horizontalMargin;

  /// 列间距
  final double columnSpacing;

  /// 数据行高度
  final double dataRowHeight;

  /// 标题行高度
  final double headingRowHeight;

  /// 标题文本样式
  final TextStyle? headingTextStyle;

  /// 数据文本样式
  final TextStyle? dataTextStyle;

  @override
  Widget build(BuildContext context) {
    return PaginatedDataTable(
      columns: _buildDataColumns(context),
      source: source,
      header: header,
      actions: actions,
      rowsPerPage: rowsPerPage,
      availableRowsPerPage: availableRowsPerPage,
      onRowsPerPageChanged: onRowsPerPageChanged,
      initialFirstRowIndex: initialFirstRowIndex,
      onPageChanged: onPageChanged,
      sortColumnIndex: sortColumnIndex,
      sortAscending: sortAscending,
      showCheckboxColumn: showCheckboxColumn,
      showFirstLastButtons: showFirstLastButtons,
      horizontalMargin: horizontalMargin,
      columnSpacing: columnSpacing,
      dataRowMinHeight: dataRowHeight,
      dataRowMaxHeight: dataRowHeight,
      headingRowHeight: headingRowHeight,
    );
  }

  List<DataColumn> _buildDataColumns(BuildContext context) {
    return columns.map((column) {
      return DataColumn(
        label: Expanded(
          child: Text(
            column.label,
            style: headingTextStyle ?? _getHeadingStyle(context),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        tooltip: column.tooltip,
        numeric: column.numeric,
        onSort: column.sortable && onSort != null
            ? (columnIndex, ascending) => onSort!(columnIndex, ascending)
            : null,
      );
    }).toList();
  }

  TextStyle _getHeadingStyle(BuildContext context) {
    return Theme.of(context).textTheme.titleSmall!.copyWith(
          fontWeight: FontWeight.w600,
          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.8),
        );
  }
}

/// 数据表格数据源
abstract class AppDataTableSource<T> extends DataTableSource {
  /// 获取数据项
  T getItem(int index);

  /// 构建行
  DataRow buildRow(T item, int index);

  @override
  DataRow getRow(int index) {
    final item = getItem(index);
    return buildRow(item, index);
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get selectedRowCount => 0;
}
